<template>
	<div class="model-detail">
		<el-container>
			<el-main>
				<el-row :gutter="24">
					<el-col :span="24">
						<el-radio-group
							class="single-btn-group"
							v-model="radio"
							size="large"
							@change="radioChange"
						>
							<el-radio-button
								v-for="option in radioOptions"
								:key="option.value"
								:label="option.label"
								:value="option.value"
								:class="getButtonClass(option.value)"
							/>
						</el-radio-group>
					</el-col>
				</el-row>
				<el-row :gutter="24" style="margin-top: 15px;">
					<el-col :span="24">
						<el-card class="model-card">
							<template #header>
								<div class="flex items-center">
									<img
										:src="getAssetsFile('risk/base_info.png')"
										alt="基本信息"
										class="header-tip-img"
									/>
									<span class="header-title">基本信息</span>
								</div>
							</template>

							<el-row :gutter="24">
								<el-col :span="12">
									<span class="label">模型编号：</span>
									<span class="value">{{ dataForm.modelNo }}</span>
								</el-col>
								<el-col :span="12">
									<span class="label">业务归属：</span>
									<span class="value">{{ dataForm.BusinessAttr }}</span>
								</el-col>
							</el-row>

							<el-row :gutter="24">
								<el-col :span="12">
									<span class="label">模型名称：</span>
									<span class="value">{{ dataForm.modelName }}</span>
								</el-col>
								<el-col :span="12">
									<span class="label">应用开始时间：</span>
									<span class="value">{{ dataForm.startTime }}</span>
								</el-col>
							</el-row>
						</el-card>
					</el-col>
				</el-row>

				<el-row :gutter="24" style="margin-top: 15px;">
					<el-col :span="24">
						<el-card class="model-card">
							<template #header>
								<div class="flex items-center">
									<img
										style="width: 25px;"
										:src="getAssetsFile('risk/double_setting.png')"
										alt="主要预警功能"
										class="header-tip-img"
									/>
									<span class="header-title">主要预警功能</span>
								</div>
							</template>

							<div class="function-tip">
								{{ dataForm.funcTag }}
							</div>
						</el-card>
					</el-col>
				</el-row>

				<el-row :gutter="24" style="margin-top: 15px;">
					<el-col :span="24">
						<el-card class="model-card">
							<template #header>
								<div class="flex items-center">
									<img
										:src="getAssetsFile('risk/db.png')"
										alt="监控模型数据字段 "
										class="header-tip-img"
									/>
									<span class="header-title">监控模型数据字段 </span>
								</div>
							</template>
							<el-table
								:data="dataForm.fieldTableData"
								:header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
							>
								<el-table-column type="index" label="序号" width="55"/>
								<el-table-column prop="fieldName" label="字段名称" align="center"/>
								<el-table-column prop="dataSourceSystem" label="数据来源系统" align="center"/>
								<el-table-column prop="remark" label="备注" align="center"/>
							</el-table>
						</el-card>
					</el-col>
				</el-row>

				<el-row :gutter="24" style="margin-top: 15px;">
					<el-col :span="24">
						<el-card class="model-card">
							<template #header>
								<div class="flex items-center">
									<!--<img
										:src="getAssetsFile('risk/setting.png')"
										alt="模型判断规则"
										class="header-tip-img"
									/>-->
									<span class="header-title">模型判断规则</span>
								</div>
							</template>

							<div class="function-tip">
								{{ dataForm.ruleTag }}
							</div>
						</el-card>
					</el-col>
				</el-row>

				<el-row :gutter="24" style="margin-top: 15px;">
					<el-col :span="24">
						<el-card class="model-card">
							<template #header>
								<div class="flex items-center">
									<img
										:src="getAssetsFile('risk/safe.png')"
										alt="风险等级识别规则 "
										class="header-tip-img"
									/>
									<span class="header-title">风险等级识别规则 </span>
								</div>
							</template>
							<el-table
								:data="dataForm.ruleTableData"
								:header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
							>
								<el-table-column prop="warningLevel" label="预警级别" align="center" width="140"/>
								<el-table-column prop="warningObject" label="被预警对象" align="center" width="180"/>
								<el-table-column prop="judgmentRule" label="预警等级判断规则" align="left">
									<template #default="scope">
										<div v-html="scope.row.judgmentRule"></div>
									</template>
								</el-table-column>
								<el-table-column prop="disposalMechanism" label="处置机制" align="center" width="210"/>
							</el-table>
						</el-card>
					</el-col>
				</el-row>

			</el-main>
		</el-container>
	</div>
</template>

<script setup lang="ts">

import {LocationQuery} from "vue-router";
import {getAssetsFile} from "@/utils/commonUtils";

const route = useRoute()
const query: LocationQuery = route.query;

const modelType = ref(query.modelType);

const radio = ref<number>(
	modelType.value != null ? (isNaN(Number(modelType.value)) ? 1 : Number(modelType.value)) : 1
)

const radioOptions = [
	{label: "ICT-ABBA", value: 1},
	{label: "ICT-上下游关联", value: 2},
	{label: "移动云-组合订购不合理", value: 3},
	{label: "移动云-收入异动", value: 4}
];

const map = new Map<number, any>([
	[1, {
		modelNo: 'M-001',
		BusinessAttr: 'ICT业务',
		modelName: 'ICT项目上游客户与下游供应商存在AB/BA关系',
		startTime: '2025-05',
		funcTag: '针对本月新签约后向供应商的ICT项目，获取上游客户及下游供应商信息，调用企业致信接口数据查询，如果上游客户与下游供应商存在关联关系，触发预警工单。',
		fieldTableData: [
			{
				fieldName: '上游客户名称',
				dataSourceSystem: 'DICT系统',
				remark: '数据频次： 每天'
			},
			{
				fieldName: '下游客户名称',
				dataSourceSystem: 'DICT系统',
				remark: '数据频次： 每天'
			},
		],
		ruleTag: '项目X，客户名称是X1，后向供应商是X2；项目Y，客户名称是Y1，后向供应商是Y2。如果X1=Y2且X2=Y1，则判定项目X和项目Y存在AB/BA关系（已在业管平台做模型规则判断），再根据前后项目金额差值设定阈值触发风险工单。',
		ruleTableData: [
			{
				warningLevel: "一级",
				warningObject: "对应一级预警领导",
				judgmentRule: `
					【单项目维度】<br/>
					(1) 预警周期：每月8号<br/>
					(2) 逻辑关系：发现项目组存在AB/BA关系后，只要其中一个项目的IT预算金额≥100万且两个项目的IT预算金额差值≤0%，则派发一级预警<br/>
					(3) 统计维度：以省份为维度统计<br/>
					(4) 工单派发：由省公司接口人进行工单派发，选择派发至省公司或地市公司。<br/>
				`,
				disposalMechanism: "一级风险处置"
			},
			{
				warningLevel: "一级",
				warningObject: "对应一级预警领导",
				judgmentRule: `
					【项目数量统计维度】<br/>
					(1) 预警周期：每月8号<br/>
					(2) 逻辑关系：针对存在AB/BA关系且两个项目的IT预算金额差值≤30%的风险项目组，当月全省总触发量超过5个，则派发一级预警<br/>
					(3) 统计维度：以省份为维度统计<br/>
					(4) 工单派发：由省公司接口人进行工单派发，选择派发至省公司或地市公司。
				`,
				disposalMechanism: "一级风险处置"
			},
			{
				warningLevel: "二级",
				warningObject: "对应二级预警领导",
				judgmentRule: `
					【单项目维度】<br/>
					(1) 预警周期：每月8号<br/>
					(2) 逻辑关系：发现项目组存在AB/BA关系后，只要其中一个项目的IT预算金额≥50万且两个项目的IT预算金额差值≤%，则派发二级预警（如已派发一级预警则无需派发二级预警）<br/>
					(3) 统计维度：以省份为维度统计<br/>
					(4)工单派发：由省公司接口人进行工单派发，选择派发至省公司或地市公司。
				`,
				disposalMechanism: "二级风险处置"
			},
			{
				warningLevel: "二级",
				warningObject: "对应二级预警领导",
				judgmentRule: `
					【项目数量统计维度】<br/>
					(1) 预警周期：每月8号<br/>
					(2) 逻辑关系：针对存在AB/BA关系且两个项目的IT预算金额差值≤30%的风险项目组，当月单个地市总触发量超过3个，则派发二级预警（如已派发一级预警则无需派发二级预警）<br/>
					(3) 统计维度：以地市为维度统计<br/>
					(4) 工单派发：由地市公司接口人进行工单派发和处理。
				`,
				disposalMechanism: "二级风险处置"
			},
			{
				warningLevel: "三级",
				warningObject: "对应三级预警领导",
				judgmentRule: `
					【单项目维度】<br/>
					（1）预警周期：每月8号<br/>
					（2）逻辑关系：发现项目组存在AB/BA关系且两个项目的IT预算金额差值≤30%后派发三级预警（如已派发一级、二级预警则无需派发三级预警单）<br/>
					（3）统计维度：以省份为维度统计<br/>
					（4）工单派发：由省公司接口人进行工单派发，选择派发至省公司或地市公司。`,
				disposalMechanism: "三级风险处置"
			}
		]
	}],
	[2, {
		modelNo: 'M-002',
		BusinessAttr: 'ICT业务',
		modelName: 'ICT项目上下游单位存在关联关系',
		startTime: '2025-05',
		funcTag: '针对本月新签约后向供应商的ICT项目，获取上游客户及下游供应商信息，调用企业致信接口数据查询，如果上游客户与下游供应商存在关联关系，触发预警工单。',
		fieldTableData: [
			{
				fieldName: "企业对外投资",
				dataSourceSystem: "企业致信系统接口",
				remark: "关系类型R101；股权占比超50%"
			},
			{
				fieldName: "企业股东",
				dataSourceSystem: "企业致信系统接口",
				remark: "关系类型R102；股权占比超50%"
			},
			{
				fieldName: "自然人企业任职",
				dataSourceSystem: "企业致信系统接口",
				remark: "关系类型R103；股权占比超50%"
			},
			{
				fieldName: "自然人股东",
				dataSourceSystem: "企业致信系统接口",
				remark: "关系类型R104；股权占比超50%"
			},
			{
				fieldName: "管理人员公司任职",
				dataSourceSystem: "企业致信系统接口",
				remark: "关系类型R105；只看合同签约期间，不看历史情况"
			},
			{
				fieldName: "公司管理人员",
				dataSourceSystem: "企业致信系统接口",
				remark: "关系类型R106；只看合同签约期间，不看历史情况"
			},
			{
				fieldName: "分支机构",
				dataSourceSystem: "企业致信系统接口",
				remark: "/"
			},
			{
				fieldName: "总部",
				dataSourceSystem: "企业致信系统接口",
				remark: "/"
			},
		],
		ruleTag: '调用企业致信系统企业关联数据实时查询，如果上游客户与下游供应商存在上述表格中的关系，即认定为存在关联关系。（已在业管平台做模型规则判断，判断规则有差异）。',
		ruleTableData: [
			{
				warningLevel: "一级",
				warningObject: "对应一级预警领导",
				judgmentRule: `
			【单项目维度】<br/>
			（1）预警周期：每月8号<br/>
			（2）逻辑关系：如果项目上下游存在关联关系，且IT预算金额≥300万，则派发一级预警<br/>
			（3）统计维度：以省份为维度统计<br/>
			（4）工单派发：由省公司接口人进行工单派发，选择派发至省公司或地市公司。`,
				disposalMechanism: "一级风险处置"
			},
			{
				warningLevel: "一级",
				warningObject: "对应一级预警领导",
				judgmentRule: `
			【项目数量统计维度】<br/>
			（1）预警周期：每月8号<br/>
			（2）逻辑关系：针对存在上下游关系的风险项目，当月全省总触发量超过5个，则派发一级预警<br/>
			（3）统计维度：以省份为维度统计<br/>
			（4）工单派发：由省公司接口人进行工单派发，选择派发至省公司或地市公司。`,
				disposalMechanism: "一级风险处置"
			},
			{
				warningLevel: "二级",
				warningObject: "对应二级预警领导",
				judgmentRule: `
			【单项目维度】<br/>
			（1）预警周期：每月8号<br/>
			（2）逻辑关系：如果项目上下游存在关联关系，且IT预算金额≥100万，则派发二级预警（如已派发一级预警则无需派发二级预警）<br/>
			（3）统计维度：以省份为维度统计<br/>
			（4）工单派发：由省公司接口人进行工单派发，选择派发至省公司或地市公司。`,
				disposalMechanism: "二级风险处置"
			},
			{
				warningLevel: "二级",
				warningObject: "对应二级预警领导",
				judgmentRule: `
			【项目数量统计维度】<br/>
			（1）预警周期：每月8号<br/>
			（2）逻辑关系：针对存在上下游关系的风险项目，当月单个地市总触发量超过3个，则派发二级预警（如已派发一级预警则无需派发二级预警）<br/>
			（3）统计维度：以地市为维度统计<br/>
			（4）工单派发：由地市公司接口人进行工单派发和处理。`,
				disposalMechanism: "二级风险处置"
			},
			{
				warningLevel: "三级",
				warningObject: "对应三级预警领导",
				judgmentRule: `
			【单项目维度】<br/>
			（1）预警周期：每月8号<br/>
			（2）逻辑关系：发现项目组存在AB/BA关系且两个项目的IT预算金额差值≤30%后派发三级预警（如已派发一级、二级预警则无需派发三级预警单）<br/>
			注意：此条规则为根据问题要求新增，但已按照问题中的描述进行添加。<br/>
			（3）统计维度：以省份为维度统计<br/>
			（4）工单派发：由省公司接口人进行工单派发，选择派发至省公司或地市公司。`,

			}
		],
	}],
	[3, {
		modelNo: 'M-003',
		BusinessAttr: '移动云业务',
		modelName: '移动云订购云硬盘逻辑不合理',
		startTime: '2025-05',
		funcTag: '识别移动云客户订购云硬盘但是未订购云主机等依赖产品情况',
		fieldTableData: [
			{
				fieldName: "省份名称",
				dataSourceSystem: "移动云经分系统",
				remark: "数据频次：每天"
			},
			{
				fieldName: "地市名称",
				dataSourceSystem: "移动云经分系统",
				remark: "数据频次：每天"
			},
			{
				fieldName: "区县名称",
				dataSourceSystem: "移动云经分系统",
				remark: "数据频次：每天"
			},
			{
				fieldName: "客户编码",
				dataSourceSystem: "移动云经分系统",
				remark: "数据频次：每天"
			},
			{
				fieldName: "省客户编码",
				dataSourceSystem: "移动云经分系统",
				remark: "数据频次：每天"
			},
			{
				fieldName: "集团客户编号",
				dataSourceSystem: "移动云经分系统",
				remark: "数据频次：每天"
			},
			{
				fieldName: "客户名称",
				dataSourceSystem: "移动云经分系统",
				remark: "数据频次：每天"
			},
			{
				fieldName: "异常大类",
				dataSourceSystem: "移动云经分系统",
				remark: "数据频次：每天"
			},
			{
				fieldName: "异常类型",
				dataSourceSystem: "移动云经分系统",
				remark: "数据频次：每天"
			},
			{
				fieldName: "具体原因",
				dataSourceSystem: "移动云经分系统",
				remark: "数据频次：每天"
			},
			{
				fieldName: "订单流水号",
				dataSourceSystem: "移动云经分系统",
				remark: "数据频次：每天"
			}
		],
		ruleTag: '移动云订购云硬盘异常规则为“客户有效大云订单中，订购云硬盘，同资源池下未订购云主机、裸金属服务器、专属宿主机、日志审计云主机、容器服务、弹性伸缩”；合营云订购云硬盘异常“客户有效和云订单中，订购云硬盘，同资源池下未订购云主机、裸金属服务器、云容器引擎CCE、专属计算集群”。',
		ruleTableData: [
			{
				warningLevel: "一级",
				warningObject: "对应一级预警领导",
				judgmentRule: `【单项目维度】<br/> （1）预警周期：每月8号<br/> （2）逻辑关系：按照“生效时间”识别“上月”新增数据、派发增量数据<br/> （3）一级风险阈值：省份新增订单流水数量超100条时（订单流水号）<br/> （4）工单派发：由省公司接口人进行工单派发，`,
				disposalMechanism: "一级风险处置"
			},
			{
				warningLevel: "二级",
				warningObject: "对应二级预警领导",
				judgmentRule: `【单项目维度】<br/> （1）预警周期：每月8号 <br/> （2）逻辑关系：按照“生效时间”识别“上月”新增数据、派发增量数据 <br/> （3）二级风险阈值：单个地市新增订单流水数量超20条时（订单流水号）且未在一级预警中 <br/> （4）工单派发：由省公司接口人进行工单派发，选择派发至省公司或地市公司，对于“其他地市”的数据仅派发单省公司政企部门科室负责人处`,
				disposalMechanism:
					"二级风险处置",
			},
			{
				warningLevel: "三级",
				warningObject:
					"对应三级预警领导",
				judgmentRule: `【单项目维度】<br/> （1）预警周期：每月8号 <br/> （2）逻辑关系：按照“生效时间”识别“上月”新增数据、派发增量数据 <br/>（3）三级风险阈值：单个区县新增订单流水数量超5条时（订单流水号）且未在一、二级预警中 <br/> （4）工单派发：由省公司接口人进行工单派发，选择派发至省公司或地市公司或区县公司，对于“其他区县”的数据仅派发单地市公司政企部门所属室负责人处`,
				disposalMechanism: "三级风险处置"
			}
		]
	}],
	[4, {
		modelNo: 'M-004',
		BusinessAttr: '移动云业务',
		modelName: '移动云合作类业务收入异动',
		startTime: '2025-05',
		funcTag: '识别直管公有云中合作类业务产品月收入异常波动的情况。',
		fieldTableData: [
			{
				fieldName: "省份名称",
				dataSourceSystem: "移动云经分系统",
				remark: "数据频次：每天"
			},
			{
				fieldName: "地市名称",
				dataSourceSystem: "移动云经分系统",
				remark: "数据频次：每天"
			},
			{
				fieldName: "区县名称",
				dataSourceSystem: "移动云经分系统",
				remark: "数据频次：每天"
			},
			{
				fieldName: "客户编码",
				dataSourceSystem: "移动云经分系统",
				remark: "数据频次：每天"
			},
			{
				fieldName: "省客户编码",
				dataSourceSystem: "移动云经分系统",
				remark: "数据频次：每天"
			},
			{
				fieldName: "集团客户编号",
				dataSourceSystem: "移动云经分系统",
				remark: "数据频次：每天"
			},
			{
				fieldName: "客户名称",
				dataSourceSystem: "移动云经分系统",
				remark: "数据频次：每天"
			},
			{
				fieldName: "异常大类",
				dataSourceSystem: "移动云经分系统",
				remark: "数据频次：每天"
			},
			{
				fieldName: "异常类型",
				dataSourceSystem: "移动云经分系统",
				remark: "数据频次：每天"
			},
			{
				fieldName: "具体原因",
				dataSourceSystem: "移动云经分系统",
				remark: "数据频次：每天"
			},
			{
				fieldName: "订单流水号",
				dataSourceSystem: "移动云经分系统",
				remark: "数据频次：每天"
			}
		],
		ruleTag: '1.产品范围：合营云、开放云市场、合作类I+P的合作类业务产品。不包含开放云市场“通用SaaS产品”、合营云“全栈专属云”。\n' +
			'\n' +
			'2.分省统计收入异动的合作类业务产品（四级产品编码）：单产品当月出账金额环比波动超50%且出账总额超30万，或当月出账增长金额超30万。',
		ruleTableData: [
			{
				warningLevel: "一级",
				warningObject: "对应一级预警领导",
				judgmentRule: `
            【单项目维度】<br/>
            （1）预警周期：每月8号<br/>
            （2）逻辑关系：本身为月识别数据，直接对当月接收的数据派发<br/>
            （3）一级预警阈值：省份总的“实收金额（含税）”金额大于等于2000万<br/>
            （4）工单派发：由省公司接口人进行工单派发，选择派发至省公司
        `,
				disposalMechanism: "一级风险处置"
			},
			{
				warningLevel: "二级",
				warningObject: "对应二级预警领导",
				judgmentRule: `
            【单项目维度】<br/>
            （1）预警周期：每月8号<br/>
            （2）逻辑关系：本身为月识别数据，直接对当月接收的数据派发，按照省份字段+地市字段进行拆分<br/>
            （3）二级预警阈值：单个地市总的“实收金额（含税）”异动金额大于等于500万，且未在一级预警中<br/>
            （4）工单派发：由省公司接口人进行工单派发，选择派发至省公司或地市公司，对于“其他地市”的数据仅派发至省公司政企部门科室负责人处
        `,
				disposalMechanism: "二级风险处置"
			},
			{
				warningLevel: "三级",
				warningObject: "对应三级预警领导",
				judgmentRule: `
            【单项目维度】<br/>
            （1）预警周期：每月8号<br/>
            （2）逻辑关系：本身为月识别数据，直接对当月接收的数据派发<br/>
            （3）三级预警阈值：单个区县总的“实收金额（含税）”异动金额大于等于100万，且未在一、二级预警中<br/>
            （4）工单派发：由省公司接口人进行工单派发，选择派发至省公司、地市公司或区县公司，对于“其他区县”的数据仅派发至地市公司政企部门所属室负责人处
        `,
				disposalMechanism: "三级风险处置"
			}
		],
	}],
])

const dataForm = ref()
// 页面初始化时赋值
if (radio.value) {
	dataForm.value = map.get(radio.value)
}

const getButtonClass = (value: number) => {
	const current = radio.value
	const isActive = current === value
	const isPrev = current === value + 1
	const isNext = current === value - 1

	return [
		{active: isActive},
		{'prev-active': isPrev},
		{'next-active': isNext},
		`button-${value}`
	]
}

const radioChange = (value: number) => {
	if (value) {
		dataForm.value = map.get(value)
	}
}

</script>

<style scoped lang="scss">
.model-detail {
	.single-btn-group {
		--radio-active-bg-color: #ffffff;
		--radio-unactive-bg-color: #e9f0fc;

		:deep(.el-radio-button__inner) {
			border: none;
			background-color: var(--radio-unactive-bg-color);
			color: #000000;
			box-shadow: -1px 0 0 0 var(--radio-unactive-bg-color);
			position: relative;
			z-index: 99;
			transition: all 0.3s ease;
		}

		:deep(.el-radio-button.is-active .el-radio-button__inner) {
			background-color: var(--radio-active-bg-color);
			box-shadow: -1px 0 0 0 var(--radio-active-bg-color);
		}

		@mixin active-button($radius) {
			:deep(.el-radio-button__inner) {
				color: #000000;
				border-radius: $radius;
			}
		}

		@mixin pseudo-element($position, $size, $color) {
			&::#{$position} {
				content: '';
				position: absolute;
				width: $size;
				height: $size;
				background-color: $color;
				z-index: 98;
				@content;
			}
		}

		.active {
			@include pseudo-element('after', 38px, var(--radio-unactive-bg-color)) {
				right: 0;
				top: 0;
			}

			&.button-1 {
				@include active-button(8px 20px 0 8px);
			}

			&.button-2 {
				@include active-button(20px 20px 0 0);
				@include pseudo-element('before', 38px, var(--radio-unactive-bg-color)) {
					left: 0;
					top: 0;
				}
			}

			&.button-3 {
				@include active-button(20px 20px 0 0);
				@include pseudo-element('before', 38px, var(--radio-unactive-bg-color)) {
					left: 0;
					top: 0;
				}
			}

			&.button-4 {
				@include active-button(20px 8px 8px 0);
				@include pseudo-element('before', 38px, var(--radio-unactive-bg-color)) {
					left: 0;
					top: 0;
				}
			}
		}

		.prev-active {
			:deep(.el-radio-button__inner) {
				border-radius: 0 0 20px 0;
			}

			@include pseudo-element('after', 38px, var(--radio-active-bg-color)) {
				right: 0;
				top: 0;
			}
		}

		.next-active {
			:deep(.el-radio-button__inner) {
				border-radius: 0 0 0 20px;
			}

			@include pseudo-element('before', 38px, var(--radio-active-bg-color)) {
				left: 0;
				top: 0;
			}
		}
	}

	.model-card {
		opacity: 1;
		border-radius: 12px;
		background: #FFFFFF;
		box-shadow: 0 1px 2px 0 #0000000C;

		.header-tip-img {
			width: 20px;
			height: 20px;
		}

		.header-title {
			opacity: 1;
			color: #585858;
			font-family: PingFang SC;
			font-weight: 500;
			font-size: 20px;
			line-height: 28px;
			letter-spacing: 0;
			text-align: left;
			margin-left: 5px;
		}

		:deep(.el-card__header) {
			padding-bottom: 0;
			border-bottom: 0;
		}

		.label {

			opacity: 1;
			color: #6B7280;
			font-family: PingFang SC;
			font-weight: 400;
			font-size: 16px;
			line-height: 24px;
			letter-spacing: 0;
			text-align: left;
		}

		.value {
			opacity: 1;
			color: #353535;
			font-family: PingFang SC;
			font-weight: 400;
			font-size: 16px;
			line-height: 24px;
			letter-spacing: 0;
			text-align: left;
			margin-left: 15px;
		}

		.function-tip {
			opacity: 1;
			color: #4B5563;
			font-family: PingFang SC;
			font-weight: 400;
			font-size: 16px;
			line-height: 26px;
			letter-spacing: 0;
			text-align: left;
		}
	}
}

</style>
