<template>
	<div class="analysis-report">
		<el-container>
			<el-header class="header">
				<el-form :model="queryForm" ref="queryFormRef" label-width="110">
					<el-row :gutter="24" style="margin-top: 15px;">
						<el-col :span="8">
              <div class="title">
                <img class="logo" :src="getAssetsFile('risk/report/riskTitle.png')" alt="logo"/>
                综合分析评估报告
              </div>
						</el-col>
						<el-col :span="4" :offset="8">
							<el-form-item label="时间：" prop="riskMonth">
								<el-select
									v-model="queryForm.riskMonth"
									placeholder="请选择"
									class="risk-month"
									:disabled="false"
									:popper-append-to-body="false"
								>
									<el-option
										v-for="item in dateOptions"
										:key="item.value"
										:label="item.label"
										:value="item.value"
										:disabled="item.disabled"
									/>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="4">
							<el-form-item label="省份：" prop="region">
								<el-select
									v-model="queryForm.region"
									placeholder="请选择"
									clearable
									:disabled="false"
									@change="regionChange"
                  @clear="clearRegion"
								>
									<el-option
										v-for="option in regionOptions"
										:key="option.value"
										:label="option.label"
										:value="option.value"
									/>
								</el-select>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
			</el-header>
			<el-container>
				<el-aside width="200px">
					<div class="aside-menu">
						<div
							v-for="(item, index) in menuItems"
							:key="index"
							class="menu-item"
							@click="handleMenuItemClick(index)"
						>
              <img class="sideLogo" :src="getAssetsFile(item.url)" alt="logo"/>
							{{ item.title }}
						</div>
					</div>
				</el-aside>
				<el-container>
					<el-main>
						<div v-for="(item,index) in pictureDefaultOptions">
						<el-row :gutter="24">
								<el-col :span="24">
									<div :id="'subtitle-' + index" class="sub-title anchor-point">{{ item.subTitle }}</div>
                </el-col>
							</el-row>
							<!--<el-divider/>-->
							<img
								:src="getAssetsFile(item.url)"
								:alt="item.subTitle"
								class="part-pic"
							/>
						</div>

					</el-main>
				</el-container>
			</el-container>
		</el-container>
	</div>

</template>

<script setup lang="ts">

import {getAssetsFile} from "@/utils/commonUtils";

const queryForm = ref<any>({
	riskMonth: '2025-06',
	region: '全国',
});

const regionOptions = ref<Array<any>>([])
regionOptions.value = [
  {
    value: '全国',
    label: '全国'
  },
  {
    value: '江苏',
    label: '江苏'
  }
]
//清除选中后
const clearRegion = (value:any) => {
  queryForm.value.region = regionOptions.value[0].value
  regionChange(regionOptions.value[0].value)
}

const regionChange = (value: any) => {
  console.log("=======",value)
  if (value === regionOptions.value[0].value) {
    //全国的
    pictureDefaultOptions.value = pictureNationalOptions.value
  }else {
    pictureDefaultOptions.value = pictureProvinceOptions.value
  }
}

const menuItems = ref([
	{ title: '风险综合评估',url: 'risk/report/side01.png', },
	{ title: '风险整改情况',url: 'risk/report/side02.png', },
	{ title: '业务维度分析',url: 'risk/report/side03.png', },
	{ title: '场景维度分析',url: 'risk/report/side04.png', },
]);

const activeIndex = ref(0);

const handleMenuItemClick = async (index: number) => {
	activeIndex.value = index;
	await nextTick(); // 等待 DOM 更新
	
	setTimeout(() => {
		const element = document.getElementById(`subtitle-${index}`);
		if (element) {
			element.scrollIntoView({ behavior: 'smooth' });
		}
	}, 300); // 添加300ms延迟
};


const dateOptions = ref<Array<any>>([
	{
		label: '2025年6月',
		value: '2025-06',
	},
	{
		label: '2025年5月',
		value: '2025-05',
	},
	{
		label: '2025年4月',
		value: '2025-04',
	},
	{
		label: '2025年3月',
		value: '2025-03',
	},
])

const pictureDefaultOptions = ref<Array<any>>([
	{
		subTitle: '一、风险综合评估',
		url: 'risk/report/pic-national01.png',
	},
	{
		subTitle: '二、风险整改情况',
		url: 'risk/report/pic-national02.png',
	},
	{
		subTitle: '三、业务维度分析',
		url: 'risk/report/pic-national03.png',
	},
	{
		subTitle: '四、场景维度分析',
		url: 'risk/report/pic-national04.png',
	},
])

const pictureProvinceOptions = ref<Array<any>>([
  {
    subTitle: '一、风险综合评估',
    url: 'risk/report/pic01.png',
  },
  {
    subTitle: '二、风险整改情况',
    url: 'risk/report/pic02.png',
  },
  {
    subTitle: '三、业务维度分析',
    url: 'risk/report/pic03.png',
  },
  {
    subTitle: '四、场景维度分析',
    url: 'risk/report/pic04.png',
  },
])

const pictureNationalOptions = ref<Array<any>>([
  {
    subTitle: '一、风险综合评估',
    url: 'risk/report/pic-national01.png',
  },
  {
    subTitle: '二、风险整改情况',
    url: 'risk/report/pic-national02.png',
  },
  {
    subTitle: '三、业务维度分析',
    url: 'risk/report/pic-national03.png',
  },
  {
    subTitle: '四、场景维度分析',
    url: 'risk/report/pic-national04.png',
  },
])

</script>


<style scoped lang="scss">
.analysis-report {
  .anchor-point {
    position: absolute;
    visibility: hidden;
    pointer-events: none;
    height: 0;
    margin: 0;
    padding: 0;
  }
	:deep(.el-aside) {
		position: relative; // 建立定位上下文
		height: calc(100vh - 75px); // 与固定面板高度一致
	}

  .header {
    opacity: 1;
    border-radius: 0px;
    background: #FFFFFF;
  }
  .title {
    display: flex;
    align-items: center;
    opacity: 1;
    font-size: 20px;
    color: #000000;
    line-height: normal;
    letter-spacing: 0px;
    text-align: left;

    .logo {
      margin-right: 10px;
    }
  }
	.sub-title {
		font-size: 36px;
		color: #000000;
		font-weight: bold;
	}

	.part-pic {
		width: 100%;
	}

	.aside-menu {

		position: fixed;
		left: 0;
		top: 130px;  // 与头部高度保持一致
		width: 200px;  // 与侧边栏宽度一致
		height: 100%; // 全屏高度减去头部
		overflow-y: auto; // 菜单过长时显示滚动条
		background: white;
		z-index: 1000;
		box-shadow: 2px 0 8px rgba(0,0,0,0.1); // 可选右侧阴影


		.menu-item {
      display: flex;
      align-items: center; // 垂直居中
      line-height: 40px;
			padding: 10px;
			cursor: pointer;
			font-size: 16px;
			color: #4E5969;
			border-left: 4px solid transparent; /* 默认边框颜色为透明 */
			transition: background-color 0.3s, border-color 0.3s;

			&:hover,
			&.active {
				//background-color: #6B77D6;
				border-left-color: #409eff; /* 激活或悬停时的边框颜色 */
				color: #165DFF;
			}
      .sideLogo {
        margin-right: 5px;
      }
		}

		.menu-item.active {
			font-weight: bold;
			background-color: #6B77D6; /* 紫色背景 */
		}
	}


}

</style>
