<template>
	<div class="h-full">
		<el-card class="h-full">
			<template #header>
				<div class="flex items-center justify-center">
					<el-button link @click="toLink" type="primary"><span class="text-base"> {{ llmName }} </span></el-button>
				</div>
			</template>
			<div class="h-full">
				<span
					class="text-sm text-[var(--el-text-color-secondary)]"
				>
					{{ llmDesc }}
			</span>
			</div>
		</el-card>
	</div>
</template>

<script setup lang="ts">
const props = defineProps({
	llmName: {
		type: String,
		required: true,
		default: ''
	},
	llmDesc: {
		type: String,
		required: true,
		default: ''
	},
	llmLink: {
		type: String,
		required: true,
		default: '默认标题'
	},
});

const toLink = () => {
	window.open(props.llmLink, '_blank')
}

</script>

<style scoped lang="scss">


</style>