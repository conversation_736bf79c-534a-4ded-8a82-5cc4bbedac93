<template>
  <section class="app-main">
    <div class="body-container">
    <router-view>
      <template #default="{ Component, route }">
        <transition
          enter-active-class="animate__animated animate__fadeIn"
          mode="out-in"
        >
          <keep-alive :include="cachedViews">
            <component :is="Component" :key="route.path" />
          </keep-alive>
        </transition>
      </template>
    </router-view>
    </div>
    <div class="foot-container">
      <el-row class="mt-3" justify="center"><span class="color-#D2D2D2" style="font-size: 12px;">Copyright © 中国移动, All Rights Reserved.</span></el-row>
<!--      <el-row class="mt-3" justify="center"><span class="color-#D2D2D2" style="font-size: 12px;">服务热线 400-000-0000</span></el-row>-->
<!--      <el-row class="mt-2" justify="center"><span class="color-#D2D2D2" style="font-size: 12px;">服务邮箱 xx@xx</span></el-row>-->
    </div>
  </section>
</template>

<script setup lang="ts">
import { useTagsViewStore } from "@/store";

const cachedViews = computed(() => useTagsViewStore().cachedViews); // 缓存页面集合
</script>

<style lang="scss" scoped>
.app-main {
  position: relative;
  width: 100%;
  min-height: 100vh;
  overflow: hidden;
  background-color: var(--el-bg-color-page);
}

.hasTagsView .app-main {
  min-height: calc(100vh - $tags-view-height);
}

.fixed-header + .app-main {
  min-height: calc(100vh - $navbar-height);
}

.hasTagsView .fixed-header + .app-main {
  min-height: calc(100vh - $navbar-height - $tags-view-height);
}

.layout-mix,
.layout-top {
  .fixed-header + .app-main {
    min-height: calc(100vh - $navbar-height);
  }
}

.layout-mix {
  .app-main {
    height: calc(100vh);
    padding-top: $navbar-height;
    overflow-y: auto;
  }

  .hasTagsView .app-main {
    height: calc(100vh - $navbar-height - $tags-view-height);
    min-height: calc(100vh - $navbar-height - $tags-view-height);
  }

  .fixed-header + .app-main {
    min-height: calc(100vh - $navbar-height);
  }

  .hasTagsView .fixed-header + .app-main {
    height: calc(100vh - $navbar-height);
    min-height: calc(100vh - $navbar-height - $tags-view-height);
    padding-top: $tags-view-height;
  }
}

.layout-top {
  .hasTagsView .fixed-header + .app-main {
    padding-top: $tags-view-height;
  }
}

.body-container {
  padding-bottom: 150px;
}

.foot-container {
  position: absolute;
  bottom: 0;
  z-index: 999;
  width: 100%;
  height: 90px;
  overflow: hidden;
  background-color: $foot-background;
  transition: width 0.28s;

  :deep(.el-menu) {
    border: none;
  }
}

.vertical-line {
  margin: 0 10px; /* 垂直线的左右间距 */
  height: 100%; /* 垂直线的高度 */
  border-left: 1px solid #D2D2D2; /* 竖线的样式，可以更改颜色和宽度 */
}
</style>
