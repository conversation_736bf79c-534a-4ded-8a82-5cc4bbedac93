<template>
	<div class="monthly-increase">
		<div class="title">
			{{ title }}
		</div>
		<div class="down">
			<div class="left">
				<div class="cnt">
					{{ count }}
				</div>
				<div class="bar">
					<span class="month-on-month">月环比：</span>
					<span>{{ mom }}</span>
					<img class="logo" :src="getAssetsFile('index/down.png')" alt="down"/>
				</div>
			</div>
			<div class="right">
				<img class="pic" :src="getAssetsFile('index/increase.png')" alt="down"/>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import {getAssetsFile} from "@/utils/commonUtils";

const props = defineProps({
	title: {
		type: String,
		default: '',
		required: true,
	},
	count: {
		type: String,
		default: '',
		required: true,
	},
	mom: {
		type: String,
		default: '',
		required: true,
	},
})

</script>

<style scoped lang="scss">
.monthly-increase {
	//opacity: 0.6;
	border-radius: 5.22px;
	background: #E1E7F9;
	padding: 15px;
	width: 100%;
	height: 177px;

	.title {
		opacity: 1;
		color: #5A7597;
		font-family: PingFang SC;
		font-weight: 500;
		font-size: 20.88px;
		line-height: normal;
		letter-spacing: 0;
		text-align: left;
	}

	.down {
		display: flex;

		.left {
			width: 50%;

			.cnt {
				opacity: 1;
				color: #475B75;
				font-family: PingFang SC;
				font-weight: 500;
				font-size: 39.16px;
				line-height: normal;
				letter-spacing: 0;
				text-align: left;
			}

			.bar {
				.year-on-year {
					opacity: 1;
					color: #5A7597CC;
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 18.27px;
					line-height: normal;
					letter-spacing: 0;
					text-align: left;
				}

				.month-on-month {
					opacity: 1;
					color: #5A7597CC;
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 18.27px;
					line-height: normal;
					letter-spacing: 0;
					text-align: left;
				}

				.logo {
					width: 9px;
					height: 15px;
				}
			}

		}

		.right {
			width: 49%;
			.pic {
				width: 100%;
				height: 105px;
			}
		}
	}

}

</style>
