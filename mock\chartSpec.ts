//定义视频的宽高比
const width = 600;
const height = 258;

// 风险场景饼图配置
export const pieSpecSense = {
    type: 'pie',
    data: [
        {
            id: 'id0',
            values: [
                { type: '移动云-产品订购组合不合理', value: '10' },
                { type: '移动云-产品收入异动', value: '15' },
                { type: 'ICT-上下游存在关联关系', value: '8' },
                { type: 'ICT-上下游存在AB/BA关', value: '12' }
            ]
        }
    ],
    outerRadius: 0.8,
    valueField: 'value',
    categoryField: 'type',
    title: {
        visible: true,
        text: '各场景风险预警单统计'
    },
    legends: {
        visible: true,
        orient: 'left'
    },
    label: {
        visible: true
    },
    tooltip: {
        mark: {
            content: [
                {
                    key: (datum: any) => datum['type'],
                    value: (datum: any) => datum['value'] + '%'
                }
            ]
        }
    },
    // 添加动画配置
    animationAppear: {
        duration: 3000, // 入场动画时长，单位毫秒
        easing: 'bounceOut' // 缓动效果
    },
    animationUpdate: {
        duration: 2000, // 更新动画时长，单位毫秒
        easing: 'cubicOut'
    },
    width: width,
    height: height
};

// 风险金额饼图配置
export const pieSpecAmount = {
    type: 'pie',
    data: [
        {
            id: 'id0',
            values: [
                { type: '移动云-产品订购组合不合理', value: '300' },
                { type: '移动云-产品收入异动', value: '500' },
                { type: 'ICT-上下游存在关联关系', value: '600' },
                { type: 'ICT-上下游存在AB/BA关', value: '400' }
            ]
        }
    ],
    outerRadius: 0.8,
    valueField: 'value',
    categoryField: 'type',
    title: {
        visible: true,
        text: '各场景风险涉及金额统计'
    },
    legends: {
        visible: true,
        orient: 'left'
    },
    label: {
        visible: true
    },
    tooltip: {
        mark: {
            content: [
                {
                    key: (datum: any) => datum['type'],
                    value: (datum: any) => datum['value'] + '%'
                }
            ]
        }
    },
    // 添加动画配置
    animationAppear: {
        duration: 3000, // 入场动画时长，单位毫秒
        easing: 'bounceOut' // 缓动效果
    },
    animationUpdate: {
        duration: 2000, // 更新动画时长，单位毫秒
        easing: 'cubicOut'
    },
    width: width,
    height: height
};

// 柱状图配置
//数据-上下游存在关联关系
const data01 = [
    { name: '上下游存在AB/BA关系',month: '一月', count: 120 },
    { name: '上下游存在AB/BA关系',month: '二月', count: 80 },
    { name: '上下游存在AB/BA关系',month: '三月', count: 60 },
    { name: '上下游存在AB/BA关系',month: '四月', count: 70 },
    { name: '上下游存在AB/BA关系',month: '五月', count: 90 },
    { name: '上下游存在AB/BA关系',month: '六月', count: 50 }
]
//数据-产品订购组合不合理
const data02 = [
    { name: '上下游存在关联关系',month: '一月', count: 60 },
    { name: '上下游存在关联关系',month: '二月', count: 100 },
    { name: '上下游存在关联关系',month: '三月', count: 120 },
    { name: '上下游存在关联关系',month: '四月', count: 60 },
    { name: '上下游存在关联关系',month: '五月', count: 75 },
    { name: '上下游存在关联关系',month: '六月', count: 80 }
]
//数据-上下游存在关联关系
const data03 = [
    { name: '产品订购组合不合理',month: '一月', count: 85 },
    { name: '产品订购组合不合理',month: '二月', count: 55 },
    { name: '产品订购组合不合理',month: '三月', count: 60 },
    { name: '产品订购组合不合理',month: '四月', count: 100 },
    { name: '产品订购组合不合理',month: '五月', count: 120 },
    { name: '产品订购组合不合理',month: '六月', count: 180 }
]
//数据-上下游存在关联关系
const data04 = [
    { name: '产品收入异动',month: '一月', count: 160 },
    { name: '产品收入异动',month: '二月', count: 80 },
    { name: '产品收入异动',month: '三月', count: 90 },
    { name: '产品收入异动',month: '四月', count: 60 },
    { name: '产品收入异动',month: '五月', count: 90 },
    { name: '产品收入异动',month: '六月', count: 60 }
]
export const barSpec01 = {
    type: 'common',
    series: [
        {
            type: 'bar',
            data: { values: data01 },
            xField: 'month',
            yField: 'count',
            morph: {
                morphKey: '上下游存在AB/BA关系'
            },
            seriesField: 'month',
        }
    ],
    axes: [
        { orient: 'left', type: 'linear' },
        { orient: 'bottom', type: 'band' }
    ],
    title: {
        text: '各场景风险涉及趋势',
        subtext: '上下游存在AB/BA关系'
    },
    // 系列配置: 图元样式
    bar: {
        style: {
            cornerRadius: 4
        }
    },
    // 系列配置: 图元标签
    label: {
        visible: true,
        position: 'top'
    },
    // 配置图例
    legends: {
        visible: true,
        orient: 'top',
        position: 'start'
    },
    // 添加动画配置
    animationAppear: {
        duration: 3000, // 入场动画时长，单位毫秒
        easing: 'cubicOut' // 缓动效果
    },
    animationUpdate: {
        duration: 2000, // 更新动画时长，单位毫秒
        easing: 'cubicOut'
    },
    width: width,
    height: height
};

export const barSpec02 = {
    type: 'common',
    series: [
        {
            type: 'bar',
            data: { values: data02 },
            xField: 'month',
            yField: 'count',
            morph: {
                morphKey: '上下游存在关联关系'
            },
            seriesField: 'month',
        }
    ],
    axes: [
        { orient: 'left', type: 'linear' },
        { orient: 'bottom', type: 'band' }
    ],
    title: {
        text: '各场景风险涉及趋势',
        subtext: '上下游存在关联关系'
    },
    // 系列配置: 图元样式
    bar: {
        style: {
            cornerRadius: 4
        }
    },
    // 系列配置: 图元标签
    label: {
        visible: true,
        position: 'top'
    },
    // 配置图例
    legends: {
        visible: true,
        orient: 'top',
        position: 'start'
    },
    // 添加动画配置
    animationAppear: {
        duration: 3000, // 入场动画时长，单位毫秒
        easing: 'cubicOut' // 缓动效果
    },
    animationUpdate: {
        duration: 2000, // 更新动画时长，单位毫秒
        easing: 'cubicOut'
    },
    width: width,
    height: height
};

export const barSpec03 = {
    type: 'common',
    series: [
        {
            type: 'bar',
            data: { values: data03 },
            xField: 'month',
            yField: 'count',
            morph: {
                morphKey: '产品订购组合不合理'
            },
            seriesField: 'month',
        }
    ],
    axes: [
        { orient: 'left', type: 'linear' },
        { orient: 'bottom', type: 'band' }
    ],
    title: {
        text: '各场景风险涉及趋势',
        subtext: '产品订购组合不合理'
    },
    // 系列配置: 图元样式
    bar: {
        style: {
            cornerRadius: 4
        }
    },
    // 系列配置: 图元标签
    label: {
        visible: true,
        position: 'top'
    },
    // 配置图例
    legends: {
        visible: true,
        orient: 'top',
        position: 'start'
    },
    // 添加动画配置
    animationAppear: {
        duration: 3000, // 入场动画时长，单位毫秒
        easing: 'cubicOut' // 缓动效果
    },
    animationUpdate: {
        duration: 2000, // 更新动画时长，单位毫秒
        easing: 'cubicOut'
    },
    width: width,
    height: height
};

export const barSpec04 = {
    type: 'common',
    series: [
        {
            type: 'bar',
            data: { values: data04 },
            xField: 'month',
            yField: 'count',
            morph: {
                morphKey: '产品收入异动'
            },
            seriesField: 'month',
        }
    ],
    axes: [
        { orient: 'left', type: 'linear' },
        { orient: 'bottom', type: 'band' }
    ],
    title: {
        text: '各场景风险涉及趋势',
        subtext: '产品收入异动'
    },
    // 系列配置: 图元样式
    bar: {
        style: {
            cornerRadius: 4
        }
    },
    // 系列配置: 图元标签
    label: {
        visible: true,
        position: 'top'
    },
    // 配置图例
    legends: {
        visible: true,
        orient: 'top',
        position: 'start'
    },
    // 添加动画配置
    animationAppear: {
        duration: 3000, // 入场动画时长，单位毫秒
        easing: 'cubicOut' // 缓动效果
    },
    animationUpdate: {
        duration: 2000, // 更新动画时长，单位毫秒
        easing: 'cubicOut'
    },
    width: width,
    height: height
};

// 视频时间配置
export const getTimeConfig = (totalTimeInSeconds: number = 4) => {
    const totalTime = totalTimeInSeconds * 1000;
    return {
        totalTime: totalTime,
        frameArr: [
            {
                time: 0, // 开始时间点
                state: 'start',
            },
            {
                time: totalTime * 0.75, // 75%时间点的状态
                state: 'middle',
            },
            {
                time: totalTime, // 结束时间点
                state: 'end',
            }
        ]
    };
};
