<template>
  <div>
    <!-- 隐藏的容器用于图表初始化 -->
    <div ref="chartContainer" style="position: absolute; width: 1px; height: 1px; overflow: hidden;"></div>
    <!-- 添加视频播放区域 -->
    <div class="video-container" :style="props.customStyle">
      <video
          v-if="videoUrl"
          ref="videoPlayer"
          muted
          controls
          @loadeddata="handleVideoLoaded"
          @pause="handleVideoPause"
          @play="handleVideoPlay"
          @timeupdate="handleVideoTimeUpdate"
          @ended="handleVideoEnded"
          @seeked="handleVideoSeeking"
          style="width: 100%; height: 100%; object-fit: contain; margin: 0 auto; display: block;"
      >
        <source :src="videoUrl" type="video/mp4">
        您的浏览器不支持HTML5视频播放
      </video>
      <!-- 当视频URL不存在时显示占位符 -->
      <div v-else class="video-placeholder">
        <el-empty description="视频尚未生成" :image-size="200">
          <template #description>
            <p>正在准备风险播报视频...</p>
          </template>
        </el-empty>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import VChart,{ registerMorph } from '@visactor/vchart';
import VMind from '@visactor/vmind'

import { ManualTicker, defaultTimeline } from "@visactor/vrender-core";
import * as vRenderCore from "@visactor/vrender-core";
import { createFFmpeg, fetchFile } from "@ffmpeg/ffmpeg";
import { createCanvas } from "canvas";

import {reactive} from "vue";
//与后端交互接口，在api目录中定义
import { uploadVideo as uploadVideoApi,getLatestVideo, getVideoList,getDynamicVideoInformation } from "@/api/risk/riskBroadcast/riskBroadcast";
//定义的图表的mock数据
import { pieSpecSense,pieSpecAmount,barSpec01,barSpec02,barSpec03,barSpec04,getTimeConfig } from "../../../../mock/chartSpec";

// 定义props，允许父组件控制组件行为
const props = defineProps({
  autoGenerate: {
    type: Boolean,
    default: false
  },
  customStyle: {
    type: Object,
    default: () => ({})
  }
});

// 定义emit，用于向父组件发送事件
const emit = defineEmits(['video-loaded', 'video-generated', 'video-error','video-upload','video-paused', 'video-played']);
//初始化播放器
const initializePlayer = async () => {
  try {
    // 确保DOM已经完全加载
    await nextTick()
    const riskParam = {
      level: '1', // 例如全国
      monthStr: '202505',
      regionName: '全国'
    }
    //获取后端视频语音文字
    await getDynamicVideoInformation(riskParam).then(res => {
      console.log("视频句柄数据：",res)
    })

    // 分别检查两个容器
    if (!chartContainer.value) {
      console.error('Chart container not found')
      return
    }

    // 如果有视频URL才检查视频播放器（但是初始化的时候，肯定是没有URL的，所有是正常的）
    if (videoUrl.value && !videoPlayer.value) {
      console.error('Video player not found')
      return
    }

    console.log('播放器初始化完成，chartContainer:', !!chartContainer.value, 'videoPlayer:', !!videoPlayer.value)
  } catch (error) {
    console.error('Player initialization error:', error)
  }
}
onMounted(async () => {
  console.log('组件已挂载，正在初始化播放器')
  await initializePlayer()
  //取消语音（初始化后就取消语音，是为了防止语音朗读后，页面刷新导致语音还在继续朗读）
  window.speechSynthesis.cancel();
  //先加载最新的视频（从后端加载最新视频，获取视频流）
  loadLatestVideo().then(() => {
    console.log("视频加载成功----------------")
    //初始化canvas配置（这步是初始化图表转视频需要用到的组件配置）
    initConfig().then(() => {
      console.log('canvas配置初始化完成')
      //根据当前日期判断是否需要生成新的视频（这步按照实际要求，可以使用定时任务或者其他逻辑实现：生成视频-》合并视频-》上传视频）
      generateVideo().then(() => {
        //生成后，进行视频合并操作
        console.log("视频生成完成")
        mergeVideo().then(() => {
          console.log('视频已合并完成')
          //上传视频
          uploadVideo().then(() => {
            // ElMessage.success('视频已上传')
            //重新获取最新视频
            loadLatestVideo()
            //取消语音（当重新生成视频后，从后端获取到了最新的视频，此时语音是在朗读，因此先取消，然后再进行朗读）
            window.speechSynthesis.cancel();
            resetTimePoints()
            handleVideoUpload("子组件：视频上传完成")
          })
        })
      })
    })
  })
})
// 添加onUnmounted生命周期钩子
onUnmounted(() => {
  // 释放视频URL资源
  if (mergedVideoUrl.value) {
    URL.revokeObjectURL(mergedVideoUrl.value);
  }

  // 如果ffmpeg已加载，尝试卸载它
  if (ffmpeg.isLoaded()) {
    try {
      // 清理ffmpeg文件系统中的文件
      for (let i = 0; i < videoBlobs.length; i++) {
        try {
          ffmpeg.FS('unlink', `video${i}.mp4`);
        } catch (e) {
          console.warn('清理文件失败:', e);
        }
      }

      try {
        ffmpeg.FS('unlink', 'inputs.txt');
        ffmpeg.FS('unlink', 'output.mp4');
      } catch (e) {
        console.warn('清理文件失败:', e);
      }
    } catch (e) {
      console.error('清理FFmpeg资源失败:', e);
    }
  }
});
const chartContainer = ref(null)

const videoBlobs = reactive<Blob[]>([])
//视频播放url
const mergedVideoUrl = ref("")
const videoPlayer = ref(null);
// 新增状态变量
const videoUrl = ref("");
const isLoading = ref(false);
const videoInfo = ref(null);

const word = ref("据新华社报道，根据世界气象组织5月28日发布的最新气候报告，近5年间，有80%几率将出现有记录以来“最暖年份”，即这五年中至少有一年的全球平均气温将超过2024年刚创下的纪录。");


// 处理视频加载完成事件
const handleVideoLoaded = () => {
  console.log('视频加载完成，尝试播放');
  if (videoPlayer.value) {
    // 尝试播放视频（自动播放功能）
    // const playPromise = videoPlayer.value.play();

    // if (playPromise !== undefined) {
    //   playPromise.then(() => {
    //     console.log('视频开始自动播放');
    //     // 触发父组件的video-loaded事件
    //     emit('video-loaded', { success: true, message: '视频加载并开始播放' });
    //   }).catch((error:any) => {
    //     console.error('自动播放失败:', error);
    //     // 如果自动播放失败，可能是因为浏览器策略，需要用户交互
    //     // ElMessage.info('请点击播放按钮开始播放视频');
    //     // 仍然触发loaded事件，但标记自动播放失败
    //     emit('video-loaded', { success: false, error, message: '视频已加载，但自动播放失败' });
    //   });
    // }
  }
};
// 添加视频生成完成的方法
const handleVideoGenerated = (message:any) => {
  console.log("子组件：" + message);
  // 触发父组件的video-generated事件
  emit('video-generated', { success: true, message: message });
};

// 添加视频处理错误的方法
const handleVideoError = (error:any) => {
  console.error('子组件-视频处理错误:', error);
  // 触发父组件的video-error事件
  emit('video-error', { success: false, error, message: '视频处理出错' });
};

// 添加视频上传完成的方法
const handleVideoUpload = (message:any) => {
  console.log("子组件：" + message);
  // 触发父组件的video-generated事件
  emit('video-upload', { success: true, message: message });
};
// 处理视频暂停事件
const handleVideoPause = () => {
  console.log('视频已暂停');
  // 可以在这里添加您需要的逻辑，比如暂停语音播报
  window.speechSynthesis.pause();
  // 如果需要通知父组件，可以使用emit
  emit('video-paused', { status: 'paused' });
};

// 处理视频播放事件
const handleVideoPlay = () => {
  console.log('视频开始播放');
  // 可以在这里添加您需要的逻辑，比如恢复语音播报
  window.speechSynthesis.resume();
  // 如果需要通知父组件，可以使用emit
  emit('video-played', { status: 'playing' });
};
const timePoints = [
  {time:1.0,text:"本次风险预警统计，移动云产品在订购组合和收入方面出现风险预警；ICT 上下游存在关联及 AB/BA 关系风险。金额方面，ICT 上下游关联关系涉及金额最高，移动云产品风险涉及金额紧随其后，需重点关注。",triggered:false},
  {time:42.0,text:"风险预警播报结束",triggered:false}
]

//视频播放进度更新事件
const handleVideoTimeUpdate = () => {
    const currentTime = videoPlayer.value.currentTime;
    timePoints.forEach(point => {
        if (Math.abs(currentTime - point.time) < 0.5 && !point.triggered) {
            console.log('时间点触发：', point.text);
            textToVoice(point.text)
            point.triggered = true;
        }
    })
}
// 在视频播放结束或重新开始时重置所有时间点
const resetTimePoints = () => {
  timePoints.forEach(point => {
    point.triggered = false;
  });
};
// 在视频事件中添加重置
const handleVideoEnded = () => {
  resetTimePoints();
  console.log('视频已结束，重置timePoint')
  console.log('timePoints：',timePoints)
};

const handleVideoSeeking = () => {
  // 当用户拖动进度条时重置时间点
  resetTimePoints();
  //取消语音
  window.speechSynthesis.cancel();
};
const initConfig = async () => {
  // 注册形变（图表动画所需）
  registerMorph('bar', 'pie');
  const spec = {
    data: [
      {
        id: 'barData',
        values: [
          { name: '一级缺陷',month: '一月', count: 22 },
          { name: '一级缺陷',month: '二月', count: 13 },
          { name: '一级缺陷',month: '三月', count: 25 },
          { name: '一级缺陷',month: '四月', count: 29 },
          { name: '一级缺陷',month: '五月', count: 38 },
          { name: '一级缺陷',month: '六月', count: 38 }
        ]
      }
    ],
    type: 'bar',
    xField: ['month'],
    yField: 'count'
  };
  //使用隐藏的div进行VChart初始化（如果视频组件一开始就是在的，也可以使用视频组件的ref）
  const vchart = new VChart(spec, { dom: chartContainer.value });
  setTimeout(() => {

  }, 1500);
  // 绘制
  // vchart.renderSync();
};
// 在组件顶部定义一个共享的FFmpeg实例
const ffmpeg = createFFmpeg({
  log: true,
  corePath: '/ffmpeg/ffmpeg-core.js'
});


// 修改loadFFmpeg函数为组件级方法
const loadFFmpeg = async () => {
  if (!ffmpeg.isLoaded()) {
    await ffmpeg.load();
  }
  return ffmpeg;
};
// 加载最新视频（从后端获取最新的视频流，先进行播放）
const loadLatestVideo = async () => {
  //加载视频的loading，按需判断是否需要取消
  isLoading.value = true;
  videoUrl.value = ""; // 清空当前视频URL

  try {
    const response = await getLatestVideo();
    console.log("response:", response)

    if (response.data && response.data.code === 200) {
      const videoData = response.data.data;

      if (videoData) {
        // 设置视频信息
        videoInfo.value = videoData;

        // 设置视频URL（使用完整的后端URL）
        const baseUrl = import.meta.env.VITE_APP_BASE_API || '';
        videoUrl.value = `${baseUrl}${videoData.url}`;
        console.log("videoUrl:", videoUrl.value)

        console.log("视频加载成功")
      } else {
        console.log("没有找到视频")
      }
    } else {
      console.log("获取视频失败:", response)
    }
  } catch (error) {
    console.error('加载视频失败:', error);
  } finally {
    isLoading.value = false;
  }
};
const generateVideo = async () => {
  try {
    console.log("generateVideo")
    registerMorph();

    // 使用共享的ffmpeg实例，而不是创建新的
    await loadFFmpeg();
    //初始化一个VMind实例（用于图表转视频）
    const vmind = new VMind({});

      await generateVideoStreams(vmind)
    // 视频生成成功后触发事件
    handleVideoGenerated("子组件：视频生成成功,继续合并视频");
  }catch (error) {
    // 视频生成失败时触发错误事件
    handleVideoError(error);
  }
};
// 生成视频流
const generateVideoStreams = async (vmind:any) => {
  //从mock数据加载图表
    const specList = [pieSpecSense, pieSpecAmount,barSpec01,barSpec02,barSpec03,barSpec04]
    try {
      //循环转换图表为视频
      for (const spec of specList) {
        console.log("开始生成视频流：", spec)
        // 传入图表spec，视频时长和视频化必要参数，返回ObjectURL
        const videoSrc = await vmind.exportVideo(spec, getTimeConfig(8), {
          VChart,
          FFmpeg: ffmpeg,
          fetchFile,
          ManualTicker,
          defaultTimeline,
          createCanvas
        });
        if (!videoSrc) {
          throw new Error(`视频流生成失败：${spec.title?.text || '未知图表'}`)
        }
        const blob = videoSrc instanceof Blob
            ? videoSrc
            : new Blob([new Uint8Array(videoSrc)], {type: "video/mp4"});
        if (blob.size === 0) {
          throw new Error(`生成的视频blob为空：${spec.title?.text || '未知图表'}`)
        }
        // 存储Blob对象而不是URL
        videoBlobs.push(blob);
        console.log("视频流生成成功：", blob.size, "bytes")
      }
    }catch (error) {
      console.error("生成视频流失败：", error);
      throw error;
    }
}
const mergeVideo = async () => {
  try {
    console.log("开始合并视频，数量：", videoBlobs.length, videoBlobs);
    await loadFFmpeg();
    if (!ffmpeg.isLoaded()) {
      await ffmpeg.load();
    }
    if (videoBlobs.length === 0) {
      throw new Error("没有可用的视频流可合并");
    }
    // 写入所有视频文件
    for (let i = 0; i < videoBlobs.length; i++) {
      const name = `video${i}.mp4`;
      try {
        // 将Blob转换为ArrayBuffer
        const arrayBuffer = await videoBlobs[i].arrayBuffer();
        // 将ArrayBuffer转换为Uint8Array
        const uint8Array = new Uint8Array(arrayBuffer);
        // 写入FFmpeg文件系统
        ffmpeg.FS('writeFile', name, uint8Array);
        console.log(`成功写入视频文件：${name}`);
      } catch (error) {
        throw new Error(`写入视频文件 ${name} 失败：${error.message}`);
      }
    }

    // 创建输入文件列表
    let inputList = '';
    for (let i = 0; i < videoBlobs.length; i++) {
      inputList += `file 'video${i}.mp4'\n`;
    }
    console.log("inputList:",inputList)
    // 写入文件列表
    ffmpeg.FS('writeFile', 'inputs.txt', inputList);
    // 执行合并命令
    await ffmpeg.run(
        '-f', 'concat',
        '-safe', '0',
        '-i', 'inputs.txt',
        'output.mp4'
    );
    //'-s', '1280x720',
    // 读取输出文件
    const data = ffmpeg.FS('readFile', 'output.mp4');
    // 创建合并后的视频Blob
    const mergedBlob = new Blob([data.buffer], { type: 'video/mp4' });

    // 创建下载链接
    const url = URL.createObjectURL(mergedBlob);
    mergedVideoUrl.value = url;

    // 视频合并成功后可以触发生成完成事件
    handleVideoGenerated("子组件：视频合并完成");
    // 在合并视频后添加代码，打印视频信息
    console.log("生成的视频尺寸:", mergedBlob.size, "字节");
  }catch (error) {
    // 视频合并失败时触发错误事件
    handleVideoError(error);
  }
};

// 上传视频到后端
const uploadVideo = async () => {
  if (!mergedVideoUrl.value) {
    console.log( "请先生成并合并视频")
    return;
  }

  try {
    // 从URL获取Blob对象
    const response = await fetch(mergedVideoUrl.value);
    const videoBlob = await response.blob();

    // 创建FormData对象
    const formData = new FormData();
    formData.append('file', videoBlob, `风险数据视频_${formatDateTime()}.mp4`);
    formData.append('videoType', 'risk_analysis');

    // 发送上传请求
    const result = await uploadVideoApi(formData);

    if (result.data) {
      console.log("视频上传成功:", result)
      // 上传成功后自动加载最新视频
      await loadLatestVideo();
    } else {
      console.log("上传失败:", result)
    }
  } catch (error) {
    console.error('上传视频时出错:', error);
  } finally {
      console.log("视频上传完成")
  }
};

// 手动触发视频生成
const manualGenerateVideo = async () => {
  try {
    await initConfig();
    await generateVideo();
    await mergeVideo();
    await uploadVideo();
    await loadLatestVideo();
    return true;
  } catch (error) {
    console.error('手动生成视频失败:', error);
    emit('video-error', error);
    return false;
  }
};

//时间格式化
function formatDateTime() {
  const now = new Date();

  // 获取年、月、日、时、分、秒
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');

  // 按照指定格式拼接字符串
  return `${year}-${month}-${day} ${hours}-${minutes}-${seconds}`;
}
//文字转语音
const textToVoice = (msg:string) => {
  // console.log("可用语音列表：",window.speechSynthesis.getVoices())
  // 为了防止在暂停状态下转语音，调用前设置继续播放
  window.speechSynthesis.resume();
  const speech = new SpeechSynthesisUtterance();
  // 截取第一个字符并拼接到原文本
  const firstChar = msg.substring(0, 1);
  const processedText = firstChar + msg;

  console.log("原始文字内容：", msg);
  console.log("处理后的文字内容：", processedText);
  speech.text = processedText; // 内容
  speech.lang = "zh-cn"; // 语言
  // speech.voiceURI = "Microsoft Huihui - Chinese (Simplified, PRC)"; // 声音和服务
  // 尝试获取中文语音
  const voices = window.speechSynthesis.getVoices();
  const chineseVoice = voices.find(voice =>
      voice.lang.includes('zh') ||
      voice.name.includes('Chinese') ||
      voice.name.includes('中文')
  );
  console.log("chineseVoice：",chineseVoice)

  if (chineseVoice) {
    speech.voice = chineseVoice;
  }
  speech.volume = 0.7;
  speech.rate = 1; // 语速
  speech.pitch = 1.5;
  // 添加事件监听
  speech.onstart = () => {
    console.log('语音播放开始');
  };

  speech.onend = () => {
    console.log('语音播放结束');
  };

  speech.onerror = (event) => {
    console.error('语音播放错误:', event);
    // ElMessage.error('语音播放失败');
  };
  window.speechSynthesis.speak(speech);
};
// 暴露方法给父组件
defineExpose({
  loadLatestVideo,
  manualGenerateVideo,
  initializePlayer,
  videoUrl
});
</script>

<style scoped lang="scss">
.video-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  overflow: hidden;
  object-fit: contain;
}

video {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.video-player {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.video-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}
</style>
