<template>
	<div class="business-card">
		<div class="title-row">
			<img class="logo" :src="getAssetsFile(imgUrl)" alt="logo"/>
			<span class="title">{{ title }}</span>
		</div>
		<div>
			<span class="work-order-title">风险工单数：</span>
			<span class="work-order-count">{{ workOrderCount }}</span>
		</div>
		<div class="leve-list">
			<span class="level-title">一级：</span><span class="level-count">{{ levelOneCnt }}</span>
			<span class="level-title">二级：</span><span class="level-count">{{ levelTwoCnt }}</span>
			<span class="level-title">三级：</span><span class="level-count">{{ levelThreeCnt }}</span>
		</div>
		<div class="rate-list">
			<span class="completion-rate">完成率：</span>
			<div class="progress">
				<el-progress :percentage="completionRate"/>
			</div>

		</div>
	</div>
</template>

<script setup lang="ts">
import {getAssetsFile} from "@/utils/commonUtils";

const props = defineProps({
	imgUrl: {
		type: String,
		required: true,
	},
	title: {
		type: String,
		required: true,
	},
	workOrderCount: {
		type: Number,
		required: true,
	},
	levelOneCnt: {
		type: Number,
		required: true,
	},
	levelTwoCnt: {
		type: Number,
		required: true,
	},
	levelThreeCnt: {
		type: Number,
		required: true,
	},
	completionRate: {
		type: String,
		required: true,
	},
	width: {
		type: String,
		required: true,
	},
});
</script>

<style scoped lang="scss">
.business-card {
	min-height: 150px;
	padding: 35px;
	display: flex;
	flex-direction: column;
	line-height: 30px;

	.title-row {
		display: flex;
		flex-direction: row;
		align-items: center;

		.logo {
			width: 1em;
			height: 1em;
			margin-right: 5px;
		}

		.title {
			font-weight: bold;
			font-size: 16px;

		}
	}

	.work-order-title {
		opacity: 1;
		color: #35393E;
		font-family: PingFang SC;
		font-weight: 300;
		font-size: 12px;
		line-height: normal;
		letter-spacing: 0;
		text-align: left;
	}

	.work-order-count {
		opacity: 1;
		color: #5A7597;
		font-family: PingFang SC;
		font-weight: 500;
		font-size: 16px;
		line-height: normal;
		letter-spacing: 0;
		text-align: left;
	}


	.leve-list {

		.level-title {
			opacity: 1;
			color: #5A7597;
			font-family: PingFang SC;
			font-weight: 400;
			font-size: 12px;
			line-height: normal;
			letter-spacing: 0;
			text-align: left;

		}

		& .level-title:not(:nth-child(1)) {
			margin-left: 5px;
		}

		.level-count {
			opacity: 1;
			color: #000000;
			font-family: PingFang SC;
			font-weight: 500;
			font-size: 14px;
			line-height: normal;
			letter-spacing: 0;
			text-align: left;
		}
	}

	.rate-list {
		display: flex;
		flex-direction: row;

		.completion-rate {
			opacity: 1;
			color: #000000D8;
			font-family: PingFang SC;
			font-weight: regular;
			font-size: 13px;
			line-height: normal;
			letter-spacing: 0;
			text-align: left;
		}

		.progress {
			width: 60%;
			margin: auto;
		}

	}

}

</style>
