<template>
	<div>
		<el-dialog
			v-model="dialogVisible"
			:title="title"
			top="2vh"
			width="80%"
			:draggable="false"
			:close-on-click-modal="false"
			:close-on-press-escape="true"
			:before-close="handleClose"
		>
			<el-container>
				<el-main>
					<el-card style="width: 100%;">
						<el-form
							:model="form"
							ref="formRef"
							label-width="120px"
							:rules="rules"
							:disabled="formDisabled"
						>
							<el-row :gutter="24">
								<el-col :span="12">
									<el-text
										class="mx-1"
										size="large"
										line-clamp="1"
										tag="b"
									>
										{{ baseInfoTitle }}
									</el-text>
								</el-col>
							</el-row>

							<el-row :gutter="24">
								<el-col :span="12">
									<el-form-item label="产品名称" prop="productName">
										<el-input v-model="form.productName"></el-input>
									</el-form-item>
								</el-col>
								<el-col :span="12" v-if="opt === 'view'">
									<el-form-item label="产品编码" prop="productNumber">
										<el-input v-model="form.productNumber"></el-input>
									</el-form-item>
								</el-col>
							</el-row>
							<el-row :gutter="24">
								<el-col :span="24">
									<el-form-item label="产品描述" prop="description">
										<el-input
											v-model="form.description"
											type="textarea"
											:rows="5"
										>
										</el-input>
									</el-form-item>
								</el-col>
							</el-row>
							<el-row :gutter="24" v-hasPerm="['prod:config:upShelf']">
								<el-col :span="12">
									<el-form-item label="产品状态" prop="productStatus">
										<dictionary
											:clearable="false"
											v-model="form.productStatus"
											type-code="productStatus"
											placeholder=""
										></dictionary>
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="上下架状态" prop="shelfStatus">
										<dictionary
											:disabled="true"
											:clearable="false"
											v-model="form.shelfStatus"
											type-code="shelfStatus"
											placeholder=""
										></dictionary>
									</el-form-item>
								</el-col>
							</el-row>

							<el-row :gutter="24">
								<el-col :span="12">
									<el-form-item label="产品大类" prop="productClass">
										<el-select
											v-model="form.productClass"
											placeholder=""
											clearable
											@change="productClassChange"
											:disabled="opt === 'update'"
										>
											<el-option
												v-for="option in productClassOption"
												:key="option.value"
												:label="option.label"
												:value="option.value"
											/>
										</el-select>
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="是否为热门产品" prop="isHot">
										<dictionary
											:clearable="false"
											v-model="form.isHot"
											type-code="whether"
											placeholder=""
										></dictionary>
									</el-form-item>
								</el-col>
							</el-row>

							<el-row :gutter="24" class="mt-10">
								<el-col :span="12">
									<el-text
										class="mx-1"
										size="large"
										line-clamp="1"
										tag="b"
									>
										{{ specificationsTitle }}
									</el-text>
								</el-col>
							</el-row>

							<el-row :gutter="24" v-if="opt !== 'view'">
								<el-col :span="4">
									<el-button
										size="small"
										:icon="Plus"
										type="primary"
										@click="addGpu"
										class="w-full"
									>新增规格
									</el-button>
								</el-col>
							</el-row>
							<el-row :gutter="24" class="mt-3">
								<el-col :span="24">
									<el-table
										:data="form.specTableData"
										border
										style="width: 100%"
									>
										<el-table-column type="index" label="序号" width="55"/>
										<el-table-column
											prop="spfName"
											align="center"
											label="规格名称"
										></el-table-column>
										<el-table-column
											prop="spfInfo"
											align="center"
											label="规格信息"
										>
											<template #default="scope">
												<span v-html="scope.row.spfInfo"></span>
											</template>
										</el-table-column>
										<el-table-column
											prop="ratePlanName"
											align="center"
											label="资费名称"
										></el-table-column>
										<el-table-column
											prop="ratePlanInfo"
											align="center"
											label="资费信息"
										>
											<template #default="scope">
												<span v-html="scope.row.ratePlanInfo"></span>
											</template>
										</el-table-column>
										<el-table-column label="操作" align="center" width="200"
										                 v-if="opt !== 'view'">
											<template #default="scope">
												<el-button link type='primary'
												           @click="editGpu(scope.row, scope.$index)">编辑
												</el-button>
												<el-button link type='primary'
												           @click="deleteSpecData(scope.$index)">
													删除
												</el-button>
											</template>
										</el-table-column>
									</el-table>
								</el-col>
							</el-row>

							<!--优惠信息-->
							<el-row :gutter="24" class="mt-10"
							        v-if="(opt === 'view' && form.discountDescription) || opt !== 'view'">
								<el-col :span="12">
									<el-text
										class="mx-1"
										size="large"
										line-clamp="1"
										tag="b"
									>
										{{ preferentialTitle }}
									</el-text>
								</el-col>
							</el-row>

							<el-row :gutter="24" v-if="opt !== 'view'">
								<el-col :span="8">
									<el-form-item label="订购时长" prop="duration">
										<el-input v-model="form.duration">
											<template #append>
												<el-select v-model="form.discountType" placeholder=""
												           style="width: 60px">
													<el-option label="年" value="Y"/>
													<el-option label="月" value="F"/>
												</el-select>
											</template>
										</el-input>
									</el-form-item>
								</el-col>
								<el-col :span="8">
									<el-form-item label="享受" prop="discountValue">
										<el-input v-model="form.discountValue">
											<template #append>
												<el-text
													class="mx-1"
													size="large"
													line-clamp="1"
												>
													折
												</el-text>
											</template>
										</el-input>
									</el-form-item>
								</el-col>
							</el-row>

							<el-row :gutter="24" v-if="opt === 'view' && form.discountDescription">
								<el-col :span="24">
									<el-form-item label="     " prop="discountDescription">
										<el-text
											class="mx-1"
											size="large"
											line-clamp="1"
										>
											{{ form.discountDescription }}
										</el-text>
									</el-form-item>
								</el-col>
							</el-row>
						</el-form>
					</el-card>
				</el-main>
			</el-container>
			<template #footer>
				<div style="text-align: center;">
					<el-button plain @click="back">取消</el-button>
					<el-button type="primary" @click="save(formRef)" v-if="opt !== 'view'">保存</el-button>
				</div>
			</template>
		</el-dialog>
		<gpu-info-dialog
			ref="gpuInfoDlg"
			@addSpecArrayData="addSpecArrayData"
			@updateSpecArrayData="updateSpecArrayData"
		>
		</gpu-info-dialog>
	</div>
</template>

<script setup lang="ts">
import {productDetailQuery, productSave, productSpecQuery} from "@/api/product";
import {ProductDetail, ProductTableInfo, SpecObj, SpfObj} from "@/api/product/types";
import {initDictMap} from "@/utils/commonUtils";
import {FormInstance, FormRules} from "element-plus";
import {Plus} from "@element-plus/icons-vue";
import GpuInfoDialog from "@/views/operator/config/components/GpuInfoDialog.vue";
import {ProductClassEnum} from "@/enums/ProductClassEnum";
import {OperationTypeEnum} from "@/enums/OperationTypeEnum";

const dialogVisible = ref<boolean>(false)
const title = ref<string>('产品详情')
const baseInfoTitle = ref<string>('基本信息')
const specificationsTitle = ref<string>('规格信息')
const preferentialTitle = ref<string>('优惠信息')
const opt = ref<string>('')
const productClass = ref<string>(ProductClassEnum.GPU)
const formDisabled = ref<boolean>(false)

const gpuInfoDlg = ref()

const props = defineProps({
		productClassOption: {
			type: Array<any>,
			required: true,
			default: []
		}
	}
)

const form = reactive<ProductDetail>(<ProductDetail>{
	specTableData: [],
	lineTableData: [],
	shelfStatus: 0,
	discountType: '年'
});
const rules = reactive<FormRules<ProductDetail>>({
	productName: [
		{required: true, message: '请输入产品名称', trigger: 'blur'},
	],
	productClass: [
		{required: true, message: '请输入产品大类', trigger: 'blur'},
	],
	isHot: [
		{required: true, message: '请选择', trigger: 'blur'},
	],
	duration: [
		{required: true, message: '请填写订购时长', trigger: 'blur'},
		{
			pattern: /^\d+(\.\d+)?$/,
			message: '请填写整数',
			trigger: "blur",
		},
	],
	discountValue: [
		{required: true, message: '请填写折扣', trigger: 'blur'},
		{
			pattern: /^(?:100(?:\.0{1,2})?|(?:[1-9]?[0-9])(?:\.[0-9]{1,2})?|0\.\d{1,2})$/,
			message: '请填写0～100数字，可保留两位小数',
			trigger: "blur",
		},
	],
})

const addGpu = () => {
	let productClass = form.productClass
	if (!productClass) {
		ElMessage.warning('请先选择产品大类！')
		return
	}
	gpuInfoDlg.value.init(OperationTypeEnum.ADD, specSpfArray.value)
}

const editGpu = (rowData: SpecObj, index: number) => {
	gpuInfoDlg.value.init(OperationTypeEnum.UPDATE, specSpfArray.value, rowData, index)
}

const deleteSpecData = (index: number) => {
	form.specTableData.splice(index, 1)
}

const addSpecArrayData = (data: SpecObj, spfList: Array<any>) => {
	form.specTableData.push(getSpecTableData(data, spfList))
}

const updateSpecArrayData = (data: SpecObj, spfList: Array<any>, index: number) => {
	form.specTableData[index] = getSpecTableData(data, spfList)
}

const getSpecTableData = (data: any, spfList: Array<any>) => {
	let item = {
		spfName: data.spfName,
		ratePlanName: data.ratePlanName,
		ratePlanInfo: '计费方式：' + typeCodeMap.get('billMethod').value[data.ratePlanType] + '<br/>'
			+ '目录价（元）：' + data.price + '<br/>'
			+ '折扣（%）：' + data.discount,
		ratePlanType: data.ratePlanType,
		price: data.price,
		discount: data.discount,
		ratePlanAndSpfId: data.ratePlanAndSpfId,
	}
	let spfInfo = ''
	let keyNameMap = {}
	spfList.forEach(spf => {
		let id = spf.id
		let name = spf.name
		keyNameMap[id] = name
		let value = data[id]
		if (name) {
			spfInfo += name + '：' + value + '<br/>'
		}
		item[id] = value
	})
	item.spfInfo = spfInfo
	item.keyNameMap = keyNameMap
	return item
}

const init = (optType: string, data: any) => {
	dialogVisible.value = true
	if (data) {
		productClass.value = data.productClass
	}
	opt.value = optType
	switch (optType) {
		case OperationTypeEnum.ADD:
			title.value = '新增配置'
			formDisabled.value = false
			break
		case OperationTypeEnum.UPDATE:
			title.value = '编辑配置'
			formDisabled.value = false
			singleInfo(data)
			break
		case OperationTypeEnum.VIEW:
			formDisabled.value = true
			title.value = '产品详情'
			singleInfo(data)
			break
	}
}

const productClassChange = (value: string) => {
	productClass.value = value
	specSpfArray.value = specSpfMap.get(value)
	form.specTableData = []
}

const singleInfo = (data: ProductTableInfo) => {
	let params = {
		productNumber: data.productNumber
	}
	productDetailQuery(params).then(response => {
		let respData = response.data
		if (respData) {
			form.productName = respData.productName
			form.productNumber = respData.productNumber
			form.productStatus = respData.productStatus
			form.productClass = respData.productClass
			specSpfArray.value = specSpfMap.get(form.productClass)
			form.isHot = respData.isHot
			form.description = respData.description
			form.duration = respData.duration
			form.discountType = respData.discountType
			form.discountValue = respData.discountValue
			form.discountDescription = respData.discountDescription
			let spfAndRateJson = respData.spfAndRateJson
			if (spfAndRateJson) {
				let spfAndRateArray = JSON.parse(spfAndRateJson)
				for (let i = 0; i < spfAndRateArray.length; i++) {
					let spfAndRate = spfAndRateArray[i]
					let spfJsonContentArray = spfAndRate.spfJsonContent
					if (spfJsonContentArray && spfJsonContentArray.length > 0) {
						let rateJsonContent = spfAndRate.rateJsonContent
						let tableRowData = {...rateJsonContent}
						tableRowData.spfName = spfAndRate.spfName
						tableRowData.ratePlanAndSpfId = spfAndRate.ratePlanAndSpfId
						spfJsonContentArray.forEach(spfContent => {
							tableRowData[spfContent.id] = spfContent.value
						})
						let specTableData = getSpecTableData(tableRowData, spfJsonContentArray);
						form.specTableData.push(specTableData)
					}
				}
			}
		}
	})
}

const formRef = ref<FormInstance>()
const handleClose = (done: () => void) => {
	dialogVisible.value = false
	form.tableData = []
	form.specTableData = []
	form.lineTableData = []
	resetForm(formRef.value)
	done()
}

const resetForm = (formEl: FormInstance | undefined) => {
	if (!formEl) return
	formEl.resetFields()
}


const back = () => {
	dialogVisible.value = false
	form.tableData = []
	form.specTableData = []
	form.lineTableData = []
	resetForm(formRef.value)
}

let emits = defineEmits(['query']);

const fixedColArray = ref<Array<string>>(['discount', 'price', 'ratePlanName',
	'ratePlanType', 'spfName', 'ratePlanInfo', 'spfInfo', 'ratePlanAndSpfId', 'keyNameMap'
])

const save = async (formEl: FormInstance | undefined) => {
	if (!formEl) return
	await formEl.validate((valid, fields) => {
		if (valid) {
			let params = {...form}
			let spfAndRateJsonContent = []
			collectSaveData(form.specTableData, spfAndRateJsonContent)
			params.spfAndRateJsonContent = spfAndRateJsonContent
			delete params.specTableData
			delete params.lineTableData
			delete params.tableData
			if (opt.value === 'add') {
				delete params.productNumber
			}
			productSave(params).then(response => {
				if (response.code === '00000') {
					ElMessage.success('保存成功！')
					emits('query', 1)
					handleClose()
				} else {
					ElMessage.error('保存失败！')
				}
			})
		}
	})
}

/**
 * 收集保存数据
 *
 * @param array 表格中数据
 * @param spfAndRateJsonContent 需要保存内容
 */
const collectSaveData = (array: Array<any>, spfAndRateJsonContent: Array<any>) => {
	if (array && array.length > 0) {
		array.forEach(spec => {
			let temp = {
				spfName: spec.spfName,
				ratePlanAndSpfId: spec.ratePlanAndSpfId,
				rateJsonContent: {
					ratePlanName: spec.ratePlanName,
					ratePlanType: spec.ratePlanType,
					price: spec.price,
					discount: spec.discount
				}
			}
			let keyNameMap = spec.keyNameMap
			let spfJsonContent = []
			Object.keys(spec).filter(key => !fixedColArray.value.includes(key))
				.forEach(key => {
					spfJsonContent.push({
						id: key,
						name: keyNameMap[key],
						value: spec[key]
					})
				})
			temp.spfJsonContent = spfJsonContent
			spfAndRateJsonContent.push(temp)
		})
	}
}

const specSpfArray = ref<Array<SpfObj>>([])
const specSpfMap = new Map<string, []>();

/**
 * 渲染子组件表单部分信息
 */
const initSubFormData = () => {
	productSpecQuery().then(response => {
		if (response.code === '00000') {
			let data = response.data
			if (data && data.length > 0) {
				data.forEach(item => {
					let bigType = item.bigType
					let spfJson = item.spfJson
					let spfArray = JSON.parse(spfJson)
					if (spfArray && spfArray.length > 0) {
						specSpfMap.set(bigType, spfArray)
					}
				})
			}
		}
	})
}

const typeCodeMap = reactive<Map<string, any>>(new Map([
	["billMethod", ref<object>({})],
]));

onMounted(() => {
	initDictMap(typeCodeMap)
	initSubFormData()
})

defineExpose({
	init
})

</script>

<style scoped lang="scss">

</style>