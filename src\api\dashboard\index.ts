import request from "@/utils/request";
import {AxiosResponse} from "axios";

/**
 * 首页指标
 */
export function queryHomePageIndex(): Promise<AxiosResponse<any, any>> {
    return request.service({
        url: "/api/v1/risk/queryHomePageIndex",
        method: "post",
    });
}

/**
 * 查个人待办
 */
export function queryToDoList(): Promise<AxiosResponse<any, any>> {
    return request.service({
        url: "/api/v1/risk/queryToDoList",
        method: "post",
    });
}