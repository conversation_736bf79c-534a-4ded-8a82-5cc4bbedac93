<template>
	<div class="top-province-dialog">
		<el-dialog
			v-model="dialogVisible"
			class="detail-dialog"
			width="2.8rem"
			:modal="false"
			:close-on-click-modal="true"
			:close-on-press-escape="true"
			:before-close="handleClose"
			:show-close="false"
			:style="dialogStyle"
			:draggable="false"
		>
			<div class="count-list">
				<div class="title-bar">
					<span class="main-title">{{ mainTitle }}</span>
					<span class="sum-cnt">{{ sum }}个</span>
				</div>
				<div class="content-text">
					<span class="sub-title">ICT</span>
					<span class="cnt">{{ ictCount }}个</span>
				</div>
				<div class="content-text">
					<span class="sub-title">移动云</span>
					<span class="cnt">{{ cloudCount }}个</span>
				</div>
				<div class="content-text">
					<el-button link @click="rectificationList">
						<span class="sub-title" style="margin-left: 0;color: #409EFF;">待整改</span>
					</el-button>
					<span class="cnt">{{ pendingRectificationCount }}个</span>
				</div>
			</div>

		</el-dialog>
	</div>
</template>

<script setup lang="ts">

import {OrderFormData} from "@/api/order/types";
import {useRouter} from "vue-router";

const props = defineProps({
	mainTitle: {
		type: String,
		default: '',
		required: true,
	},
	ictCount: {
		type: Number,
		default: 0,
		required: true,
	},
	sum: {
		type: String,
		default: '0',
		required: true,
	},
	cloudCount: {
		type: Number,
		default: 0,
		required: true,
	},
	// 待整改
	pendingRectificationCount: {
		type: Number,
		default: 0,
		required: true,
	},
	riskMonth: {
		type: String,
		default: '',
		required: true,
	},
	provinceName: {
		type: String,
		default: '',
		required: true,
	},
})

const dialogVisible = ref<boolean>(false)
const tableData = ref([])
const opt = ref<string>('')

const form = reactive<OrderFormData>({
	productOrderNumber: '',
	operationSubType: null,
	batchNumber: '',
	prodordTime: '',
});

const clickPosition = ref({x: 0, y: 0});

// 鼠标点击坐标偏移量（根据实际情况调整）
const dialogOffset = 120;

const dialogStyle = ref({
	left: '0px',
	top: '0px'
});

const init = (optType: string, data: any) => {
	// 获取点击位置
	clickPosition.value = {
		x: event.clientX,
		y: event.clientY
	};

	// 设置对话框位置
	dialogStyle.value = {
		position: 'fixed',
		top: `${clickPosition.value.y - dialogOffset}px`,  // 向上偏移30px
		left: `${clickPosition.value.x + 10}px`, // 向右偏移10px
		margin: '0', // 清除默认居中样式
		transform: 'none' // 禁用默认的 transform 居中
	};

	dialogVisible.value = true
	opt.value = optType
	switch (optType) {
		case 'view':
			break
	}
}

const router = useRouter();

// 查看整改列表
const rectificationList = () => {
	router.push({
		path: '/correction',
		state: {
			riskMonth: props.riskMonth,
			provinceName: props.provinceName
		}
	})
}

const handleClose = (done: () => void) => {
	dialogVisible.value = false
	tableData.value = []
	done()
}

const back = () => {
	dialogVisible.value = false
	tableData.value = []
}

defineExpose({
	init
})

</script>

<style scoped lang="scss">

.top-province-dialog {
	/* 覆盖默认样式 */
	:deep(.el-dialog) {
		opacity: 1;
		border-radius: 4.5px;
		background-color: rgba(255, 255, 255, 0.8); /* 白色背景，透明度为0.8 */
		//background: linear-gradient(317deg, #FDFEFF 0%, #F4F7FC 100%);
		backdrop-filter: blur(7.5px);
		box-shadow: 0 0 12px rgb(0, 0, 0, .12);
		padding: 0;
	}

	:deep(.el-dialog__header) {
		padding: 0;
	}

	:deep(.el-dialog__body) {
		height: 2rem;
	}


	.detail-dialog {

		.count-list {
			display: flex;
			flex-direction: column;
			align-items: center;
			margin-top: 0.1rem;

			.title-bar {
				opacity: 1;
				color: #FFFFFF;
				font-family: PingFang SC;
				font-weight: bold;
				font-size: 0.18rem;
				line-height: 0.4rem;
				letter-spacing: 0;
				text-align: left;

				.main-title {
					opacity: 1;
					color: #7B7B7B;
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.18rem;
					line-height: 0.15rem;
					letter-spacing: 0;
					text-align: left;
				}

				.sum-cnt {
					opacity: 1;
					color: #3D3D3D;
					font-family: PingFang SC;
					font-weight: bold;
					font-size: 0.18rem;
					line-height: 0.15rem;
					letter-spacing: 0;
					text-align: left;
				}
			}

			.content-text {
				display: flex;
				flex-direction: row;
				justify-content: space-between;
				background: #FFFFFF;
				border-radius: 0.04rem;
				width: 2rem;
				height: 0.4rem;
				align-items: center;

				&:not(:first-child) {
					margin-top: 0.04rem;
				}

				.sub-title {
					opacity: 1;
					color: #4E5969;
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.18rem;
					line-height: 0.15rem;
					letter-spacing: 0;
					margin-left: 0.05rem;
				}

				.sub-title::before {
					content: '';
					display: inline-block;
					width: 0.08rem;
					height: 0.08rem;
					background-color: #409EFF;
					border-radius: 50%;
					margin-right: 0.06rem;
					vertical-align: middle;
				}

				.cnt {
					opacity: 1;
					color: #0079FD;
					font-family: PingFang SC;
					font-weight: 500;
					font-size: 0.18rem;
					line-height: 0.11rem;
					letter-spacing: 0;
					margin-right: 0.05rem;
				}
			}


			.detail-text {
				opacity: 1;
				color: #FFFFFF;
				font-family: PingFang SC;
				font-weight: bold;
				font-size: 0.18rem;
				line-height: 0.4rem;
				letter-spacing: 0;
				margin-top: 0.05rem;

			}
		}
	}
}

</style>
