<template>
	<div>
		<el-dialog
			v-model="dialogVisible"
			:title="title"
			top="10vh"
			width="80%"
			:close-on-click-modal="false"
			:close-on-press-escape="true"
			:before-close="handleClose"
			draggable
		>
			<el-container>
				<el-main>
					<el-card style="width: 100%;">
						<el-form
							:model="form"
							ref="formRef"
							label-width="120px"
							:rules="rules"
							:disabled="formDisabled"
						>
							<el-row :gutter="24">
								<el-col :span="12">
									<el-text
										class="mx-1"
										size="large"
										line-clamp="1"
										tag="b"
									>
										{{ baseInfoTitle }}
									</el-text>
								</el-col>
							</el-row>

							<el-row :gutter="24">
								<el-col :span="12">
									<el-form-item label="产品大类编码" prop="name">
										<el-input :disabled="opt === 'update'" v-model="form.name" clearable></el-input>
									</el-form-item>
								</el-col>
							</el-row>

							<el-row :gutter="24">
								<el-col :span="12">
									<el-form-item label="产品大类名称" prop="value">
										<el-input v-model="form.value" clearable></el-input>
									</el-form-item>
								</el-col>
							</el-row>
						</el-form>
					</el-card>
				</el-main>
			</el-container>
			<template #footer>
				<div style="text-align: center;">
					<el-button plain @click="back">取消</el-button>
					<el-button type="primary" @click="save(formRef)" v-if="opt !== 'view'">保存</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import {CategoriesAddReq, PmProductCateBean, ProductClassInfo} from "@/api/product/types";
import {initDictMap} from "@/utils/commonUtils";
import {FormInstance, FormRules} from "element-plus";
import {categoriesAdd, categoriesQueryOne, categoriesUpdate} from "@/api/product";

const dialogVisible = ref<boolean>(false)
const title = ref<string>('规格详情')
const baseInfoTitle = ref<string>('基本信息')
const opt = ref<string>('')
const formDisabled = ref<boolean>(false)


const form = reactive<PmProductCateBean>({});

const rules = reactive<FormRules<ProductClassInfo>>({
	value: [
		{required: true, message: '请输入产品大类编码', trigger: 'blur'},
	],
	name: [
		{required: true, message: '请输入产品大类名称', trigger: 'blur'},
	],
})

const init = (optType: string, data: any) => {
	dialogVisible.value = true
	opt.value = optType
	switch (optType) {
		case 'add':
			title.value = '新增'
			formDisabled.value = false
			break
		case 'update':
			title.value = '编辑'
			formDisabled.value = false
			singleInfo(data)
			break
		case 'view':
			formDisabled.value = true
			title.value = '详情'
			singleInfo(data)
			break
	}
}

const singleInfo = (data: any) => {
	let params = {
		id: data.id
	}
	categoriesQueryOne(params).then(response => {
		let respData = response.data
		if (respData) {
			form.name = respData.name
			form.value = respData.value
			form.id = respData.id
		}
	})
}

const formRef = ref<FormInstance>()
const handleClose = (done: () => void) => {
	dialogVisible.value = false
	resetForm(formRef.value)
	done()
}

const resetForm = (formEl: FormInstance | undefined) => {
	if (!formEl) return
	formEl.resetFields()
}


const back = () => {
	dialogVisible.value = false
	resetForm(formRef.value)
}


let emits = defineEmits(['query']);

const save = async (formEl: FormInstance | undefined) => {
	if (!formEl) return
	await formEl.validate((valid, fields) => {
		if (valid) {
			let params: any = {
				name: form.name,
				value: form.value,
			}
			let promise = null;
			if (opt.value === 'add') {
				promise = categoriesAdd(params);
			} else if (opt.value === 'update') {
				params.id = form.id
				promise = categoriesUpdate(params)
			}
			if (promise) {
				promise.then(response => {
						if (response.code === '00000') {
							ElMessage.success('保存成功！')
							emits('query', 1)
							back()
						} else {
							ElMessage.error('保存失败！')
						}
					}
				)
			}
		}
	})
}


const typeCodeMap = reactive<Map<string, any>>(new Map([
	["billMethod", ref<object>({})],
]));

onMounted(() => {
	initDictMap(typeCodeMap)
})

defineExpose({
	init
})

</script>

<style scoped lang="scss">

</style>