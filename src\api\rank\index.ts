import request from "@/utils/request";
import {RankForm} from "@/api/rank/types";

/**
 * 获GPU卡算力排行榜管理列表
 */
export function queryGpuInfoList() {
    return request.service({
        url: "/api/v1/manage/gpuInfo",
        method: "get",
    });
}

/**
 * 根据ID获取单条数据
 * @param id
 */
export function getGpuInfoById(id: string) {
    return request.service({
        url: `/api/v1/manage/getGpuInfoById/${id}`,
        method: "get",
    });
}

export function gpuInfoAdd(params: RankForm) {
    return request.service({
        url: "/api/v1/manage/gpuInfoAdd",
        method: "post",
        data: params,
    });
}

export function gpuInfoUpdate(params: RankForm) {
    return request.service({
        url: "/api/v1/manage/gpuInfoUpdate",
        method: "put",
        data: params,
    });
}