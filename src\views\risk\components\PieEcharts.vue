<template>
	<div class="echarts-box">
		<div ref="echartsRef" class="charts"></div>
	</div>
</template>
<script setup>
import {ref, onMounted, onBeforeUnmount, watch, nextTick, markRaw} from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
	// 图表配置
	data: {
		type: Object,
		default: () => {
		},
	},
});
const echartsRef = ref();

let dom = null;

//设置图表配置
const setOptions = (options) => {
	//清除画布
	dom && dom.clear();
	//重新渲染
	dom && dom.setOption(options);
};


watch(
	() => props.data,
	(val) => {
		nextTick(() => {
			//默认关闭动画
			setOptions({animation: false, ...val});
		});
	},
	{deep: true, immediate: true}
);
const emits = defineEmits(['click'])
onMounted(() => {
	//初始化
	dom = markRaw(echarts.init(echartsRef.value));
	//点击事件
	dom.on('click', (param) => {
		emits('click', param)
	})
});
onBeforeUnmount(() => {
	//离开销毁
	echarts.dispose(dom);
	dom = null;
});

defineExpose({
	setOptions,
});
</script>
<style lang="scss" scoped>
.echarts-box {
	width: 100%;
	height: 100%;
	box-sizing: border-box;
}

.charts {
	width: 100%;
	height: 100%;
}
</style>


