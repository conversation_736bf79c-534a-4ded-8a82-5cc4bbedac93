/** 全局SCSS变量 */

:root {
  --menu-background: #239cDD;
  --menu-text: #e0e8f0;
  --menu-active-text: #f9fafb;
  --menu-hover: #e7ecf3;
  --sidebar-logo-background: #239cDD;
  --foot-background: #304156;
}

/** 暗黑主题 */
html.dark {
  --menu-background: var(--el-bg-color-overlay);
  --menu-text: #fff;
  --menu-active-text: var(--el-menu-active-color);
  --menu-hover: #239cDD;
  --sidebar-logo-background: rgb(0 0 0 / 20%);
}

$menu-background: var(--menu-background); // 菜单背景色
$menu-text: var(--menu-text); // 菜单文字颜色
$menu-active-text: var(--menu-active-text); // 菜单激活文字颜色
$menu-hover: var(--menu-hover); // 菜单悬停背景色
$sidebar-logo-background: var(--sidebar-logo-background); // 侧边栏 Logo 背景色
$foot-background: var(--foot-background); // 页脚背景色

$sidebar-width: 210px; // 侧边栏宽度
$sidebar-width-collapsed: 54px; // 侧边栏收缩宽度
$navbar-height: 50px; // 导航栏高度
$tags-view-height: 34px; // TagsView 高度
