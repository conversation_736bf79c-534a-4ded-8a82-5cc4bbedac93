<template>
	<div id="enterprise-info" class="enterprise-info-page">
		<el-card style="width: 100%;">
			<template #header>
				<el-text
					class="mx-1"
					size="large"
					line-clamp="1"
					tag="b"
				>
					{{ orderTitle }}
				</el-text>
			</template>
			<el-container>
				<el-main>
					<el-form
						ref="ruleFormRef"
						:model="ruleForm"
						label-width="auto"
						class="demo-ruleForm"
						size="default"
						status-icon
						label-position="left"
						:disabled="formDisabled"
					>

						<el-row :gutter="24">
							<el-col :offset="2" :span="18">
								<el-form-item>
									<template #label>
										<span class="service-title">{{ enterpriseCertificationInfoText }}</span>
									</template>
								</el-form-item>
							</el-col>
						</el-row>

						<el-row :gutter="24">
							<el-col :offset="2" :span="18">
								<el-form-item label="统一社会信用代码" prop="creditCode">
									<el-input
										v-model="ruleForm.creditCode"
										clearable
									></el-input>
								</el-form-item>
							</el-col>
						</el-row>

						<el-row :gutter="24">
							<el-col :offset="2" :span="18">
								<el-form-item label="企业名称" prop="enterpriseName">
									<el-input
										v-model="ruleForm.enterpriseName"
										clearable
									></el-input>
								</el-form-item>
							</el-col>
						</el-row>

						<el-row :gutter="24">
							<el-col :offset="2" :span="18">
								<el-form-item label="企业类型" prop="enterpriseType">
									<dictionary
										v-model="ruleForm.enterpriseType"
										type-code="enterpriseType"
									></dictionary>
								</el-form-item>
							</el-col>
						</el-row>

						<el-row :gutter="24">
							<el-col :offset="2" :span="18">
								<el-form-item label="法人代表姓名" prop="legalRepresentative">
									<el-input
										v-model="ruleForm.legalRepresentative"
										clearable
									></el-input>
								</el-form-item>
							</el-col>
						</el-row>
						<el-row :gutter="24">
							<el-col :offset="2" :span="18">
								<el-form-item label="法人代表身份证" prop="legalRepresentativeId">
									<el-input
										v-model="ruleForm.legalRepresentativeId"
										clearable
									></el-input>
								</el-form-item>
							</el-col>
						</el-row>
						<el-row :gutter="24">
							<el-col :offset="2" :span="18">
								<el-form-item label="联系人姓名" prop="liaison">
									<el-input
										v-model="ruleForm.liaison"
										clearable
									></el-input>
								</el-form-item>
							</el-col>
						</el-row>
						<el-row :gutter="24">
							<el-col :offset="2" :span="18">
								<el-form-item label="联系人电话" prop="liaisonPhone">
									<el-input
										v-model="ruleForm.liaisonPhone"
										clearable
									></el-input>
								</el-form-item>
							</el-col>
						</el-row>
						<el-row :gutter="24">
							<el-col :offset="2" :span="18">
								<el-form-item label="联系人邮箱" prop="liaisonEmail">
									<el-input
										v-model="ruleForm.liaisonEmail"
										clearable
									></el-input>
								</el-form-item>
							</el-col>
						</el-row>

						<el-row :gutter="24">
							<el-col :offset="2" :span="18">
								<el-form-item>
									<template #label>
										<span class="service-title">{{ payInfoText }}</span>
									</template>
								</el-form-item>
							</el-col>
						</el-row>

						<el-row :gutter="24">
							<el-col :offset="2" :span="18">
								<el-form-item label="开户名" prop="accountName">
									<el-input
										v-model="ruleForm.accountName"
										clearable
									></el-input>
								</el-form-item>
							</el-col>
						</el-row>
						<el-row :gutter="24">
							<el-col :offset="2" :span="18">
								<el-form-item label="税号" prop="taxNumber">
									<el-input
										v-model="ruleForm.taxNumber"
										clearable
									></el-input>
								</el-form-item>
							</el-col>
						</el-row>
						<el-row :gutter="24">
							<el-col :offset="2" :span="18">
								<el-form-item label="单位地址" prop="address">
									<el-input
										v-model="ruleForm.address"
										clearable
									></el-input>
								</el-form-item>
							</el-col>
						</el-row>
						<el-row :gutter="24">
							<el-col :offset="2" :span="18">
								<el-form-item label="开户银行" prop="accountBank">
									<el-input
										v-model="ruleForm.accountBank"
										clearable
									></el-input>
								</el-form-item>
							</el-col>
						</el-row>
						<el-row :gutter="24">
							<el-col :offset="2" :span="18">
								<el-form-item label="银行账号" prop="bankAccount">
									<el-input
										v-model="ruleForm.bankAccount"
										clearable
									></el-input>
								</el-form-item>
							</el-col>
						</el-row>
					</el-form>
				</el-main>
			</el-container>
		</el-card>
	</div>
</template>

<script setup lang="ts">
import {RegisterInfo} from "@/api/user/types";
import {getEnterpriseInfo} from "@/api/user";

const orderTitle = ref<string>('账户信息')
const enterpriseCertificationInfoText = ref<string>('企业认证信息')
const payInfoText = ref<string>('收付款信息')
const ruleForm = ref<RegisterInfo>({})
const formDisabled = ref<boolean>(true)

// 初始化注册人员的企业信息
const initEnterpriseInfo = () => {
	getEnterpriseInfo().then(response => {
		ruleForm.value = response.data
	})
}

onMounted(() => {
	initEnterpriseInfo()
})

</script>

<style scoped lang="scss">
.enterprise-info-page {
	.service-title {
		padding-left: 12px;
		position: relative;
		padding-top: 3px;
		font-weight: bold;
		font-size: large;
	}

	.service-title::before {
		content: "";
		position: absolute;
		top: 8px;
		left: 0;
		display: inline-block;
		width: 6px;
		height: 22px;
		background: #409EFF;
		border-radius: 2px;
	}
}
</style>