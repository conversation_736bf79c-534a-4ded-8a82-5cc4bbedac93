<template>
	<div>
		<el-card style="width: 100%;">
			<template #header>
				<el-text
					class="mx-1"
					size="large"
					line-clamp="1"
					tag="b"
				>
					{{ orderTitle }}
				</el-text>
			</template>

			<bar-chart
				id="barChart"
				height="400px"
				width="100%"
				class="bg-[var(--el-bg-color-overlay)]"
				:chartData="chartData"
			/>

			<el-container class="mt-10">
				<el-header>
					<el-form :model="queryForm" ref="queryFormRef" label-width="auto">
						<el-row :gutter="24">
							<el-col :span="5">
								<el-form-item label="订购关系ID" prop="subsId">
									<el-input
										v-model="queryForm.subsId"
										placeholder="请输入订单编号"
										clearable>
									</el-input>
								</el-form-item>
							</el-col>
							<el-col :span="5">
								<el-form-item label="客户编码" prop="userCode">
									<el-input
										v-model="queryForm.userCode"
										placeholder="请输入客户编码"
										clearable>
									</el-input>
								</el-form-item>
							</el-col>
							<el-col :span="5">
								<el-form-item label="产品名称" prop="productName">
									<el-input
										v-model="queryForm.productName"
										placeholder="请输入产品名称"
										clearable>
									</el-input>
								</el-form-item>
							</el-col>
							<el-col :span="5">
								<el-form-item label="账期" prop="billMonth">
									<el-date-picker
										v-model="queryForm.billMonth"
										type="month"
										placeholder="选择账期"
									/>
								</el-form-item>
							</el-col>
							<el-col :span="4">
								<el-button type="primary" @click="query(1)">查询</el-button>
								<el-button plain @click="resetForm(queryFormRef)">重置</el-button>
							</el-col>
						</el-row>
					</el-form>
				</el-header>
				<el-main>
					<el-table
						v-loading="loading"
						:data="tableData"
						border
						height="450"
						style="width: 100%"
					>
						<el-table-column prop="billMonth" label="账期" align="center" width="200"/>
						<el-table-column prop="customerNumber" label="客户编码" align="center" width="210" />
						<el-table-column prop="customerName" label="客户名称" align="center"/>
						<el-table-column prop="subsId" label="订购关系ID" align="center"/>
						<el-table-column prop="productName" label="产品名称" align="center"/>
						<el-table-column prop="totalFeeBeforeDis" label="总金额（元）" align="center"/>
						<el-table-column prop="discountFee" label="优惠金额（元）" align="center"/>
						<el-table-column prop="totalFeeAfterDis" label="实际金额（元）" align="center"/>
					</el-table>
				</el-main>
				<el-footer>
					<el-pagination
						style="float: right;line-height: 60px"
						v-model:current-page="current"
						v-model:page-size="size"
						:page-sizes="[10, 50, 100, 200]"
						:small="false"
						:background="true"
						size="default"
						layout="total, sizes, prev, pager, next, jumper"
						:total="total"
						@size-change="handleSizeChange"
						@current-change="handleCurrentChange"
					/>
				</el-footer>
			</el-container>

		</el-card>
	</div>
</template>

<script setup lang="ts">
import {FormInstance} from "element-plus";
import BarChart from '@/views/operator/bill/components/BarChart.vue'
import {queryBills, queryBillsByMonth} from "@/api/bill";
import {BillInfoQuery} from "@/api/bill/types";
import moment from "moment";

const orderTitle = ref('账单管理')

const queryForm = ref<BillInfoQuery>({
	subsId: '',
	userCode: '',
	productName: '',
	billMonth: null,
});

const tableData = ref([]);

const chartData = ref<Array<object>>([])

const loading = ref(false)
const current = ref<number>(1)
const size = ref<number>(10)
const total = ref<number>(0)

const handleSizeChange = (val: number) => {
	size.value = val
	query()
}
const handleCurrentChange = (val: number) => {
	current.value = val
	query(val)
}

const query = (index?: number) => {
	if (index) {
		current.value = index
	}
	let data: BillInfoQuery = {
		...queryForm.value,
		extra: {
			currentPage: current.value,
			pageSize: size.value
		}
	}
	if (data.billMonth) {
		data.billMonth = moment(data.billMonth).format('YYYYMM')
	}

	loading.value = true
	queryBills(data).then(response => {
		let pageData = response.data
		if (pageData) {
			tableData.value = pageData.bill
			let extra = pageData.extra;
			current.value = extra.currentPage
			size.value = extra.pageSize
			total.value = extra.totalRow
		}
	}).finally(() => {
		loading.value = false
	})
}

const queryFormRef = ref<FormInstance>()

const resetForm = (formEl: FormInstance | undefined) => {
	if (!formEl) return
	formEl.resetFields()
}

const queryChartData = () => {
	queryBillsByMonth().then(response => {
		chartData.value = response.data
	})
}

onBeforeMount(() => {
	queryChartData()
})

onMounted(() => {
	query()
})

</script>

<style scoped lang="scss">

</style>