import request from "@/utils/request";
import {AxiosPromise} from "axios";
import {WorkOrderQuery} from "@/api/workOrder/types";

/**
 * 工单查询
 */
export function workOrderQuery(queryParams: WorkOrderQuery): AxiosPromise<any> {
    return request.service({
        url: "/api/v1/sync/EBOSS/WorkOrderQuery",
        method: "post",
        data: queryParams,
    });
}

/**
 * 工单信息处理
 *
 * @param opertationId
 */
export function workOrderDeal(opertationId: string): AxiosPromise<any> {
    return request.service({
        url: `/api/v1/sync/EBOSS/WorkOrderDeal?opertationId=${opertationId}`,
        method: "get",
    });
}


