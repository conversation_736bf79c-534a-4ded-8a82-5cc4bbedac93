export default {
  // 路由国际化
  route: {
    dashboard: "首页",
    document: "项目文档",
  },
  // 登录页面国际化
  login: {
    username: "用户名",
    password: "密码",
    login: "登 录",
    dhaa:"还没有账号？",
    regNow:"立即注册",
    captchaCode: "验证码",
    capsLock: "大写锁定已打开",
    
    message: {
      username: {
        required: "请输入用户名",
      },
      password: {
        required: "请输入密码",
        min: "密码不能少于6位",
      },
      captchaCode: {
        required: "请输入验证码",
      },
    },
  },

// 登录页面国际化
register: {
  username: "请输入用户名（2-32个字符，仅支持数字、字母、下划线）",
  password: "登录密码",
  confirmPassword: "确认密码", 
  register: "注 册",
  return:"返 回",
  to_login:"去登录",
  to_Portal:"返回门户",
  ahaa:"已有账号？",
  title:"用户注册",
  captchaCode: "验证码",
  capsLock: "大写锁定已打开",
  nickname:"真实姓名 （2-32个字符，仅支持中文汉字）",
  companyName:"单位名称（2-32个字符，仅支持中文汉字）",
  email:"单位电子邮箱（不可使用个人邮箱）",
  mobile:"手机号",
  message: {
    username: {
      required: "请输入用户名",
      length: "用户名不能少于2位字符,不能多于32位字符",
      pattern:"用户名只支持数字、字母、下划线",
    },
    password: {
      required: "请输入密码",
      length: "密码不能少于6位字符,不能多于32位字符",
    },
    confirmPassword: {
      required: "请输入确认密码",
      consistency: "两次输入密码不一致",      
    },
    nickname: {
      required: "请输入真实姓名",
      length: "姓名不能少于2位字符,不能多于32位字符",
      CHI:"姓名请输入中文"
    },
    companyName:{
      required: "请输入单位名称",
      length: "单位名称不能少于2位字符,不能多于32位字符",
      CHI:"单位名称请输入中文",
    },
    email:{

      required: "请输入单位电子邮箱",
      pattern:"请输入正确的邮箱地址",
    },
    mobile:{

      required: "请输入手机号",
      pattern: "请输入正确的手机号",
    },

    captchaCode: {
      required: "请输入验证码",
    },
  },
},


  // 导航栏国际化
  navbar: {
    dashboard: "首页",
    logout: "注销登出",
    document: "项目文档",
    gitee: "项目地址",
  },
  sizeSelect: {
    tooltip: "布局大小",
    default: "默认",
    large: "大型",
    small: "小型",
    message: {
      success: "切换布局大小成功！",
    },
  },
  langSelect: {
    message: {
      success: "切换语言成功！",
    },
  },
  settings: {
    project: "项目配置",
    theme: "主题设置",
    interface: "界面设置",
    navigation: "导航设置",
    themeColor: "主题颜色",
    tagsView: "开启 Tags-View",
    fixedHeader: "固定 Header",
    sidebarLogo: "侧边栏 Logo",
    watermark: "开启水印",
  },
};
