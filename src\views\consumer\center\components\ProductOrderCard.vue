<template>
	<div class="card">
		<div class="per-row">
			<el-image :src="src"
			          class="recent-card-pic"
			          fit="fill"
			>
			</el-image>
		</div>
		<div>
			<el-text
				class="mx-1"
				type="info"
				size="default"
				line-clamp="1"
			>
				商品：
			</el-text>
			<el-text
				class="mx-1"
				size="large"
				line-clamp="1"
				tag="b"
			>
				{{ productName }}
			</el-text>
		</div>
		<div v-if="orderDuration">
			<el-text
				class="mx-1"
				type="info"
				size="default"
				line-clamp="1"
			>
				订购时长：
			</el-text>
			<el-text
				class="mx-1"
				size="large"
				line-clamp="1"
				tag="b"
			>
				{{ orderDuration }}
			</el-text>
		</div>
		<div v-if="orderTime">
			<el-text
				class="mx-1"
				type="info"
				size="default"
				line-clamp="1"
			>
				订购时间：
			</el-text>
			<el-text
				class="mx-1"
				size="large"
				line-clamp="1"
				tag="b"
			>
				{{ orderTime }}
			</el-text>
		</div>
		<div v-if="expirationDate">
			<el-text
				class="mx-1"
				type="info"
				size="default"
				line-clamp="1"
			>
				到期时间：
			</el-text>
			<el-text
				class="mx-1"
				size="large"
				line-clamp="1"
				tag="b"
			>
				{{ expirationDate }}
			</el-text>
		</div>
	</div>
</template>

<script setup lang="ts">

const props = defineProps({
	src: {
		type: String,
		default: "",
	},
	productName: {
		type: String,
		default: "",
		required: true,
	},
	orderDuration: {
		type: String,
		default: "",
	},
	orderTime: {
		type: String,
		default: "",
	},
	expirationDate: {
		type: String,
		default: "",
	},
});


</script>

<style scoped lang="scss">

.card {
	display: flex;
	flex-direction: column;
	justify-content: center;
	padding: 20px;
	background: rgba(246, 249, 252, 1);

	.per-row {
		text-align: center;

		.recent-card-pic {
			height: 100px;
			width: 100px;
			padding: 20px;
		}
	}

}
</style>