<template>
	<div class="about-us">
		<div class="banner-main">
			<div class="banner-text">
				<el-text
					class="mx-1 banner-title"
					size="large"
					line-clamp="1"
					tag="b"
				>
					{{ bannerTitle }}
				</el-text>
				<el-text
					class="mx-1 banner-desc"
					size="large"
					line-clamp="1"
				>
					{{ bannerDesc }}
				</el-text>
			</div>
			<el-image
				:src="getAssetsFile('product/banner.png')"
				style="height: 100%;width: 100%"
				fit="fill"
			>
			</el-image>
		</div>

		<div class="product-title">
			<span class="service-title">{{ introductionTitle }}</span>
		</div>

		<div class="brief-model">
			<el-row :gutter="24" class="brief-introduction-main">
				<el-col :span="15">
					<el-text
						class="mx-1 introduction-content"
						size="large"
						line-clamp="8"
					>
						{{ introduction }}
					</el-text>
					<el-text
						style="margin-top: 15px;"
						class="mx-1 introduction-content"
						size="large"
						line-clamp="8"
					>
						{{ firstParagraph }}
					</el-text>
					<el-text
						style="margin-top: 15px;"
						class="mx-1 introduction-content"
						size="large"
						line-clamp="8"
					>
						{{ secondParagraph }}
					</el-text>
					<el-text
						style="margin-top: 15px;"
						class="mx-1 introduction-content"
						size="large"
						line-clamp="8"
					>
						{{ thirdParagraph }}
					</el-text>
				</el-col>
				<el-col :span="9" style="text-align: center;">
					<el-image
						:src="getAssetsFile('about/introduce_pic.jpg')"
						class="introduction-pic"
						fit="fill"
					>
					</el-image>
				</el-col>
			</el-row>
		</div>

		<div class="product-title">
			<span class="service-title">{{ promotionalVideoTitle }}</span>
		</div>

		<div class="video-list">
			<video
				class="video-real"
				controls
				preload="metadata"
				height="100%"
				:src="getAssetsFile('about/promotional_video.mp4')"
				type="video/mp4"
			>
			</video>
		</div>
	</div>
</template>

<script setup lang="ts">

import {getAssetsFile, getNewArray} from "@/utils/commonUtils";

const bannerTitle = ref<String>('服务愿景')
const bannerDesc = ref<String>('网络无所不达，算力无处不在')
const introductionTitle = ref<String>('简介')

const introduction = ref<String>('汕头移动的基本情况：')
const firstParagraph = ref<string>('1、机房优势：广东汕头数据中心是广东移动布局的六大数据中心之一，总投资超30亿元，采用国际T3+建设标准，CDCC认证五星级数据中心，汇聚中国移动IT云南方节点、全国物联网南方节点、移动云广东节点等重要节点，是目前粤东规模最大、等级最高的数据中心，达到99.9%的SLA服务级别保障，目前已投产数字广东政务云汕头节点、省容灾节点，亚青云，视频云、IT云、移动云省内节点等多个大型云底座项目具备完善的规划、建设、运维能力。')
const secondParagraph = ref<string>('2、网络优势：汕头是中国移动全国政企专网的节点城市，实现一跳进入全国骨干网；拥有全国唯三之一的SJC国际海缆出口，实现全业务在汕头一跳出海；汕头国际互联网出口可直达全球7个海外数据中心节点，承载中国移动华南地区的国际互联网出口流量；汕头是中央网信办特批的全国唯一试点IPV6合规出海通道；汕头本地启动三网节点，按照5：3：2，移动是5这个规划来做。')
const thirdParagraph = ref<string>('3、地理位置优势：汕头是中国著名的侨乡，海上丝绸之路的重要门户，跟东南亚国家有很多文化交流与输出。')


const promotionalVideoTitle = ref<String>('宣传视频')

const videoArray = ref([
	{
		type: 1,
		src: 'about/promotional_video.mp4'
	},

])

const videoSplitRowArray = ref()

onMounted(() => {
	videoSplitRowArray.value = getNewArray(videoArray.value, 3);
})


</script>

<style scoped lang="scss">
.about-us {
	margin-bottom: 15px;

	.banner-main {
		height: 420px;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;

		.banner-text {
			position: absolute;
			z-index: 100;
			text-align: center;

			.banner-title {
				display: block;
				font-size: 48px;
				color: white;
				font-weight: bold;
			}

			.banner-desc {
				display: block;
				font-size: 36px;
				color: white;
				font-weight: bold;
			}
		}
	}

	.product-title {
		margin-top: 105px;
		padding-left: 75px;

		.service-title {
			padding-left: 12px;
			position: relative;
			padding-top: 3px;
			font-size: 18px;
			font-weight: bold;
		}

		.service-title::before {
			content: "";
			position: absolute;
			top: 4px;
			left: 0;
			display: inline-block;
			width: 6px;
			height: 18px;
			background: #409EFF;
			border-radius: 2px;
		}
	}

	.brief-model {
		width: 100%;

		.brief-introduction-main {
			margin: 0 auto !important;
			max-width: 1440px;
			height: 400px;
			padding: 15px 75px;

			.introduction-content {
				text-indent: 2em;
				display: block;

			}

			.introduction-pic {
				height: 100%;
				width: 100%;
			}
		}
	}

	.video-list {
		height: 500px;
		padding: 15px 75px;
		display: flex;
		flex-wrap: wrap;
		justify-content: center;

		.video-real {
			width: 800px;
			height: 100%;
			object-fit: fill;
		}

		.video-card {
			width: 400px;
			height: 268px;
			background: #1b2a47;
			display: flex;
			justify-content: center;
			align-items: center;
			flex-direction: column;

			.title {
				color: white;
				display: block;
				font-size: 36px;

			}

			.subtitle {
				color: white;
				display: block;
				font-size: 36px;
			}
		}
	}
}
</style>