<template>
	<div class="product-order">
		<el-card style="max-width: 100%">
			<template #header>
				<div class="card-header">
					<el-text
						class="mx-1"
						size="large"
						line-clamp="1"
						tag="b"
					>
						{{ feeTitle }}
					</el-text>
				</div>
			</template>
			<el-form :model="feeQueryForm" label-width="auto">
				<el-row :gutter="24">
					<el-col :span="8">
						<el-form-item label="计费方式">
							<dictionary
								:clearable="false"
								v-model="feeQueryForm.billingMethod"
								type-code="billMethod"
								@change="billingMethodChange"
							></dictionary>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
		</el-card>

		<el-card class="specifications-card">
			<template #header>
				<div class="card-header">
					<el-text
						class="mx-1"
						size="large"
						line-clamp="1"
						tag="b"
					>
						{{ specificationsTitle }}
					</el-text>
				</div>
			</template>
			<el-table
				:data="tableData"
				border
				style="width: 100%"
				highlight-current-row
				@current-change="handleCurrentChange"
			>
				<el-table-column width="55">
					<template #default="scope">
						<el-radio
							:label="scope.row.specId"
							v-model="specId"
							@change="specChange"
						><span></span></el-radio>
					</template>
				</el-table-column>
				<el-table-column
					v-for="item in tableHeadField"
					:prop="item.id"
					align="center"
					:label="item.name"
				></el-table-column>
			</el-table>
		</el-card>

		<el-card class="specifications-card">
			<template #header v-if="route.query.productClass === ProductClassEnum.GPU">
				<div class="card-header">
					<el-text
						class="mx-1"
						size="large"
						line-clamp="1"
						tag="b"
					>
						{{ lineTitle }}
					</el-text>
				</div>
			</template>
			<!--线路可选开关-->
			<el-row :gutter="24" v-if="route.query.productClass === ProductClassEnum.GPU">
				<el-col :span="2">
					<el-text
						style="line-height: 32px;"
						class="mx-1"
						size="default"
						line-clamp="1"
					>
						{{ isChooseLineText }}
					</el-text>
				</el-col>
				<el-col :span="6">
					<dictionary
						v-model="isChooseLine"
						:clearable="false"
						@change="chooseLineChange"
						type-code="whether"/>
				</el-col>
			</el-row>
			<!--表格-->
			<el-row :gutter="24" style="margin-top: 15px;"
			        v-if="route.query.productClass === ProductClassEnum.GPU && isChooseLine == 1">
				<el-col :span="23">
					<el-form-item>
						<el-table
							:data="lineTableData"
							border
							style="width: 100%"
						>
							<el-table-column width="55">
								<template #default="scope">
									<el-radio
										:label="scope.row.specId"
										v-model="lineSpecId"
										@change="lineSpecChange"
									><span></span></el-radio>
								</template>
							</el-table-column>
							<el-table-column
								v-for="item in lineTableHeadField"
								:prop="item.id"
								align="center"
								:label="item.name"
							></el-table-column>
						</el-table>
					</el-form-item>
				</el-col>
			</el-row>
			<!--购买数量和时间-->
			<el-row :gutter="24" style="margin-top: 15px;">
				<el-col :span="6">
					<el-form-item label="购买数量">
						<el-input-number
							v-model="lineForm.purchaseQuantity"
							@change="purchaseQuantityChange"
							:min="1"/>
						<el-text
							style="margin-left: 5px;"
							class="mx-1"
							size="default"
							line-clamp="1"
						>台
						</el-text>
					</el-form-item>
				</el-col>
				<el-col :span="6">
					<el-form-item>
						<el-input-number
							v-model="lineForm.purchaseDuration"
							@change="purchaseDurationChange"
							:min="1"/>
						<el-text
							style="margin-left: 5px;"
							class="mx-1"
							size="default"
							line-clamp="1"
						> {{ feeQueryForm.billingMethod === 'F' ? '月' : '年' }}
						</el-text>
					</el-form-item>
				</el-col>
				<el-col :span="6">
					<el-form-item label="费用合计：">
						<el-text
							class="mx-1"
							size="large"
							line-clamp="1"
							type="danger"
							tag="b"
						>¥ {{ feeValue }}
						</el-text>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="24" class="confirm-row">
				<el-col :span="2">
					<el-button type="primary" @click="confirm">确认开通</el-button>
				</el-col>
			</el-row>
		</el-card>
	</div>
</template>

<script setup lang="ts">

import {accountSpecification, orderSync, productQueryforOrder} from "@/api/product";
import {InquiryQueryParams} from "@/api/product/types";
import {ProductClassEnum} from "@/enums/ProductClassEnum";

const router = useRouter();
const route = useRoute();

const feeTitle = ref<string>('计费')
const specificationsTitle = ref<string>('规格')
const lineTitle = ref<string>('线路（可选）')

const feeQueryForm = ref({
	billingMethod: 'F',
})

const lineForm = ref({
	purchaseQuantity: 1,
	purchaseDuration: 1,
})

const tableData = ref<Array<any>>([])

const isChooseLineText = ref<string>('是否选择线路：')
const isChooseLine = ref<number>(2)
const feeValue = ref<number>(0)

const lineTableData = ref<Array<any>>([])
// 选择的规格ID
const specId = ref<string | null>()
// 选择的线路ID
const lineSpecId = ref<string | null>()

const billingMethodChange = (value: any) => {
	queryChooseLineList()
}

// 规格ID对应的行数据
const specMap = new Map<string, any>()
// 线路ID对应的行数据
const lineMap = new Map<string, any>()

const tableHeadField = ref<Array<any>>([])
const lineTableHeadField = ref<Array<any>>([])

const queryChooseLineList = () => {
	if (route.query.productNumber && route.query.productClass) {
		let data = {
			ratePlanType: feeQueryForm.value.billingMethod,
			productNumber: route.query.productNumber,
			productClass: route.query.productClass,
		}
		productQueryforOrder(data).then(response => {
			let array = response.data
			tableData.value = []
			lineTableData.value = []
			specMap.clear()
			lineMap.clear()
			if (array && array.length > 0) {
				array.forEach((item: any) => {
					let productClass = item.productClass
					let specInfoArray = item.specInfo
					if (specInfoArray && specInfoArray.length > 0) {
						let specInfo = specInfoArray[0].specInfo
						if (specInfo) {
							let parse = JSON.parse(specInfo)
							if (productClass === ProductClassEnum.LINE) {
								if (lineTableHeadField.value.length === 0) {
									lineTableHeadField.value = parse
								}
							} else {
								if (tableHeadField.value.length === 0) {
									tableHeadField.value = parse
								}
							}
						}
					}
				})
			}
			for (let i = 0; i < array.length; i++) {
				let item = array[i]
				let productClass = item.productClass
				let productNumber = item.productNumber
				let paramInfo: Array<object> = item.paramInfo
				let ratePlanInfo: Array<object> = item.ratePlanInfo
				let specInfoArray: Array<any> = item.specInfo
				if (specInfoArray && specInfoArray.length > 0) {
					specInfoArray.forEach(specItem => {
						let specId = specItem.specId
						let specName = specItem.specName
						let specCode = specItem.specCode
						let specInfoJson = specItem.specInfo;
						let specAndRatePlan = specItem.specAndRatePlan;
						let specInfo;
						if (specInfoJson) {
							specInfo = JSON.parse(specInfoJson)
							if (specInfo && specInfo.length > 0) {
								let temp: any = {
									specId: specId
								}
								specInfo.forEach((item: any) => {
									temp[item.id] = item.value
								})
								if (productClass === ProductClassEnum.LINE) {
									lineTableData.value.push(temp)
								} else {
									tableData.value.push(temp)
								}
							}
						}
						let obj: object = {
							specId: specId,
							specName: specName,
							specCode: specCode,
							productNumber: productNumber,
							paramInfo: paramInfo,
							ratePlanInfo: ratePlanInfo,
							specAndRatePlan: specAndRatePlan,
							//...specInfo,
						}
						if (productClass === ProductClassEnum.LINE) {
							lineMap.set(specId, obj)
						} else {
							specMap.set(specId, obj)
						}
					})
				}
			}
		})
	}
}

const confirm = () => {
	if (!specId.value) {
		ElMessage.warning('请先选择规格！')
		return
	}
	if (isChooseLine.value == 1 && !lineSpecId.value) {
		ElMessage.warning('请先选择线路！')
		return
	}
	let selectSpecRowData = specMap.get(specId.value);
	let specObj = getSpecObj(selectSpecRowData)
	let selectLineSpecRowData: any;
	if (lineSpecId.value) {
		selectLineSpecRowData = lineMap.get(lineSpecId.value);
	}
	let lineSpecObj = getSpecObj(selectLineSpecRowData)
	let subProductOrderInfo: Array<object> = [
		specObj
	]
	if (lineSpecObj && Object.keys(lineSpecObj).length > 0) {
		subProductOrderInfo.push(lineSpecObj)
	}
	let params = {
		subProductOrderInfo: subProductOrderInfo
	}
	orderSync(params).then((response: any) => {
		let code = response.code;
		if (code === '00000') {
			router.push({
				path: '/product/result',
				query: {
					title: feeTitle.value,
					flag: 'success'
				}
			});
		} else {
			router.push({
				path: '/product/result',
				query: {
					title: feeTitle.value,
					flag: 'failed'
				}
			});
		}
	})
}

const getSpecObj = (selectSpecRowData: any) => {
	let specObj = {}
	let productOrderCharacter: object[] = []
	let productOrderRatePlan = []
	if (selectSpecRowData) {
		let specAndRatePlan = selectSpecRowData.specAndRatePlan;
		let paramInfo: Array<any> = selectSpecRowData.paramInfo
		if (paramInfo && paramInfo.length > 0) {
			paramInfo.forEach(item => {
				productOrderCharacter.push({
					characterID: item.paramId,
					characterName: item.paramName,
					characterValue: item.paramValue,
				})
			})
		}
		let ratePlanInfo: Array<any> = selectSpecRowData.ratePlanInfo;
		if (ratePlanInfo && ratePlanInfo.length > 0) {
			let rateParam: Array<any> = []
			ratePlanInfo.forEach((item: any) => {
				let ratePlanId = item.ratePlanId
				if (specAndRatePlan === ratePlanId) {
					rateParam.push({
						paramName: item.ratePlanName,
						paramValue: item.defaultValue,
						paramID: item.rateParamId
					})
				}
			})
			productOrderRatePlan.push({
				action: '1',
				ratePlanID: specAndRatePlan,
				rateParam: rateParam
			})
		}
		let obj = {
			subOprType: '1',
			productNumber: selectSpecRowData.productNumber,
			subsId: '',
			effduration: lineForm.value.purchaseDuration,
			effdurationType: feeQueryForm.value.billingMethod,
			effNum: lineForm.value.purchaseQuantity,
			effTime: '',
			expTime: '',
			billEffTime: '',
			productSpec: [
				{
					specId: selectSpecRowData.specId,
					specName: selectSpecRowData.specName
				}
			],
			productOrderCharacter: productOrderCharacter,
			productOrderRatePlan: productOrderRatePlan
		}
		specObj = {...obj}
	}
	return specObj
}

const specChange = (value: string) => {
	inquiry()
}

const lineSpecChange = (value: string) => {
	if (specId.value) {
		inquiry()
	}
}

const purchaseQuantityChange = (value: string) => {
	if (specId.value) {
		inquiry()
	}
}

const purchaseDurationChange = (value: string) => {
	if (specId.value) {
		inquiry()
	}
}

const chooseLineChange = (value: string) => {
	if (value == '1') {
		if (specId.value) {
			inquiry()
		}
	} else {
		lineSpecId.value = null
		if (specId.value) {
			inquiry()
		}
	}
}

const handleCurrentChange = (currentRow: any) => {
	if (currentRow) {
		specId.value = currentRow.id;
	} else {
		specId.value = null
		feeValue.value = 0
	}
}

const inquiry = () => {
	let params: InquiryQueryParams = {
		productCnt: lineForm.value.purchaseQuantity,
		billMethod: feeQueryForm.value.billingMethod,
		effduration: lineForm.value.purchaseDuration,
	}
	if (specId.value) {
		params.specId = specId.value
	}
	if (lineSpecId.value) {
		params.lineSpecId = lineSpecId.value
	}
	accountSpecification(params).then(response => {
		let data = response.data
		if (data) {
			feeValue.value = data.feeValue ? data.feeValue : 0
		} else {
			feeValue.value = 0
		}
	})
}

onMounted(() => {
	queryChooseLineList()
	feeTitle.value = history.state.title;
})

</script>

<style scoped lang="scss">
.product-order {
	width: 100%;

	.specifications-card {
		width: 100%;
		margin-top: 15px;
	}

	.confirm-row {
		margin-bottom: 15px;
		float: right;
	}
}
</style>