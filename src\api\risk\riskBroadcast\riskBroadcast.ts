import {AxiosPromise} from "axios";
import request from "@/utils/request";
import {AxiosResponse} from "axios";
import { LatestVideoResponse, VersionCheckResponse, VideoUploadResult } from "@/api/risk/riskBroadcast/types";

// export function queryBillsByMonth(): AxiosPromise<any> {
//     return request.service({
//         url: "/api/v1/sync/EBOSS/QueryBillsByMonth",
//         method: "get",
//     });
// }
//
// /**
//  * 账单信息查询
//  */
// export function queryBills(queryParams: BillInfoQuery): AxiosPromise<any> {
//     return request.service({
//         url: "/api/v1/sync/EBOSS/QueryBills",
//         method: "post",
//         data: queryParams,
//     });
// }


/**
 * 上传视频文件
 * @param data FormData对象，包含视频文件
 * @returns Promise<VideoUploadResult>
 */
export function uploadVideo(data: FormData) {
    return request.service({
        url: "/api/v1/video/upload",
        method: "post",
        data: data,
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    }) as Promise<VideoUploadResult>;
}

/**
 * 获取视频列表
 * @returns Promise
 */
export function getVideoList() {
    return request.service({
        url: "/api/v1/video/list",
        method: "get"
    });
}

/**
 * 获取最新视频
 * @returns Promise
 */
export function getLatestVideo() {
    return request.service({
        url: "/api/v1/video/latest",
        method: "get"
    });
}

/**
 * 获取视频流URL
 * @param fileName 文件名
 * @returns 视频流URL
 */
export function getVideoStreamUrl(fileName: string) {
    return `${request.service.defaults.baseURL}/api/v1/video/stream/${fileName}`;
}

/**
 * 获取视频剧本数据
 */
export function getDynamicVideoInformation(params: any) {
    return request.service({
        url: "/api/v1/ebrps/test/getDynamicVideoInformation",
        method: "post",
        data:params
    });
}
