import {
  login<PERSON><PERSON>,
  logo<PERSON><PERSON><PERSON>,
  verify<PERSON><PERSON><PERSON>,
  verify<PERSON><PERSON><PERSON><PERSON><PERSON>,
  verify<PERSON>ms<PERSON><PERSON><PERSON>,
  apply<PERSON><PERSON><PERSON>,
  getUserName<PERSON>
} from "@/api/auth";
import { getUserInfoApi } from "@/api/user";
import { resetRouter } from "@/router";
import { store } from "@/store";

import { LoginData } from "@/api/auth/types";
import { UserInfo } from "@/api/user/types";

export const useUserStore = defineStore("user", () => {
  const user = ref<UserInfo>({
    roles: [],
    perms: [],
  });

  /**
   * 登录
   *
   * @param {LoginData}
   * @returns
   */
  function login(loginData: LoginData) {
    return new Promise<void>((resolve, reject) => {
      loginApi(loginData)
        .then((response) => {
          const { tokenType, accessToken } = response.data;
          localStorage.setItem("accessToken", tokenType + " " + accessToken); // Bearer eyJhbGciOiJIUzI1NiJ9.xxx.xxx
          resolve();
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  function verifyTicket(ticket: string) {
    return new Promise<string>((resolve, reject) => {
      verifyTickett(ticket)
        .then((response) => {
          resolve(response.data)
        })
    })
  }

  function verifyUserType(staffCode: string) {
    return new Promise<string>((resolve, reject) => {
      verifyUserTypee(staffCode)
        .then((response) => {
          resolve(response.data)
        })
    })
  }

  function verifySmsCode(smsCode: string) {
    return new Promise<string>((resolve, reject) => {
      verifySmsCodee(smsCode)
        .then((response) => {
          resolve(response.data)
        })
    })
  }

  function applyTicket() {
    return new Promise<string>((resolve, reject) => {
      applyTickett()
        .then((response) => {
          resolve(response.data)
        })
    })
  }

  function getUserName() {
    return new Promise<string>((resolve, reject) => {
      getUserNamee()
        .then((response) => {
          resolve(response.data)
        })
    })
  }


  // 获取信息(用户昵称、头像、角色集合、权限集合)
  function getUserInfo() {
    return new Promise<UserInfo>((resolve, reject) => {
      getUserInfoApi()
        .then(({ data }) => {
          if (!data) {
            reject("Verification failed, please Login again.");
            return;
          }
          if (!data.roles || data.roles.length <= 0) {
            reject("getUserInfo: roles must be a non-null array!");
            return;
          }
          Object.assign(user.value, { ...data });
          resolve(data);
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  // user logout
  function logout() {
    return new Promise<void>((resolve, reject) => {
      logoutApi()
        .then(() => {
          localStorage.setItem("accessToken", "");
          location.reload(); // 清空路由
          resolve();
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  // remove token
  function resetToken() {
    console.log("resetToken");
    return new Promise<void>((resolve) => {
      localStorage.setItem("accessToken", "");
      resetRouter();
      resolve();
    });
  }

  return {
    user,
    login,
    getUserInfo,
    logout,
    resetToken,
    verifyTicket,
    verifyUserType,
    verifySmsCode,
    applyTicket,
    getUserName
  };
});

// 非setup
export function useUserStoreHook() {
  return useUserStore(store);
}
