<template>
	<el-container class="model-card">
		<el-main>
			<el-row :gutter="24">
				<el-col :span="4">
					<img
						:src="getAssetsFile(url)"
						alt="logo"
						class="logo"
					/>
				</el-col>
				<el-col :span="20">
					<div class="title">{{ title }}</div>
				</el-col>
			</el-row>
			<el-row :gutter="24">
				<el-col :span="24">
					<div class="content">{{ content }}</div>
				</el-col>
			</el-row>
			<el-row :gutter="24" style="margin-top: 20px;">
				<el-col :span="24" class="text-center">
					<el-button type="primary" @click="viewDetail" class="view-detail-btn">查看详情</el-button>
				</el-col>
			</el-row>
		</el-main>
	</el-container>

</template>

<script setup lang="ts">

import {getAssetsFile} from "@/utils/commonUtils";
import {useRouter} from "vue-router";

const props = defineProps({
	url: {
		type: String,
		default: '',
		required: true,
	},
	title: {
		type: String,
		default: '',
		required: true,
	},
	content: {
		type: String,
		default: '',
		required: true,
	},
	modelType: {
		type: Number,
		default: 1,
		required: true,
	},
})
const router = useRouter();

const viewDetail = () => {
	router.push({
		path: '/model/detail',
		query: {
			modelType: props.modelType,
		}
	})
}

</script>

<style scoped lang="scss">
.model-card {
	box-shadow: 0 0 12px rgb(0, 0, 0, .12);
	opacity: 1;
	border-radius: 11px;
	background: #EFF6FF;

	.logo {
		width: 80px;
		height: 63px;
	}

	.title {
		opacity: 1;
		color: #2E6FC2;
		font-family: PingFang SC;
		font-weight: bold;
		font-size: 18px;
		line-height: 28px;
		letter-spacing: 0;
		text-align: left;
	}

	.content {
		opacity: 1;
		color: #666666;
		font-family: PingFang SC;
		font-weight: 400;
		font-size: 14px;
		line-height: 28px;
		letter-spacing: 0;
		text-align: left;
	}

	.view-detail-btn {
		opacity: 1;
		border-radius: 8px;
		background: #0079FD;
		width: 195px;
		height: 36px;
	}
}

</style>
