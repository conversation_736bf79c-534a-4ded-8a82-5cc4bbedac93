<template>
  <div class="logout-page">
    <div class="logout-content">
      <img class="logout-img" :src="getAssetsFile('logout-bg.png')" alt="系统服务正常" />
      <div class="logout-text">
        <div class="logout-title">系统服务正常</div>
        <div class="logout-desc">
          请从共享中心单点登录
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getAssetsFile } from "@/utils/commonUtils";
onMounted(() => {
  // 禁止后退
  history.pushState(null, '', location.href);
  window.addEventListener('popstate', onPopState);
});

onUnmounted(() => {
  window.removeEventListener('popstate', onPopState);
});

function onPopState() {
  history.pushState(null, '', location.href);
}
</script>

<style scoped lang="scss">
.logout-page {
  width: 100vw;
  min-height: 100vh;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
}
.logout-content {
  display: flex;
  flex-direction: row; /* 横向排列图片和文字 */
  align-items: center;  /* 垂直居中 */
  justify-content: center;
  gap: 48px; /* 图片和文字间距 */
}
.logout-img {
  width: 260px;
  max-width: 30vw;
}
.logout-text {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.logout-title {
  font-size: 48px;
  font-weight: bolder;
  color: #222;
  margin-bottom: 16px;
}
.logout-desc {
  font-size: 20px;
  color: #092589;
  font-weight: bold;
  //.oa-link {
  //  color: #1976d2;
  //  font-weight: 500;
  //  margin-left: 4px;
  //}
}
</style>
