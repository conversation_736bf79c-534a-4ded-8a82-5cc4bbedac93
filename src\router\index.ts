import {createRouter, createWebHashHistory, createWebHistory, RouteRecordRaw} from "vue-router";

export const Layout = () => import("@/layout/index.vue");

// 静态路由
export const constantRoutes: RouteRecordRaw[] = [
    {
        path: "/redirect",
        component: Layout,
        meta: {hidden: true},
        children: [
            {
                path: "/redirect/:path(.*)",
                component: () => import("@/views/redirect/index.vue"),
            },
        ],
    },
    {
        path: "/login",
        name: 'Login',
        component: () => import("@/views/login/index.vue"),
        meta: {hidden: true},
    },

    // oa系统登录
    {
      path: "/verifyTicket",
      component: () => import("@/views/login/index.vue"),
      meta: {hidden: true},
    },

    // 短信验证码界面
    {
      path: "/smsCode",
      component: () => import("@/views/login/CaptchaCodeLogin.vue"),
      meta: {hidden: true},
    },

    // 4a页面登录
    {
      path: "/jump4a",
      component: () => import("@/views/login/indexFourA.vue"),
      meta: {hidden: true},
    },

    {
      path: "/login4a",
      component: () => import("@/views/login/login4a.vue"),
      meta: {hidden: true},
    },

    {
        path: "/code-login",
        name: 'CodeLogin',
        component: () => import("@/views/login/CaptchaCodeLogin.vue"),
        meta: {hidden: true},
    },
    {
        path: "/logout",
        name: 'Logout',
        component: () => import("@/views/login/Logout.vue"),
        meta: {hidden: true},
    },
    {
        path: "/riskVideo",
        component: () => import("@/views/risk/components/RiskVideo.vue"),
        meta: { hidden: true },
    },
    {
        path: "/risk-detail",
        name: 'RiskDetail',
        component: () => import("@/views/risk/RiskDetail.vue"),
        meta: {hidden: true},
    },
    {
        path: "/correction",
        name: 'Correction',
        component: () => import("@/views/risk/Correction.vue"),
        meta: {hidden: true},
    },
    {
        path: "/summary-rectification",
        name: 'SummaryRectification',
        component: () => import("@/views/risk/SummaryRectification.vue"),
        meta: {hidden: true},
    },

    {
        path: "/risk-control-platform",
        name: 'RiskControlPlatform',
        component: () => import("@/views/risk/RiskControlPlatform.vue"),
        meta: {hidden: true},
    },

    {
        path: "/system/register",
        name: 'Register',
        component: () => import("@/views/system/register/index.vue"),
        meta: {hidden: true},
    },
    {
        path: "/external",
        component: Layout,
        children: [
            {
                path: "/external",
                component: () => import("@/components/ExternalPage/index.vue"),
                meta: {hidden: true},
            },
        ]
    },

    {
        path: "/",
        name: "/",
        component: Layout,
        redirect: "/dashboard",
        children: [
            {
                path: "dashboard",
                component: () => import("@/views/dashboard/index.vue"),
                name: "Dashboard", // 用于 keep-alive, 必须与SFC自动推导或者显示声明的组件name一致
                // https://cn.vuejs.org/guide/built-ins/keep-alive.html#include-exclude
                meta: {
                    title: "dashboard",
                    icon: "homepage",
                    affix: true,
                    keepAlive: true,
                    alwaysShow: false,
                },
            },
            {
                path: "401",
                component: () => import("@/views/error-page/401.vue"),
                meta: {hidden: true},
            },
            {
                path: "404",
                component: () => import("@/views/error-page/404.vue"),
                meta: {hidden: true},
            },
            /*{
                path: '/:pathMatch(.*)*',
                // name: 'NotFound', // 直接去掉
                meta: {
                    isHide: true
                },
                component: () => import("@/views/error-page/404.vue")
            },*/
        ],
    },

    // 外部链接
    // {
    //   path: "/external-link",
    //   component: Layout,
    //   children: [ {
    //       component: () => import("@/views/external-link/index.vue"),
    //       path: "https://www.cnblogs.com/haoxianrui/",
    //       meta: { title: "外部链接", icon: "link" },
    //     },
    //   ],
    // },
    // 多级嵌套路由
    /* {
           path: '/nested',
           component: Layout,
           redirect: '/nested/level1/level2',
           name: 'Nested',
           meta: {title: '多级菜单', icon: 'nested'},
           children: [
               {
                   path: 'level1',
                   component: () => import('@/views/nested/level1/index.vue'),
                   name: 'Level1',
                   meta: {title: '菜单一级'},
                   redirect: '/nested/level1/level2',
                   children: [
                       {
                           path: 'level2',
                           component: () => import('@/views/nested/level1/level2/index.vue'),
                           name: 'Level2',
                           meta: {title: '菜单二级'},
                           redirect: '/nested/level1/level2/level3',
                           children: [
                               {
                                   path: 'level3-1',
                                   component: () => import('@/views/nested/level1/level2/level3/index1.vue'),
                                   name: 'Level3-1',
                                   meta: {title: '菜单三级-1'}
                               },
                               {
                                   path: 'level3-2',
                                   component: () => import('@/views/nested/level1/level2/level3/index2.vue'),
                                   name: 'Level3-2',
                                   meta: {title: '菜单三级-2'}
                               }
                           ]
                       }
                   ]
               },
           ]
       }*/
];

/**
 * 创建路由
 */
const router = createRouter({
    history: createWebHistory(), // 历史模式 [!code ++]
    // history: createWebHashHistory(),
    routes: constantRoutes,
    // 刷新时，滚动条位置还原
    scrollBehavior: () => ({left: 0, top: 0}),
});

/**
 * 重置路由
 */
export function resetRouter() {
    router.replace({path: "/login"});
}

export default router;
