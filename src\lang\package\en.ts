import { register } from "module";

export default {
  // 路由国际化
  route: {
    dashboard: "Dashboard",
    document: "Document",
  },
  // 登录页面国际化
  login: {
    username: "Username",
    password: "Password",
    login: "Sign In",
    return:"Return",
    dhaa:"Don't have an account?",
    regNow:"Create an Account",
    captchaCode: "Verify Code",
    capsLock: "Caps Lock is On",
    message: {
      username: {
        required: "Please enter Username",
       
      },
      password: {
        required: "Please enter Password",
        min: "The password can not be less than 6 digits",
       
      },
      captchaCode: {
        required: "Please enter Verify Code",
      },
    },
  },
 // 注册页面国际化
  register: {
    username: "Username",
    password: "Password",
    confirmPassword: "confirmPassword", 
    register: "Register",
    return:"Return",
    to_login:"To Sign in",
    to_Portal:"Back to Portal",
    ahaa:"Already have an account?",
    title:"Create An Account",
    captchaCode: "Verify Code",
    capsLock: "Caps Lock is On",
    nickname:"Name",
    companyName:"companyName",
    email:"email",
    mobile:"mobile",
    message: {
      username: {
        required: "Please enter Username",
        length:"The username can not be less than 2 characters or more than 32 characters ",
        pattern:"Username can be only number,letters and '_' ",
      },
      password: {
        required: "Please enter Password",
        length:"The password can not be less than 6 characters or more than 32 characters ",
      },
      confirmPassword: {
        required: "Please enter confirmPassword",
        consistency: "Type the same password in both textboxs",      
      },
      nickname: {
        required: "Please enter Name",
        CHI: "The nickname must be Chinese",
        length:"The nickname can not be less than 2 characters or more than 32 characters ",
      },
      companyName:{
        required: "Please enter companyName ",
        length:"The companyName can not be less than 2 characters or more than 32 characters ",
        CHI:"The companyName must be Chinese"
      },
      email:{
  
        required: "Please enter email",
        pattern:"Please enter the correct email address",
      },
      mobile:{
  
        required: "Please enter mobile number",
        pattern:"Please enter the right mobile number",
      },
      captchaCode: {
        required: "Please enter Verify Code",
      },
    },
  },

  // 导航栏国际化
  navbar: {
    dashboard: "Dashboard",
    logout: "Logout",
    document: "Document",
    gitee: "Gitee",
  },
  sizeSelect: {
    tooltip: "Layout Size",
    default: "Default",
    large: "Large",
    small: "Small",
    message: {
      success: "Switch Layout Size Successful!",
    },
  },
  langSelect: {
    message: {
      success: "Switch Language Successful!",
    },
  },
  settings: {
    project: "Project Settings",
    theme: "Theme",
    interface: "Interface",
    navigation: "Navigation",
    themeColor: "Theme Color",
    tagsView: "Tags View",
    fixedHeader: "Fixed Header",
    sidebarLogo: "Sidebar Logo",
    watermark: "Watermark",
  },
};
