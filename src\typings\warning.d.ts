declare module "@/utils/commonUtils" {
    export function getAssetsFile(path: string): string;
    // 如果有其他导出的函数或变量，也可以在这里声明
}

declare module "*.vue" {
    import { DefineComponent } from "vue";
    const component: DefineComponent<{}, {}, any>;
    export default component;
}

declare module "@/assets/mapJson/cityInfo" {
    export const provinces: any[];
    // 如果有其他导出的变量或函数，也可以在这里声明
}

declare module "*.json" {
    const value: any;
    export default value;
}
