<template>
	<div class="product-service">
		<div class="banner-main">
			<div class="banner-text">
				<el-text
					class="mx-1 banner-title"
					size="large"
					line-clamp="1"
					tag="b"
				>
					{{ bannerTitle }}
				</el-text>
				<el-text
					class="mx-1 banner-desc"
					size="large"
					line-clamp="1"
				>
					{{ bannerDesc }}
				</el-text>
			</div>
			<el-image
				:src="getAssetsFile('product/banner.png')"
				style="height: 100%;width: 100%"
				fit="fill"
			>
			</el-image>
		</div>

		<div class="product-title">
			<span class="service-title">{{ productListName }}</span>
		</div>

		<div class="table-parent">
			<div class="table-data-list">
				<el-row :gutter="24" style="margin-top: 24px;" v-for="rowList in tableDataByRowList">
					<el-col :span="6" v-for="item in rowList">
						<product-card
							ref="productCard"
							:title="item.productName"
							:description="item.description"
							:imageBase64Code="item.imageBase64Code"
						    :product-class="item.productClass"
						    :product-number="item.productNumber"
						>
						</product-card>
					</el-col>
				</el-row>
			</div>
		</div>

	</div>
</template>

<script setup lang="ts">

import { getAssetsFile, getNewArray } from '@/utils/commonUtils'
import ProductCard from "@/views/product/service/components/ProductCard.vue";
import {productListQuery} from "@/api/product";

const bannerTitle = ref('产品服务')
const bannerDesc = ref('算力汇聚共享，用科技构建信任基石')

const productListName = ref('产品列表')
const tableData = ref([])
const tableDataByRowList = ref()


const initProductList = () => {
	let params = {}
	productListQuery(params).then(response => {
		tableData.value = response.data
		tableDataByRowList.value = getNewArray(tableData.value, 4)
	})
}

onMounted(() => {
	initProductList()
})


</script>


<style scoped lang="scss">
.product-service {
	margin-bottom: 45px;

	.banner-main {
		height: 420px;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;

		.banner-text {
			position: absolute;
			z-index: 100;
			text-align: center;


			.banner-title {
				display: block;
				font-size: 48px;
				color: white;
				font-weight: bold;
			}

			.banner-desc {
				display: block;
				font-size: 36px;
				color: white;
				font-weight: bold;
			}
		}
	}

	.product-title {

		margin-top: 35px;
		padding-left: 35px;

		.service-title {
			padding-left: 12px;
			position: relative;
			padding-top: 3px;
		}


		.service-title::before {
			content: "";
			position: absolute;
			top: 4px;
			left: 0;
			display: inline-block;
			width: 6px;
			height: 18px;
			background: #409EFF;
			border-radius: 2px;
		}

	}

	.table-parent {
		display: flex;
		justify-content: center;

		.table-data-list {
			padding: 35px;
			max-width: 1440px;
		}
	}
}

</style>