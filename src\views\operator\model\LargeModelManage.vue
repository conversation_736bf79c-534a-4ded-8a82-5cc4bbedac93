<template>
	<div>
		<el-card style="width: 100%;">
			<template #header>
				<el-text
					class="mx-1"
					size="large"
					line-clamp="1"
					tag="b"
				>
					{{ productTitle }}
				</el-text>
			</template>

			<el-container>
				<el-header>
					<el-form :model="queryForm" ref="queryFormRef" label-width="auto">
						<el-row :gutter="24">
							<el-col :span="2">
								<el-button type="primary" @click="add">{{ addBtnText }}</el-button>
							</el-col>
							<el-col :span="2">
								<el-button type="primary" @click="query">{{ queryBtnText }}</el-button>
							</el-col>
						</el-row>
					</el-form>
				</el-header>
				<el-main>
					<el-table
						v-loading="loading"
						:data="tableData"
						border
						height="580"
						style="width: 100%"
						tooltip-effect="dark"
					>
						<el-table-column prop="llmName" label="大模型名称" align="center"/>
						<el-table-column prop="llmDesc" label="大模型描述" align="center" width="380"/>
						<el-table-column prop="llmLink" label="大模型链接" align="center"/>
						<el-table-column prop="llmOrder" label="展示顺序" align="center" width="120"/>
						<el-table-column prop="updateTime" label="更新时间" align="center"/>
						<el-table-column label="操作" align="center" width="200">
							<template #default="scope">
								<el-button link type='primary' @click="edit(scope.row)">{{ editBtnText }}</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-main>
			</el-container>
		</el-card>
		<large-model-dialog
			ref="largeModelDlgRef"
			@query="query"
		>
		</large-model-dialog>
	</div>
</template>

<script setup lang="ts">
import {FormInstance} from "element-plus";
import {initDictMap} from "@/utils/commonUtils";
import LargeModelDialog from "@/views/operator/model/components/LargeModelDialog.vue";
import {LargeModel} from "@/api/model/types";
import {queryLargeModelList} from "@/api/model";
import {OperationTypeEnum} from "@/enums/OperationTypeEnum";

const productTitle = ref<string>('市面主流大模型介绍管理')
const addBtnText = ref<string>('新增大模型')
const queryBtnText = ref<string>('查询')
const editBtnText = ref<string>('编辑')
const largeModelDlgRef = ref()

const queryForm = reactive({
	productClass: '',
});

const tableData = ref<Array<LargeModel>>([]);

const loading = ref(false)

const query = () => {
	loading.value = true
	queryLargeModelList().then(response => {
		tableData.value = response.data
	}).finally(() => {
		loading.value = false
	})
}
const queryFormRef = ref<FormInstance>()

const resetForm = (formEl: FormInstance | undefined) => {
	if (!formEl) return
	formEl.resetFields()
}

const add = () => {
	largeModelDlgRef.value.init(OperationTypeEnum.ADD, null)
}

const view = (rowData: LargeModel) => {
	largeModelDlgRef.value.init(OperationTypeEnum.VIEW, rowData)
}

const edit = (rowData: LargeModel) => {
	largeModelDlgRef.value.init(OperationTypeEnum.UPDATE, rowData)
}

const typeCodeMap = reactive<Map<string, any>>(new Map([
	["productClass", ref<object>({})],
]));

onMounted(() => {
	query()
	initDictMap(typeCodeMap)
})

</script>

<style scoped lang="scss">

.product-page {
	float: right;
	line-height: 60px;
}

</style>