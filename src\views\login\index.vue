<template>
  <!--	<div class="login-container">-->
  <!--		<el-container class="h-full">-->
  <!--			<el-header>-->
  <!--				<el-image-->
  <!--					:src="getAssetsFile('china_mobile.png')"-->
  <!--					class="w-140px h-43px ml-3 mt-3"-->
  <!--					fit="fill"-->
  <!--				>-->
  <!--				</el-image>-->
  <!--			</el-header>-->
  <!--			<el-main style="height: calc(100% - 120px)">-->
  <!--				<el-row :gutter="24">-->
  <!--					<el-col :offset="2" :span="18">-->
  <!--						<div class="login-title-bar">-->
  <!--							<div class="login-title">政企业务风险防控信息化系统</div>-->
  <!--							<div class="login-title-en-desc">Government enterprise business risk prevention and control-->
  <!--								information system-->
  <!--							</div>-->
  <!--						</div>-->
  <!--					</el-col>-->
  <!--				</el-row>-->
  <!--				<el-row :gutter="24" class="mt-5" style="height: calc(100% - 132px);">-->
  <!--					<el-col :offset="2" :span="18" class="login-card">-->
  <!--						<el-card style="width: 360px;height: 380px;">-->
  <!--							<el-form-->
  <!--								ref="loginFormRef"-->
  <!--								:model="loginData"-->
  <!--								:rules="loginRules"-->
  <!--								class="login-form"-->
  <!--								label-position="top"-->
  <!--							>-->
  <!--								<el-form-item label="用户名称" prop="username">-->
  <!--									<el-input-->
  <!--										ref="username"-->
  <!--										:placeholder="$t('login.username')"-->
  <!--										v-model="loginData.username"-->
  <!--										name="username"-->
  <!--										size="default"-->
  <!--									/>-->
  <!--								</el-form-item>-->
  <!--								<el-form-item label="登录密码" prop="password">-->
  <!--									<el-input-->
  <!--										v-model="loginData.password"-->
  <!--										:placeholder="$t('login.password')"-->
  <!--										type="password"-->
  <!--										name="password"-->
  <!--										@keyup="checkCapslock"-->
  <!--										@keyup.enter="handleLogin"-->
  <!--										size="default"-->
  <!--										show-password-->
  <!--									/>-->
  <!--								</el-form-item>-->
  <!--								<el-form-item label="验证码" prop="captchaCode">-->
  <!--									<el-input-->
  <!--										v-model="loginData.captchaCode"-->
  <!--										auto-complete="off"-->
  <!--										size="default"-->
  <!--										class="flex-1"-->
  <!--										:placeholder="$t('login.captchaCode')"-->
  <!--										@keyup.enter="handleLogin"-->
  <!--									/>-->
  <!--									<el-image-->
  <!--										@click="getCaptcha"-->
  <!--										:src="captchaBase64"-->
  <!--										class="rounded-tr-md rounded-br-md cursor-pointer h-[48px] ml-5"-->
  <!--									/>-->
  <!--								</el-form-item>-->

  <!--							</el-form>-->


  <!--							<el-button-->
  <!--								:loading="loading"-->
  <!--								type="primary"-->
  <!--								size="large"-->
  <!--								class="w-full"-->
  <!--								@click.prevent="handleLogin"-->
  <!--							>{{ $t("login.login") }}-->
  <!--							</el-button>-->
  <!--						</el-card>-->
  <!--					</el-col>-->
  <!--				</el-row>-->
  <!--			</el-main>-->
  <!--			<el-footer>-->
  <!--				<el-row :gutter="24">-->
  <!--					<el-col :offset="2" :span="6">-->
  <!--						<div class="foot-text">Copyright @ 中国移动，All Rights Reserved.</div>-->
  <!--						<div class="foot-text" style="text-align: center;">政企风控预警管理平合</div>-->
  <!--					</el-col>-->
  <!--				</el-row>-->
  <!--			</el-footer>-->
  <!--		</el-container>-->

		<!-- 顶部 -->
		<!--		<div class="absolute-lt flex-x-start p-3 w-full">
				&lt;!&ndash;<el-switch
						v-model="isDark"
						inline-prompt
						:active-icon="Moon"
						:inactive-icon="Sunny"
						@change="toggleTheme"
					/>
					<lang-select class="ml-2 cursor-pointer"/>&ndash;&gt;
				</div>
		&lt;!&ndash;		<div class="">
					<div>政企业务风险防控信息化系统</div>
					<div>Government enterprise business risk prevention and control information system</div>
				</div>&ndash;&gt;
				&lt;!&ndash; 登录表单 &ndash;&gt;
				<el-card class="!border-none !bg-transparent !rounded-4% w-100 <sm:w-85" style="margin-left: 154px;">
					<div class="text-center relative">
						<el-tag class="ml-2 absolute-rt">{{ defaultSettings.version }}</el-tag>
					</div>

					<div style="display: flex; justify-content: center;align-items:end; margin: auto; padding-top: 10px; ">
						<img src="../../assets/images/logo.png"
							 style="vertical-align: middle;width:30%; height: 30%; padding-right: 5px">
					</div>
					<div class="text-center">
						<h2 style="vertical-align:bottom;margin-bottom: 0;color: #409EFF;">{{ defaultSettings.title }}</h2>
					</div>
					<el-form
						ref="loginFormRef"
						:model="loginData"
						:rules="loginRules"
						class="login-form"
					>
						&lt;!&ndash; 用户名   &ndash;&gt;
						<el-form-item prop="username">
							<div class="flex-y-center w-full">
								<svg-icon icon-class="user" class="mx-2"/>
								<el-input
									ref="username"
									:placeholder="$t('login.username')"
									v-model="loginData.username"

									name="username"
									size="large"
									class="h-[48px]"
								/>
							</div>
						</el-form-item>

						&lt;!&ndash; 密码     :content="$t('login.capsLock')"&ndash;&gt;
						<el-tooltip
							:visible="isCapslock"

							:content="$t('login.capsLock')"
							placement="right"
						>
							<el-form-item prop="password">
								<div class="flex-y-center w-full">
									<svg-icon icon-class="lock" class="mx-2"/>
									<el-input
										v-model="loginData.password"
										:placeholder="$t('login.password')"
										type="password"
										name="password"
										@keyup="checkCapslock"
										@keyup.enter="handleLogin"
										size="large"
										class="h-[48px] pr-2"
										show-password
									/>
								</div>
							</el-form-item>
						</el-tooltip>

						&lt;!&ndash; 验证码 &ndash;&gt;
						<el-form-item prop="captchaCode">
							<div class="flex-y-center w-full">
								<svg-icon icon-class="captcha" class="mx-2"/>
								<el-input
									v-model="loginData.captchaCode"
									auto-complete="off"
									size="large"
									class="flex-1"
									:placeholder="$t('login.captchaCode')"
									@keyup.enter="handleLogin"
								/>

								<el-image
									@click="getCaptcha"
									:src="captchaBase64"
									class="rounded-tr-md rounded-br-md cursor-pointer h-[48px]"
								/>
							</div>
						</el-form-item>
						&lt;!&ndash;<div>
						<span class="sp">{{ $t("login.dhaa") }}</span>  <span class="reg" @click="func_toReg()">{{ $t("login.regNow") }}</span>
						</div>&ndash;&gt;
						&lt;!&ndash; 登录按钮 &ndash;&gt;
						<el-button
							:loading="loading"
							type="primary"
							size="large"
							class="w-full"
							@click.prevent="handleLogin"
						>{{ $t("login.login") }}
						</el-button>
					</el-form>
				</el-card>

				&lt;!&ndash; ICP备案 &ndash;&gt;
				<div class="absolute bottom-1 text-[10px] text-center" v-show="icpVisible">
					<p>
						Copyright © 中国移动, All Rights Reserved.
					</p>
				</div>-->
  <!--	</div>-->

  <el-card v-if="tmp" class="code-box" shadow="always">
    <div class="title">短信验证码</div>
    <el-form :model="form" ref="formRef" label-width="80px">
      <el-form-item label="验证码">
        <el-input
          v-model.number="form.code"
          :maxlength="6"
          placeholder="请输入6位验证码"
          @input="handleInput"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleVerify">验证</el-button>
      </el-form-item>
    </el-form>
  </el-card>

</template>

<script setup lang="ts">
import {useSettingsStore, useUserStore} from "@/store";
import {getCaptchaApi} from "@/api/auth";
import {LoginData} from "@/api/auth/types";
import {Sunny, Moon} from "@element-plus/icons-vue";
import {LocationQuery, LocationQueryValue, useRoute,useRouter} from "vue-router";

import defaultSettings from "@/settings";
import {ThemeEnum} from "@/enums/ThemeEnum";

// Stores
const userStore = useUserStore();
const settingsStore = useSettingsStore();

// Internationalization
const {t} = useI18n();

// Reactive states
const isDark = ref(settingsStore.theme === ThemeEnum.DARK);
const icpVisible = ref(true);
const loading = ref(false); // 按钮loading
const isCapslock = ref(false); // 是否大写锁定
const captchaBase64 = ref(); // 验证码图片Base64字符串
const loginFormRef = ref(ElForm); // 登录表单ref
const {height} = useWindowSize();

const loginData = ref<LoginData>({
  username: "root",
  password: "123456",
});

// 普通用户登录时用来获取用户名
let userName: string;

const loginRules = computed(() => {
  return {
    username: [
      {
        required: true,
        trigger: "blur",
        message: t("login.message.username.required"),
      },
    ],
    password: [
      {
        required: true,
        trigger: "blur",
        message: t("login.message.password.required"),
      },
      {
        min: 6,
        message: t("login.message.password.min"),
        trigger: "blur",
      },
    ],
    captchaCode: [
      {
        required: true,
        trigger: "blur",
        message: t("login.message.captchaCode.required"),
      },
    ],
  };
});

/**
 * 获取验证码
 */
function getCaptcha() {
  getCaptchaApi().then(({data}) => {
    loginData.value.captchaKey = data.captchaKey;
    captchaBase64.value = data.captchaBase64;
  });
}

// oa系统请求
const routeNew = useRouter();
const paramNew = new URLSearchParams(routeNew.currentRoute.value.query);
const ticket = paramNew.get('ticket'); // 从 URL 参数获取登录名
// 调用实际登录接口
if (ticket) {
  handleLoginNew(ticket);
}

/**
 * 登录
 */
const route = useRoute();

function handleLogin() {
	loginFormRef.value.validate((valid: boolean) => {
		if (valid) {
			loading.value = true;
			userStore
				.login(loginData.value)
				.then(() => {
					const query: LocationQuery = route.query;
					const redirect = (query.redirect as LocationQueryValue) ?? "/";
					const otherQueryParams = Object.keys(query).reduce(
						(acc: any, cur: string) => {
							if (cur !== "redirect") {
								acc[cur] = query[cur];
							}
							return acc;
						},
						{}
					);

					router.push({path: redirect, query: otherQueryParams});
				})
				.catch(() => {
					getCaptcha();
				})
				.finally(() => {
					loading.value = false;
				});
		}
	});
}

async function handleLoginNew(loginName: string) {
  // 校验ticket,获取oa主账号
  const staffCode = await userStore.verifyTicket(loginName);
  const userTypeResponse = await userStore.verifyUserType(staffCode);

  const loginDataA = ref<LoginData>({
    username: staffCode,
    password: ''
  });

  // 为true代表非普通用户，直接跳转；否则进行鉴权登录
  if (userTypeResponse === "-1") {
    // tmp.value = true
    userName = staffCode
    // 鉴权接口
    await router.push('/smsCode?staffCode=' + userName)
  } else if (userTypeResponse === "0") {
    userStore
      .login(loginDataA.value)
      .then(() => {
        const query: LocationQuery = route.query;
        const redirect = (query.redirect as LocationQueryValue) ?? "/";
        const otherQueryParams = Object.keys(query).reduce(
          (acc: any, cur: string) => {
            if (cur !== "redirect") {
              acc[cur] = query[cur];
            }
            return acc;
          },
          {}
        );
        router.push({path: redirect, query: otherQueryParams});
      })
  }
}

/**
 * 主题切换
 */

const toggleTheme = () => {
	const newTheme =
		settingsStore.theme === ThemeEnum.DARK ? ThemeEnum.LIGHT : ThemeEnum.DARK;
	settingsStore.changeTheme(newTheme);
};
/**
 * 根据屏幕宽度切换设备模式
 */

watchEffect(() => {
	if (height.value < 600) {
		icpVisible.value = false;
	} else {
		icpVisible.value = true;
	}
});

/**
 * 检查输入大小写
 */
function checkCapslock(event: KeyboardEvent) {
	// 防止浏览器密码自动填充时报错
	if (event instanceof KeyboardEvent) {
		isCapslock.value = event.getModifierState("CapsLock");
	}
}

onMounted(() => {
	getCaptcha();
});

//点击卡片，路由跳转
// import {useRouter} from "vue-router";
import {getAssetsFile} from "@/utils/commonUtils";

const router = useRouter();
const func_toReg = () => {
	router.push("/system/register");
}

// 控制显示的变量
const tmp = ref(false);

// 表单数据
const form = reactive({
  code: ''
});

// 输入框自动限制为6位
const handleInput = (val) => {
  form.code = val?.toString()?.slice(0, 6);
};

// 验证逻辑
async function handleVerify() {
  // if (!form.code || form.code.length !== 6) {
  //   ElMessage.warning('请输入6位有效验证码');
  //   return;
  // }

  // 校验验证码
  const res = await userStore.verifySmsCode(form.code)
  if (res === '-1') {
    ElMessage.error('验证码错误')
  } else {
    ElMessage.success('验证成功');

    const loginDataA = ref<LoginData>({
      username: userName,
      password: ''
    });
    userStore
      .login(loginDataA.value)
      .then(() => {
        const query: LocationQuery = route.query;
        const redirect = (query.redirect as LocationQueryValue) ?? "/";
        const otherQueryParams = Object.keys(query).reduce(
          (acc: any, cur: string) => {
            if (cur !== "redirect") {
              acc[cur] = query[cur];
            }
            return acc;
          },
          {}
        );
        router.push({path: redirect, query: otherQueryParams});
      })
  }
}

// 监听显示状态变化，重置表单
watch(tmp, (newVal) => {
  if (newVal) {
    form.code = ''; // 显示时清空输入
  }
});


</script>

<style lang="scss" scoped>
html.dark .login-container {
	overflow-y: auto;
	background: url("@/assets/images/login-bg-dark.jpg") no-repeat center right;
}

.login-container {
	width: 100%;
	height: 100%;
	overflow-y: auto;
	background: url("@/assets/login_bg.png") no-repeat center;
	background-size: 100% 100%;
	//@apply wh-full flex-center;

	.login-form {
		padding: 10px 10px;
	}

	:deep(.el-card__body) {
		background: #FFFFFF;
	}

	.login-title-bar {
		margin-top: 65px;

		.login-title {
			opacity: 1;
			color: #404040;
			font-family: YouSheBiaoTiHei;
			//font-weight: bold;
			font-size: 50px;
			line-height: 24px;
			letter-spacing: 0;
			text-align: left;
		}

		.login-title-en-desc {
			opacity: 1;
			color: #7C7C7C;
			font-family: PingFang SC;
			font-weight: 400;
			font-size: 16px;
			line-height: 24px;
			letter-spacing: 0;
			text-align: left;
			margin-top: 19px;
		}
	}
	.login-card {
		height: 100%;
		display: flex;
		align-items: center;
	}

  .foot-text {
    opacity: 1;
    color: #D3D3D3;
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0;
    text-align: left;
    white-space: nowrap;
  }

  .container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
    background-color: #f5f7fa;
  }

  .code-box {
    width: 400px;
    padding: 30px;
  }

  .title {
    text-align: center;
    font-size: 1.25rem;
    font-weight: 500;
    color: #1a1a1a;
    margin-bottom: 20px;
  }
}

/*.el-form-item {
	background: var(--el-input-bg-color);
	border: 1px solid var(--el-border-color);
	border-radius: 5px;
}

.sp {
	margin-top: 5px;
	height: 20px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	text-align: left;
	line-height: 20px;
	font-size: 12px;
	color: #999;

}

.reg {
	margin-top: 5px;
	height: 20px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	text-align: left;
	line-height: 20px;
	font-size: 12px;
	color: #2547b8;
	text-decoration: underline;
	cursor: pointer;

}

:deep(.el-input) {
	.el-input__wrapper {
		padding: 0;
		background-color: transparent;
		box-shadow: none;

		&.is-focus,
		&:hover {
			box-shadow: none !important;
		}

		input:-webkit-autofill {
			!* 通过延时渲染背景色变相去除背景颜色 *!
			transition: background-color 1000s ease-in-out 0s;
		}
	}
}*/
</style>
