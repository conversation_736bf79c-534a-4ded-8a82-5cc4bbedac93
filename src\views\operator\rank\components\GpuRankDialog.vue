<template>
	<div>
		<el-dialog
			v-model="dialogVisible"
			:title="title"
			top="15vh"
			width="60%"
			:close-on-click-modal="false"
			:close-on-press-escape="true"
			:before-close="handleClose"
			draggable
		>
			<el-container>
				<el-main>
					<el-card style="width: 100%;">
						<el-form
							:model="form"
							ref="formRef"
							label-width="120px"
							:rules="rules"
							:disabled="formDisabled"
						>
							<el-row :gutter="24">
								<el-col :span="18">
									<el-text
										class="mx-1"
										size="large"
										line-clamp="1"
										tag="b"
									>
										{{ baseInfoTitle }}
									</el-text>
								</el-col>
							</el-row>

							<el-row :gutter="24">
								<el-col :span="18">
									<el-form-item label="GPU卡算力" prop="gpuTypeName">
										<el-input v-model="form.gpuTypeName" clearable></el-input>
									</el-form-item>
								</el-col>
							</el-row>

							<el-row :gutter="24">
								<el-col :span="12">
									<el-form-item label="算力值" prop="gpuFlops">
										<el-input-number
											style="width: 100%;"
											v-model="form.gpuFlops"
											:controls="false"
											:precision="2"
											:min="1"
										/>
									</el-form-item>
								</el-col>
							</el-row>
							<el-row :gutter="24">
								<el-col :span="12">
									<el-form-item label="排名" prop="gpuRank">
										<el-input-number
											style="width: 100%;"
											v-model="form.gpuRank"
											clearable
										></el-input-number>
									</el-form-item>
								</el-col>
							</el-row>
						</el-form>
					</el-card>
				</el-main>
			</el-container>
			<template #footer>
				<div style="text-align: center;">
					<el-button plain @click="back">取消</el-button>
					<el-button type="primary" @click="save(formRef)" v-if="opt !== 'view'">保存</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import {ProductTableInfo} from "@/api/product/types";
import {initDictMap} from "@/utils/commonUtils";
import {FormInstance, FormRules} from "element-plus";
import {Rank, RankForm} from "@/api/rank/types";
import {getGpuInfoById, gpuInfoAdd, gpuInfoUpdate} from "@/api/rank";
import {OperationTypeEnum} from "@/enums/OperationTypeEnum";

const dialogVisible = ref<boolean>(false)
const title = ref<string>('规格详情')
const baseInfoTitle = ref<string>('基本信息')
const opt = ref<string>('')
const formDisabled = ref<boolean>(false)


const form = reactive<RankForm>({
	gpuTypeName: null,
	gpuFlops: null,
	gpuRank: 1,
});

const rules = reactive<FormRules<RankForm>>({
	gpuTypeName: [
		{required: true, message: '请输入GPU卡算力', trigger: 'blur'},
	],
	gpuFlops: [
		{required: true, message: '请输入算力值', trigger: 'blur'},
	],
})

const init = (optType: string, data: any) => {
	dialogVisible.value = true
	opt.value = optType
	switch (optType) {
		case OperationTypeEnum.ADD:
			title.value = '新增算力'
			formDisabled.value = false
			break
		case OperationTypeEnum.UPDATE:
			title.value = '编辑算力'
			formDisabled.value = false
			singleInfo(data)
			break
		case OperationTypeEnum.VIEW:
			formDisabled.value = true
			title.value = '算力详情'
			singleInfo(data)
			break
	}
}


const singleInfo = (data: ProductTableInfo) => {
	getGpuInfoById(data.id).then(response => {
		let respData = response.data
		if(respData){
			form.id = respData.id
			form.gpuTypeName = respData.gpuTypeName
			form.gpuFlops = respData.gpuFlops
			form.gpuRank = respData.gpuRank
		}
	})
}

const formRef = ref<FormInstance>()
const handleClose = (done: () => void) => {
	dialogVisible.value = false
	resetForm(formRef.value)
	done()
}

const resetForm = (formEl: FormInstance | undefined) => {
	if (!formEl) return
	formEl.resetFields()
}


const back = () => {
	dialogVisible.value = false
	resetForm(formRef.value)
}
let emits = defineEmits(['query']);

const save = async (formEl: FormInstance | undefined) => {
	if (!formEl) return
	await formEl.validate((valid, fields) => {
		if (valid) {
			let params: RankForm = {...form}
			if (opt.value === OperationTypeEnum.ADD) {
				handleSave(gpuInfoAdd, params)
			} else if (opt.value === OperationTypeEnum.UPDATE) {
				handleSave(gpuInfoUpdate, params)
			}
		}
	})
}

const handleSave = (func: typeof Function, params: Rank) => {
	func(params).then(response => {
		if (response.code === '00000') {
			ElMessage.success('保存成功！')
			dialogVisible.value = false
			resetForm(formRef.value)
			emits('query')
		} else {
			ElMessage.error('保存失败！')
		}
	}).catch((e) => {
		ElMessage.error('保存失败！')
	})
}


const typeCodeMap = reactive<Map<string, any>>(new Map([
	["billMethod", ref<object>({})],
]));

onMounted(() => {
	initDictMap(typeCodeMap)
})

defineExpose({
	init
})

</script>

<style scoped lang="scss">

</style>