<template>
	<div>
		<el-card style="width: 100%;">
			<template #header>
				<el-text
					class="mx-1"
					size="large"
					line-clamp="1"
					tag="b"
				>
					{{ productTitle }}
				</el-text>
			</template>

			<el-container>
				<el-header>
					<el-form :model="queryForm" ref="queryFormRef" label-width="auto">
						<el-row :gutter="24">
							<el-col :span="2">
								<el-button type="primary" @click="add">{{ addBtnText }}</el-button>
							</el-col>
							<el-col :span="2">
								<el-button type="primary" @click="query">{{ queryBtnText }}</el-button>
							</el-col>
						</el-row>
					</el-form>
				</el-header>
				<el-main>
					<el-table
						v-loading="loading"
						:data="tableData"
						border
						height="450"
						style="width: 100%"
						tooltip-effect="dark"
					>
						<el-table-column prop="gpuTypeName" label="GPU卡算力" align="center" />
						<el-table-column prop="gpuFlops" label="算力值" sortable align="center" />
						<el-table-column prop="gpuRank" label="排名" sortable align="center" />
						<el-table-column prop="updateTime" label="更新时间" align="center"/>
						<el-table-column label="操作" align="center" width="200">
							<template #default="scope">
								<el-button link type='primary' @click="edit(scope.row)">{{ editBtnText }}</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-main>
			</el-container>
		</el-card>
		<gpu-rank-dialog
			ref="gpuRankDlgRef"
			@query="query"
		>
		</gpu-rank-dialog>
	</div>
</template>

<script setup lang="ts">
import { FormInstance} from "element-plus";
import {initDictMap} from "@/utils/commonUtils";
import GpuRankDialog from "@/views/operator/rank/components/GpuRankDialog.vue";
import {Rank} from "@/api/rank/types";
import {queryGpuInfoList} from "@/api/rank";
import {OperationTypeEnum} from "@/enums/OperationTypeEnum";

const productTitle = ref<string>('市面主流GPU卡算力排行榜管理')
const addBtnText = ref<string>('新增算力')
const queryBtnText = ref<string>('查询')
const editBtnText = ref<string>('编辑')
const gpuRankDlgRef = ref()

const queryForm = reactive({
	productClass: '',
});

const tableData = ref<Array<Rank>>([]);

const loading = ref(false)
const current = ref<number>(1)
const size = ref<number>(10)
const total = ref<number>(0)

const query = () => {
	loading.value = true
	queryGpuInfoList().then(response => {
		tableData.value = response.data
	}).finally(() => {
		loading.value = false
	})
}
const queryFormRef = ref<FormInstance>()

const resetForm = (formEl: FormInstance | undefined) => {
	if (!formEl) return
	formEl.resetFields()
}

const add = () => {
	gpuRankDlgRef.value.init(OperationTypeEnum.ADD, null)
}

const view = (rowData: Rank) => {
	gpuRankDlgRef.value.init(OperationTypeEnum.VIEW, rowData)
}

const edit = (rowData: Rank) => {
	gpuRankDlgRef.value.init(OperationTypeEnum.UPDATE, rowData)
}

const typeCodeMap = reactive<Map<string, any>>(new Map([
	["productClass", ref<object>({})],
]));

onMounted(() => {
	query()
	initDictMap(typeCodeMap)
})

</script>

<style scoped lang="scss">

.product-page {
	float: right;
	line-height: 60px;
}

</style>