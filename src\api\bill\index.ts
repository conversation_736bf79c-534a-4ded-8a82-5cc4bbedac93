import request from "@/utils/request";
import {AxiosPromise} from "axios";
import {BillInfoQuery} from "@/api/bill/types";

/**
 * 消费方账单汇总查询
 */
/*export function queryBillsByMonthCo(): AxiosPromise<any> {
    return request.service({
        url: "/api/v1/sync/EBOSS/QueryBillsByMonthCo",
        method: "get",
    });
}*/

/**
 * 运营方账单汇总查询
 */
/*export function queryBillsByMonthOp(): AxiosPromise<any> {
    return request.service({
        url: "/api/v1/sync/EBOSS/QueryBillsByMonthOp",
        method: "get",
    });
}*/

/**
 * 账单汇总查询
 */
export function queryBillsByMonth(): AxiosPromise<any> {
    return request.service({
        url: "/api/v1/sync/EBOSS/QueryBillsByMonth",
        method: "get",
    });
}

/**
 * 账单信息查询
 */
export function queryBills(queryParams: BillInfoQuery): AxiosPromise<any> {
    return request.service({
        url: "/api/v1/sync/EBOSS/QueryBills",
        method: "post",
        data: queryParams,
    });
}


