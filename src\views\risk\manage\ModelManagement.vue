<template>
	<!--模型管理-->
	<div class="model-management">
		<el-container>
			<el-header style="height: 75px;">
				<el-row :gutter="24" class="tip-row">
					<el-col :span="1" class="img-col">
						<img
							:src="getAssetsFile('risk/model_tips.png')"
							alt="语音"
							class="tip-img"
						/>
					</el-col>
					<el-col :span="23">
						<div class="tip-content">{{ tipContent }}</div>
					</el-col>
				</el-row>
			</el-header>
			<el-main>
				<div class="main-card-list">
					<model-card
						v-for="item in modelCardList"
						:title="item.title"
						:content="item.content"
						:url="item.url"
						:model-type="item.modelType"
					></model-card>
					<div class="expectation">
						<img
							:src="getAssetsFile('risk/clock.png')"
							alt="语音"
							class="clock"
						/>
						<div class="text">
							更多风控预警模型在途中......
						</div>
					</div>
				</div>
			</el-main>
		</el-container>
	</div>
</template>

<script setup lang="ts">
import {getAssetsFile} from "@/utils/commonUtils";
import ModelCard from "@/views/risk/manage/components/ModelCard.vue";

const tipContent = ref<string>('政企业务风险防控信息化系统一期主要聚焦四大风险场景：上游客户与下游供应商存在AB/BA关系、上游客户与下游供应商存在关联关系、产品订购组合疑似不合理、合作类业务产品收入异动。后续将持续迭代更多风险模型，构建更全面的政企风险防控体系。')

const modelCardList = ref<Array<any>>(
	[
		{
			url: 'index/ict_log.png',
			title: 'ICT-项目上游客户与下游供应商存在AB/BA关系预警模型',
			content: '比对近 1 年新签约项目的上下游名称，若两项目存在 AB/BA 关系且金额差值达设定阈值，则触发对应等级预警。',
			modelType: 1,
		},
		{
			url: 'index/ict_log.png',
			title: 'ICT项目上下游单位存在关联关系预警模型',
			content: '针对当月新签约下游供应商的 ICT 项目，调用数据查询上下游关联关系，符合条件时按规则触发各级预警。',
			modelType: 2,
		},
		{
			url: 'index/cloud.png',
			title: '移动云订购云硬盘逻辑不合理预警模型',
			content: '监测移动云客户订购云硬盘却未购云主机等依赖产品的情况，按省 / 市 / 县订单量分三级触发预警。',
			modelType: 3,
		},
		{
			url: 'index/cloud.png',
			title: '移动云合作类业务收入异动预警模型',
			content: '统计直管公有云合作类业务产品月收入，当波动超阈值时，按省 / 市 / 县收入规模分三级触发预警。',
			modelType: 4,
		},
	]
)

</script>

<style scoped lang="scss">
.model-management {
	.tip-row {
		opacity: 1;
		border-radius: 12px;
		background: #FFE7D6;
		margin-top: 15px;
		height: 74px;
		padding: 10px 20px;

		.img-col {
			display: flex;
			justify-content: center;
			align-items: center;

			.tip-img {
				width: 33px;
				height: 33px;
			}
		}

		.tip-content {
			opacity: 1;
			color: #FC7F04;
			font-family: PingFang SC;
			font-weight: 400;
			font-size: 14px;
			line-height: 1.79;
			letter-spacing: 0;
			text-align: left;
		}
	}


	.main-card-list {
		margin-top: 20px;

		display: grid;
		grid-template-columns: repeat(3, 1fr); /* 三列等宽分配:ml-citation{ref="2,6" data="citationList"} */
		gap: 20px; /* 统一行列间距:ml-citation{ref="2,7" data="citationList"} */

		.expectation {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			opacity: 1;
			border-radius: 11px;
			background: #FFFFFF;
			border: 1px solid #DFDFDF;
			box-shadow: 0px 0px 6px 0px #CBCBCB4C;


			.clock {
				width: 108px;
				height: 92px;
			}

			.text {
				opacity: 1;
				color: #6D6D6D;
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 14px;
				line-height: 28px;
				letter-spacing: 0;
				text-align: left;
			}
		}
	}

}
</style>
