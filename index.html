<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/chinamobile-min.jpg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      name="description"
      content="Vue3 + Vite5 + TypeScript5 + Element-Plus 的后台管理模板，配套接口文档和后端源码，vue-element-admin 的 Vue3 版本"
    />
    <meta
      name="keywords"
      content="vue,element-plus,typescript,vue-element-admin,vue3-element-admin"
    />
    <title>政企业务风险防控信息化系统</title>
  </head>

  <body>
    <div id="app">
      <div class="loader"></div>
    </div>
  </body>
  <script type="module" src="/src/main.ts"></script>

  <style>
    html,
    body,
    #app {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
    }

    .loader {
      position: relative;
      width: 40px;
      aspect-ratio: 0.577;
      overflow: hidden;
      clip-path: polygon(0 0, 100% 100%, 0 100%, 100% 0);
      animation: l19 2s infinite linear;
    }

    .loader::before {
      position: absolute;
      inset: -150%;
      content: "";
      background: repeating-conic-gradient(
        from 30deg,
        #ffabab 0 60deg,
        #abe4ff 0 120deg,
        #ff7373 0 180deg
      );
      animation: inherit;
      animation-direction: reverse;
    }

    @keyframes l19 {
      100% {
        transform: rotate(360deg);
      }
    }
  </style>
</html>
