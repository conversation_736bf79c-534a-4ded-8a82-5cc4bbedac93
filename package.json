{"name": "政企业务风险防控信息化系统", "version": "1.0", "private": true, "type": "module", "scripts": {"dev": "vite serve --mode development", "build:prod": "vite build --mode production && vue-tsc --noEmit", "prepare": "husky", "lint:eslint": "eslint  --fix --ext .ts,.js,.vue ./src ", "lint:prettier": "prettier --write \"**/*.{js,cjs,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint  \"**/*.{css,scss,vue}\" --fix", "lint:lint-staged": "lint-staged", "commit": "git-cz"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{cjs,json}": ["prettier --write"], "*.{vue,html}": ["eslint --fix", "prettier --write", "stylelint --fix"], "*.{scss,css}": ["stylelint --fix", "prettier --write"], "*.md": ["prettier --write"]}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^10.9.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "5.1.10", "animate.css": "^4.1.1", "axios": "^1.6.8", "echarts": "^5.5.0", "echarts-gl": "^2.0.9", "element-plus": "^2.6.1", "lodash-es": "^4.17.21", "moment": "^2.30.1", "net": "^1.0.2", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "path-to-regexp": "^6.2.1", "pinia": "^2.1.7", "sockjs-client": "1.6.1", "sortablejs": "^1.15.2", "stompjs": "^2.3.3", "vue": "^3.4.21", "vue-i18n": "9.9.1", "vue-router": "^4.3.0", "xlsx": "^0.18.5", "@ffmpeg/ffmpeg": "0.11.6", "@visactor/vchart": "1.12.7", "@visactor/vrender-core": "0.20.7", "@visactor/vmind": "1.2.13", "canvas": "^2.11.2"}, "devDependencies": {"@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "^18.6.3", "@iconify-json/ep": "^1.1.15", "@types/lodash": "^4.17.0", "@types/node": "^20.11.30", "@types/nprogress": "^0.2.3", "@types/path-browserify": "^1.0.2", "@types/sockjs-client": "^1.5.4", "@types/sortablejs": "^1.15.8", "@types/stompjs": "^2.3.9", "@typescript-eslint/eslint-plugin": "^7.3.1", "@typescript-eslint/parser": "^7.3.1", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "autoprefixer": "^10.4.18", "commitizen": "^4.3.0", "cz-git": "^1.9.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.23.0", "fast-glob": "^3.3.2", "husky": "^9.0.11", "lint-staged": "^15.2.2", "postcss": "^8.4.36", "postcss-html": "^1.6.0", "postcss-scss": "^4.0.9", "prettier": "^3.2.5", "sass": "^1.72.0", "stylelint": "^16.2.1", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^4.6.0", "stylelint-config-recommended-scss": "^14.0.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^36.0.0", "terser": "^5.29.2", "typescript": "^5.4.2", "unocss": "^0.58.6", "unplugin-auto-import": "^0.17.5", "unplugin-icons": "^0.18.5", "unplugin-vue-components": "^0.26.0", "vite": "^5.1.6", "vite-plugin-mock-dev-server": "^1.4.7", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^2.0.6"}, "engines": {"node": ">=18.0.0"}}