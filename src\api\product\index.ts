import request from "@/utils/request";
import {
    CategoriesAddReq,
    CategoriesUpdateReq,
    ProductSyncReq,
    SpecificationQuery
} from "@/api/product/types";

export function productQuery(data: any) {
    return request.service({
        url: "/api/v1/sync/EBOSS/ProductQuery",
        method: "post",
        data: data,
    });
}

export function productListQuery(data: any) {
    return request.service({
        url: "/api/v1/sync/EBOSS/ProductListQuery",
        method: "post",
        data: data,
    });
}


export function productQueryforOrder(data: any) {
    return request.service({
        url: "/api/v1/sync/EBOSS/ProductQueryforOrder",
        method: "post",
        data: data,
    });
}

/**
 * 询价接口
 * @param data
 */
export function accountSpecification(data: any) {
    return request.service({
        url: "/inquery/accountSpecification",
        method: "post",
        data: data,
    });
}

/**
 * 确认开通
 * @param data
 */
export function orderSync(data: any) {
    return request.service({
        url: "/api/v1/sync/EBOSS/OrderSync",
        method: "post",
        data: data,
    });
}

/**
 * 配置查看
 * @param data
 */
export function productDetailQuery(data: any) {
    return request.service({
        url: "/api/v1/sync/EBOSS/ProductDetailQuery",
        method: "post",
        data: data,
    });
}

/**
 * 新增时初始化规格/线路表头数据
 *
 * @param data
 */
export function specHeadQuery(data: any) {
    return request.service({
        url: "/api/v1/sync/EBOSS/SpecHeadQuery",
        method: "post",
        data: data,
    });
}

/**
 * 初始化规格/线路 表单数据
 *
 * @param data
 */
export function productSpecQuery() {
    return request.service({
        url: "/api/v1/sync/EBOSS/ProductSpecQuery",
        method: "get",
    });
}

/**
 * 配置查看
 * @param data
 */
export function categoriesQuery(data: SpecificationQuery) {
    return request.service({
        url: "/api/v1/sync/EBOSS/categoriesQuery",
        method: "post",
        data: data,
    });
}

/**
 * 配置查看单条数据
 * @param params
 */
export function categoriesQueryOne(params: any) {
    return request.service({
        url: "/api/v1/sync/EBOSS/categoriesQueryOne",
        method: "get",
        params: params,
    });
}

/**
 * 配置查看
 * @param data
 */
export function categoriesAdd(data: CategoriesAddReq) {
    return request.service({
        url: "/api/v1/sync/EBOSS/categoriesAdd",
        method: "post",
        data: data,
    });
}

/**
 * 配置查看
 * @param data
 */
export function categoriesUpdate(data: CategoriesUpdateReq) {
    return request.service({
        url: "/api/v1/sync/EBOSS/categoriesUpdate",
        method: "post",
        data: data,
    });
}

/**
 * 产品保存
 * @param data
 */
export function productSave(data: any) {
    return request.service({
        url: "/api/v1/sync/EBOSS/ProductSave",
        method: "post",
        data: data,
    });
}

/**
 * 产品上架
 * @param data
 */
export function productSync(data: ProductSyncReq) {
    return request.service({
        url: "/api/v1/sync/EBOSS/ProductSync",
        method: "post",
        data: data,
    });
}

/**
 * 产品下架
 * @param data
 */
export function productExpire(data: ProductSyncReq) {
    return request.service({
        url: "/api/v1/sync/EBOSS/ProductExpire",
        method: "post",
        data: data,
    });
}