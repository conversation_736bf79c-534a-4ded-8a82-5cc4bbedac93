<template>
	<div class="risk-work-order-list">
		<el-card class="h-full w-full">
			<template #header>
				<el-text
					class="mx-1"
					size="large"
					line-clamp="1"
					tag="b"
				>
					{{ orderTitle }}
				</el-text>
			</template>

			<el-container class="h-full">
				<el-header class="risk-work-order-header">
					<div style="overflow-x: auto; white-space: nowrap;"> <!-- 新增滚动容器 -->
						<el-form inline :model="queryForm" ref="queryFormRef" label-width="110">
						<div class="flex flex-row flex-nowrap">
							<el-form-item label="" prop="riskDesc">
								<el-input
									style="width: 210px;"
									v-model="queryForm.riskDesc"
									placeholder="风险工单问题关键字模糊查询"
									clearable
								>
								</el-input>
							</el-form-item>

							<el-form-item label="" prop="riskLevel">
								<dictionary
									style="width: 130px;"
									v-model="queryForm.riskLevel"
									type-code="riskWarningLevel"
									placeholder="风险预警等级"
								/>
							</el-form-item>

							<el-form-item label="" prop="riskBusinessType">
								<dictionary
									style="width: 130px;"
									v-model="queryForm.riskBusinessType"
									type-code="riskWarningType"
									placeholder="风险业务类型"
								/>
							</el-form-item>

							<el-form-item label="" prop="status">
								<el-select
									style="width: 110px;"
									v-model="queryForm.status"
									placeholder="选择状态"
								>
									<el-option
										v-for="option in statusOptions"
										:key="option.value"
										:label="option.label"
										:value="option.value"
									/>
								</el-select>
							</el-form-item>

							<el-form-item label="" prop="regionName">
								<el-select
									style="width: 110px;"
									v-model="queryForm.regionName"
									placeholder="选择区域"
									clearable
								>
									<el-option
										v-for="option in regionOptions"
										:key="option.value"
										:label="option.label"
										:value="option.value"
									/>
								</el-select>
							</el-form-item>

							<el-form-item label="" prop="businessDataTime">
								<el-date-picker
									style="width: 130px;"
									v-model="queryForm.businessDataTime"
									type="month"
									placeholder="业务数据时间"
								/>
							</el-form-item>

							<el-form-item label="" prop="warningDate">
								<el-date-picker
									v-model="queryForm.warningDate"
									type="daterange"
									placeholder="风险预警时间"
									range-separator="至"
									start-placeholder="请选择开始时间"
									end-placeholder="请选择结束时间"
									size="default"
									clearable
								/>
							</el-form-item>
							<div class="flex flex-row flex-nowrap" >
								<el-button
									type="primary"
									@click="query(1)"
								>
									查询
								</el-button>
								<el-button
									plain
									@click="resetForm(queryFormRef)"
								>
									重置
								</el-button>
							</div>
						</div>
						<el-divider style="margin: 10px 0"/>
						<div class="flex flex-row flex-nowrap" style="margin-top: 10px;">
							<el-button
								type="primary"
								@click="exportData()"
								:icon="Download"
								v-hasPerm="['risk:work:export']"
							>
								导出数据
							</el-button>
							<el-button
								type="primary"
								@click="batchClose()"
								:icon="CircleClose"
								v-hasPerm="['risk:work:batchClose']"
							>
								批量关闭
							</el-button>
						</div>
					</el-form>
					</div>
				</el-header>
				<el-main class="h-full">
					<el-table
						v-loading="loading"
						:data="tableData"
						border
						height="100%"
						style="width: 100%"
						tooltip-effect="dark"
						ref="multipleTableRef"
						@selection-change="handleSelectionChange"
					>
						<el-table-column type="selection" width="55"/>
						<el-table-column prop="recordId" label="工单编码" sortable align="center" width="133"/>
						<el-table-column
							prop="riskLevel"
							label="预警等级"
							sortable
							align="center"
							width="105"
							:formatter="riskLevelFormatter"
						>
						</el-table-column>
						<el-table-column prop="riskBusinessType" label="风险业务类型" sortable align="center" width="133"/>
						<el-table-column prop="riskWarningModel" label="风险预警模式" sortable align="center" width="133"/>
						<el-table-column prop="riskWarningTime" label="风险预警时间" sortable align="center" width="133"/>
						<el-table-column prop="businessDataTime" label="业务数据时间" sortable align="center" width="133"/>
						<el-table-column prop="riskWarningIssue" label="风险预警问题" align="left" width="330"/>
						<el-table-column
							prop="warningStatus"
							label="预警状态"
							sortable
							align="center"
							width="120"
							:formatter="warningStatusFormatter"
						/>
						<el-table-column prop="regionName" label="区域" sortable align="center" width="90"/>
						<el-table-column prop="rectificationDeadline" label="整改时限" sortable align="center" width="110"/>
						<el-table-column
							prop="isTimeout"
							label="超时情况"
							sortable
							align="center"
							width="115"
						>
							<template #default="scope">
                {{ typeCodeMap.get('riskOvertime').value[scope.row.isTimeout] }}
								<span :class="['timeout-situation',
								                 scope.row.isTimeout == 0 ? 'not-timed-out' :
							                   scope.row.isTimeout == 1 ? 'not-timed-out' :
							                   scope.row.isTimeout == 2 ? 'timed-out' : '']">

							     </span>
							</template>
						</el-table-column>
						<el-table-column label="操作" align="center" width="150">
							<template #default="scope">
								<el-button link
								           type="primary"
								           @click="detail(scope.row)"
								>详情
								</el-button>
								<el-button link
								           type="primary"
								           @click="closeWorkOrder(scope.row)"
								           v-hasPerm="['risk:work:close']"
								><span style="color: #F56C6C;">关闭</span>
								</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-main>
				<el-footer>
					<el-pagination
						style="float: right;line-height: 60px"
						v-model:current-page="current"
						v-model:page-size="size"
						:page-sizes="[10, 50, 100, 200]"
						:small="false"
						:background="true"
						size="default"
						layout="total, sizes, prev, pager, next, jumper"
						:total="total"
						@size-change="handleSizeChange"
						@current-change="handleCurrentChange"
					/>
				</el-footer>
			</el-container>

		</el-card>
	</div>
</template>

<script setup lang="ts">
import {useUserStore} from "@/store";
import {FormInstance, TableInstance} from "element-plus";
import moment from "moment/moment";
import {CircleClose, Download} from "@element-plus/icons-vue";
import {queryRegionList, revokeWorkOrder, riskDataExport, riskRecordListQueryPage} from "@/api/risk";
import {RiskWaringRecordListQuery, WorkOrderForm} from "@/api/risk/types";
import {initDictMap, stringToNumber} from "@/utils/commonUtils";
import {getDictOptionData} from "@/api/dict";

const orderTitle = ref('风险预警工单')

const userStore = useUserStore();

const queryForm = reactive<RiskWaringRecordListQuery>({
	riskDesc: '',
	riskLevel: null,
	riskBusinessType: null,
	status: null,
	regionName: null,
	businessDataTime: null,
});

const statusOptions = ref<[]>()

const initStatusOptions = () => {
	getDictOptionData().then((res) => {
		let code = res.code
		let valueArray = res.data
		if (code === '00000' && Array.isArray(valueArray)) {
			statusOptions.value = valueArray.map(value => (
				{
					value: stringToNumber(value),
					label: typeCodeMap.get('riskWarningStatus').value[stringToNumber(value)]
				}
			))
		}
	})
}

const regionOptions = ref<Array<any>>([])
//选择区域列表数据
const loadRegionSelectData = () => {
	queryRegionList().then(res => {
    if (res.code === '00000' && Array.isArray(res.data)) {
      // 转换为 { label, value } 数组
      regionOptions.value = res.data.map((item: string) => ({
        label: item,
        value: item
      }));
    } else {
      regionOptions.value = [];
    }
  })
}
const multipleTableRef = ref<TableInstance>()
const multipleSelection = ref<Array<any>>([])

const tableData = ref([
	{
		riskLevel: '一级预警',
		riskType: 'ICT业务',
		riskModel: 'M001_ICT项目上下游存在 AB/BA 关系',
		riskTime: '2025-06-08 08:01',
		businessTime: '202506',
		problem: '系统统计发现江苏省2025年5月1日-31日期间涉及“上游客户与下游供应商存在AB/BA关系”的ICT项目，存在其中A或B项目的IT预算金额≥100万且单个项目的IT预算金额差值≤30%。',
		riskStatus: '待发布',
		region: '江苏省',
		timeLimit: '2个月',
		timeoutSituation: '未超时',
	},
	{
		riskLevel: '一级预警',
		riskType: 'ICT业务',
		riskModel: 'M001_ICT项目上下游存在 AB/BA 关系',
		riskTime: '2025-06-08 08:01',
		businessTime: '202506',
		problem: '系统统计发现江西省2025年5月1日-31日期间涉及“上游客户与下游供应商存在AB/BA关系”的ICT项目，单个项目的IT预算金额差值≤30%且项目数超过5个。',
		riskStatus: '待发布',
		region: '江西省',
		timeLimit: '2个月',
		timeoutSituation: '未超时',
	},
	{
		riskLevel: '一级预警',
		riskType: 'ICT业务',
		riskModel: 'M002_ICT项目上下游单位存在关联关系',
		riskTime: '2025-06-08 08:01',
		businessTime: '202506',
		problem: '系统统计发现河北省2025年5月1日-31日期间涉及“上下游单位存在关联关系”的ICT项目，存在单个项目的IT预算金额≥300万。',
		riskStatus: '待发布',
		region: '河北省',
		timeLimit: '2个月',
		timeoutSituation: '未超时',
	},
	{
		riskLevel: '一级预警',
		riskType: '移动云业务',
		riskModel: 'M003_移动云订购云硬盘逻辑不合理',
		riskTime: '2025-06-08 08:01',
		businessTime: '202506',
		problem: '系统统计发现广东省2025年5月1日-31日期间涉及“上游客户与下游供应商存在AB/BA关系”的ICT项目，存在其中A或B项目的IT预算金额≥100万且单个项目的IT预算金额差值≤30%。',
		riskStatus: '待发布',
		region: '广东省',
		timeLimit: '2个月',
		timeoutSituation: '未超时',
	},
	{
		riskLevel: '二级预警',
		riskType: '移动云业务',
		riskModel: 'M003_移动云订购云硬盘逻辑不合理',
		riskTime: '2025-06-08 08:01',
		businessTime: '202506',
		problem: '系统统计发现广东省深圳市2025年5月1日-31日期间涉及“订购云硬盘逻辑不合理”的移动云新增订单流水超过20条。',
		riskStatus: '待发布',
		region: '广东省深圳市',
		timeLimit: '1个月',
		timeoutSituation: '未超时',
	},
	{
		riskLevel: '二级预警',
		riskType: '移动云业务',
		riskModel: 'M003_移动云订购云硬盘逻辑不合理',
		riskTime: '2025-06-08 08:01',
		businessTime: '202506',
		problem: '系统统计发现广东省珠海市2025年5月1日-31日期间涉及“订购云硬盘逻辑不合理”的移动云新增订单流水超过20条。',
		riskStatus: '待发布',
		region: '广东省珠海市',
		timeLimit: '1个月',
		timeoutSituation: '未超时',
	},
	{
		riskLevel: '二级预警',
		riskType: '移动云业务',
		riskModel: 'M003_移动云订购云硬盘逻辑不合理',
		riskTime: '2025-06-08 08:01',
		businessTime: '202506',
		problem: '系统统计发现广东省汕头市2025年5月1日-31日期间涉及“订购云硬盘逻辑不合理”的移动云新增订单流水超过20条。',
		riskStatus: '待发布',
		region: '广东省汕头市',
		timeLimit: '1个月',
		timeoutSituation: '未超时',
	},
]);

const riskLevelFormatter = (row: any, column: any, cellValue: any, index: number) => {
	return typeCodeMap.get('riskWarningLevel').value[cellValue]
}

const handleSelectionChange = (val: Array<any>) => {
	multipleSelection.value = val
}
const loading = ref(false)
const current = ref<number>(1)
const size = ref<number>(10)
const total = ref<number>(0)

const handleSizeChange = (val: number) => {
	size.value = val
	query()
}
const handleCurrentChange = (val: number) => {
	current.value = val
	query(val)
}

// 导出数据
const exportData = () => {
	if(!multipleSelection.value || multipleSelection.value.length === 0){
		ElMessage.warning('请先勾选数据')
		return
	}
	let recordIds = multipleSelection.value.map(item => item.recordId);
	let params: WorkOrderForm = {
		recordIds: recordIds
	}
	riskDataExport(params).then((response: any) => {
		const fileData = response.data;
		const fileName = decodeURI(
			response.headers["content-disposition"].split(";")[1].split("=")[1]
		);
		const fileType =
			"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8";

		const blob = new Blob([fileData], { type: fileType });
		const downloadUrl = window.URL.createObjectURL(blob);

		const downloadLink = document.createElement("a");
		downloadLink.href = downloadUrl;
		downloadLink.download = fileName;

		document.body.appendChild(downloadLink);
		downloadLink.click();

		document.body.removeChild(downloadLink);
		window.URL.revokeObjectURL(downloadUrl);
	});
}

// 批量关闭
const batchClose = () => {
	if(!multipleSelection.value || multipleSelection.value.length === 0){
		ElMessage.warning('请先勾选数据')
		return
	}
	let recordIds = multipleSelection.value.map(item => item.recordId);
	let params: WorkOrderForm = {
		recordIds: recordIds
	}
	revokeWorkOrder(params).then(response => {
		if (response.code === '00000') {
			ElMessage.success('批量关闭成功！')
		} else {
			ElMessage.error('批量关闭失败！')
		}
	})
}

const query = (index?: number) => {
	if (index) {
		current.value = index
	}
	let data: RiskWaringRecordListQuery = {
		...queryForm,
		pageNum: current.value,
		pageSize: size.value,
	}

	let warningDate = queryForm.warningDate;
	if (warningDate && warningDate.length === 2) {
		data.startDate = moment(warningDate[0]).format('YYYY-MM-DD')
		data.endDate = moment(warningDate[1]).format('YYYY-MM-DD')
	}

	delete data.warningDate
	if (queryForm.businessDataTime) {
		data.businessDataTime = moment(queryForm.businessDataTime).format('YYYY-MM')
	}

	loading.value = true
	riskRecordListQueryPage(data).then(response => {
		let pageData = response.data;
		if (pageData) {
			tableData.value = pageData.records
			current.value = pageData.current
			size.value = pageData.size
			total.value = pageData.total
		}
	}).finally(() => {
		loading.value = false
	})
}
const queryFormRef = ref<FormInstance>()

const resetForm = (formEl: FormInstance | undefined) => {
	if (!formEl) return
	formEl.resetFields()
}

//const url = ref<string>('http://172.21.154.39:2444/jlwzng/operations/?ticket=')
const detail = (rowData: object) => {
  userStore.applyTicket()
    .then((res: any) => {
      window.open( res  + rowData.processCode)
      console.log("121321----"+res +  + rowData.processCode)
    })

}

const closeWorkOrder = (rowData: object) => {
	let params: WorkOrderForm = {
		recordIds: [rowData.recordId]
	}
	revokeWorkOrder(params).then(response => {
		if (response.code === '00000') {
			ElMessage.success('关闭成功！')
		} else {
			ElMessage.error('关闭失败！')
		}
	})
}

const warningStatusFormatter = (row: any, column: any, cellValue: any, index: number) => {
	return typeCodeMap.get('riskWarningStatus').value[cellValue]
}

const typeCodeMap = reactive<Map<string, any>>(new Map([
	["riskWarningLevel", ref<object>({})],
	["riskOvertime", ref<object>({})],
	["riskWarningStatus", ref<object>({})],
]));

onMounted(() => {
	initDictMap(typeCodeMap)
	query()
	//加载区域列表数据
	initStatusOptions()
	loadRegionSelectData()
})

</script>

<style scoped lang="scss">

.risk-work-order-list {
	height: calc(100vh - 50px);

	:deep(.el-card__body) {
		height: calc(100% - 78.5px);
		padding: 10px;
	}

	:deep(.el-form-item) {
		margin-right: 8px;
		margin-bottom: 0;
	}

  :deep(.el-input__inner::placeholder) {
    color: rgba(0,0,0,0.7) !important;
    -webkit-text-fill-color: rgba(0,0,0,0.7) !important;
  }

  // 修改select的placeholder颜色 - 针对未选择时显示的文本
  :deep(.el-select .el-input__inner) {
    color: rgba(0, 0, 0, 0.7) !important;
  }

  // 修改select的placeholder - 针对el-select__placeholder元素
  :deep(.el-select__placeholder) {
    color: rgba(0, 0, 0, 0.7) !important;
  }

  // 修改date-picker的placeholder颜色
  :deep(.el-range-input) {
    color: rgba(0, 0, 0, 0.7) !important;
    -webkit-text-fill-color: rgba(0, 0, 0, 0.7) !important;
  }

  // 确保自定义组件dictionary中的placeholder也被修改
  :deep(.dictionary .el-input__inner::placeholder) {
    color: rgba(0, 0, 0, 0.7) !important;
    -webkit-text-fill-color: rgba(0, 0, 0, 0.7) !important;
  }


	.risk-work-order-header {
		height: 80px;
	}
	
	
	.flex.flex-row.flex-nowrap {
		min-width: 1440px; /* 根据实际内容调整 */
		padding-bottom: 5px; /* 避免滚动条遮挡 */
	}
	
	@media (max-width: 1440px) {
		.el-form-item {
			margin-right: 4px !important;
		}
		.el-input, .el-select, .el-date-editor {
			width: 110px !important; /* 缩小元素宽度 */
		}
	}
	
	.timeout-situation {
	
	}
	
	.timeout-situation::before {
		content: '';
		display: inline-block;
		width: 8px;
		height: 8px;
		border-radius: 50%;
		margin-right: 6px; /* 可选：右边距 */
		vertical-align: middle; /* 可选：垂直对齐方式 */
	}
	
	--not-timed-out-color: #00ff00;
	--timed-out-color: #F56C6C;
	
	.not-timed-out::before {
		background-color: var(--not-timed-out-color) !important;
	}
	
	.timed-out::before {
		background-color: var(--timed-out-color) !important;
	}
}

</style>
