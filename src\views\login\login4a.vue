<script setup lang="ts">
// oa系统请求
import {LocationQuery, LocationQueryValue, useRoute, useRouter} from "vue-router";
import {useUserStore} from "@/store";
import {LoginData} from "@/api/auth/types";

const routeNew = useRouter();
const paramNew = new URLSearchParams(routeNew.currentRoute.value.query);
const loginName = paramNew.get('loginName'); // 从 URL 参数获取登录名

const userStore = useUserStore();
const router = useRouter();
const route = useRoute();
// 调用实际登录接口
if (loginName) {
  const loginDataA = ref<LoginData>({
    username: loginName,
    password: ''
  });

  userStore
    .login(loginDataA.value)
    .then(() => {
      const query: LocationQuery = route.query;
      const redirect = (query.redirect as LocationQueryValue) ?? "/";
      const otherQueryParams = Object.keys(query).reduce(
        (acc: any, cur: string) => {
          if (cur !== "redirect") {
            acc[cur] = query[cur];
          }
          return acc;
        },
        {}
      );
      router.push({path: redirect, query: otherQueryParams});
    })
}
</script>

<template>

</template>

<style scoped lang="scss">

</style>
