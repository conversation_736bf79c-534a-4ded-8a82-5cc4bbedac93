<template>
	<div>
		<el-container>
			<el-main>
				<el-row :gutter="24">
					<el-col :offset="1" :span="12">
						<el-card style="width: 100%;">
							<template #header>
								<div class="card-header">
									<el-text
										class="mx-1"
										size="large"
										line-clamp="1"
										tag="b"
									>
										{{ headerTitle }}
									</el-text>
								</div>
							</template>

							<!--cpu使用情况饼状图-->
							<el-row :gutter="24">
								<el-col :span="24">
									<pie-chart
										id="cpuPieChart"
										height="400px"
										width="100%"
										class="bg-[var(--el-bg-color-overlay)]"
										:card-title="cpuUsageTitle"
										:chart-data="cpuChartData"
									></pie-chart>
								</el-col>
							</el-row>

							<!--内存使用情况饼状图-->
							<el-row :gutter="24" class="mt-10">
								<el-col :span="24">
									<pie-chart
										id="memoryPieChart"
										height="400px"
										width="100%"
										:card-title="memoryUsageTitle"
										class="bg-[var(--el-bg-color-overlay)]"
										:chart-data="memoryChartData"
									></pie-chart>
								</el-col>
							</el-row>

							<!--GPU使用情况饼状图-->
							<el-row :gutter="24" class="mt-10" v-if="gpuChartData.length > 0">
								<el-col :span="24">
									<pie-chart
										id="gpuPieChart"
										height="400px"
										width="100%"
										:card-title="gpuUsageTitle"
										class="bg-[var(--el-bg-color-overlay)]"
										:chart-data="gpuChartData"
									></pie-chart>
								</el-col>
							</el-row>
						</el-card>
					</el-col>

					<el-col :span="9">
						<el-row :gutter="24">
							<el-col :span="24">
								<el-card style="width: 100%;">
									<el-row :gutter="24">
										<el-col :span="3">
											<el-avatar v-if="userStore.user.avatar" :size="50" :src="userStore.user.avatar"/>
											<el-avatar v-else :size="50" :src="getAssetsFile('default_avatar.png')"/>
										</el-col>
										<el-col :span="12">
											<el-text
												class="mx-1 user-name-content"
												size="large"
												line-clamp="1"
												tag="b"
											>
												{{ userNameText }} {{ userStore.user.username }}
											</el-text>
										</el-col>
									</el-row>

									<el-row :gutter="24" style="margin-top: 15px;" v-for="rowList in tableDataByRowList">
										<el-col :span="6" v-for="item in rowList">
											<el-button plain @click="toManagePage(item.url)">
												{{ item.name }}
											</el-button>
										</el-col>
									</el-row>
								</el-card>
							</el-col>
						</el-row>

						<!--待办提醒-->
						<el-row :gutter="24" style="margin-top: 15px;">
							<el-col :span="24">
								<el-card style="width: 100%;">
									<template #header>
										<div class="card-header">
											<el-text
												class="mx-1"
												size="large"
												line-clamp="1"
												tag="b"
											>
												{{ todoTitle }}
											</el-text>
										</div>
									</template>
									<p>
										<el-button link @click="toManagePage('/operator/work')">
											<el-text
												class="mx-1"
												type="primary"
												size="default"
												line-clamp="1"
												tag="b"
											>
												{{ toBeHandleText }} {{ workOrderNumber }}条
											</el-text>
										</el-button>
									</p>
								</el-card>
							</el-col>
						</el-row>
					</el-col>
				</el-row>
			</el-main>
		</el-container>
	</div>
</template>

<script setup lang="ts">
import {useUserStore} from "@/store";
import PieChart from "@/views/operator/center/components/PieChart.vue";
import {workOrderQuery} from "@/api/workOrder";
import {getAssetsFile, getNewArray} from "@/utils/commonUtils";
import {getAllClusterResource} from "@/api/user";

const router = useRouter();
const userStore = useUserStore();

const headerTitle = ref<string>('集群资源使用情况')
const todoTitle = ref<string>('待办提醒')
const workOrderNumber = ref<Number>(0)

const cpuUsageTitle = ref<string>('CPU使用情况')
const memoryUsageTitle = ref<string>('内存使用情况')
const gpuUsageTitle = ref<string>('GPU使用情况')

const userNameText = ref<string>('用户名：')
const toBeHandleText = ref<string>('待处理工单：')

const tableDataByRowList = ref()

const routerInfoList = ref([
	{
		url: '/operator/op-order',
		name: '订单管理',
	},
	{
		url: '/operator/op-bill',
		name: '账单管理',
	},
	{
		url: '/operator/work',
		name: '工单管理',
	},
	{
		url: '/operator/config',
		name: '产品配置管理',
	},
	{
		url: '/operator/spec',
		name: '规格配置',
	},
	{
		url: '/operator/class',
		name: '产品大类查询',
	},
	{
		url: '/operator/gpu-rank',
		name: '算力排行榜',
	},
	{
		url: '/operator/large-model',
		name: '大模型介绍',
	},
])


const toManagePage = (url: string) => {
	if (url) {
		router.push(
			{
				path: url,
			}
		)
	}
}

const query = () => {
	let data: object = {
		level: 1
	}
	workOrderQuery(data).then(response => {
		let pageData = response.data;
		if (pageData) {
			workOrderNumber.value = pageData.undealCount
		}
	})
}
const cpuChartData = ref<Array<any>>([])
const memoryChartData = ref<Array<any>>([])
const gpuChartData = ref<Array<any>>([])

const initChartData = () => {
	getAllClusterResource().then(response => {
		let data = response.data
		if(data && data.length > 0){
			let datum = data[0]
			if (datum) {
				let cpuAll = Number(datum.cpuAll)
				let cpuUsed = Number(datum.cpuUsed)
				let cpuUnused = cpuAll - cpuUsed
				cpuChartData.value = [
					{
						value: cpuUsed,
						name: '已使用'
					},
					{
						value: cpuUnused,
						name: '未使用'
					},
				]
				let memoryAll = datum.memoryAll
				let memoryAllNumber = Number(memoryAll.substring(0, memoryAll.length - 2))
				let memoryUsed = datum.memoryUsed
				let memoryUsedNumber = Number(memoryUsed.substring(0, memoryUsed.length - 2));
				let memoryUnused = memoryAllNumber - memoryUsedNumber
				memoryChartData.value = [
					{
						value: memoryUsedNumber,
						name: '已使用'
					},
					{
						value: memoryUnused,
						name: '未使用'
					},
				]
			}
		}
	})
}

const initRouterBtn = () => {
	tableDataByRowList.value = getNewArray(routerInfoList.value, 4)
}

onMounted(() => {
	query()
	initRouterBtn()
	initChartData()
})


</script>

<style scoped lang="scss">

.user-name-content {
	line-height: 56.5px;
}

</style>