<!-- 饼图 -->
<template>
  <el-card>
    <template #header> 产品分类图 </template>
    <div :id="id" :class="className" :style="{ height, width }"></div>
  </el-card>
</template>

<script setup lang="ts">
import * as echarts from "echarts";

const props = defineProps({
  id: {
    type: String,
    default: "pieChart",
  },
  className: {
    type: String,
    default: "",
  },
  width: {
    type: String,
    default: "200px",
    required: true,
  },
  height: {
    type: String,
    default: "200px",
    required: true,
  },
});
const options = {
  grid: {
    left: "2%",
    right: "2%",
    bottom: "10%",
    containLabel: true,
  },
  legend: {
    top: "bottom",
    textStyle: {
      color: "#999",
    },
  },
  tooltip: {
    trigger: 'item',
    formatter: '{b} : {d}%',
  },
  series: [
    {
      name: "Nightingale Chart",
      type: "pie",
      radius: "55%",
      center: ["50%", "50%"],
      roseType: "area",
      itemStyle: {
        borderRadius: 1,
        color: function (params: any) {
          //自定义颜色
          const colorList = ["#409EFF", "#67C23A", "#E6A23C", "#F56C6C","#C7E27A"];
          return colorList[params.dataIndex];
        },
      },
      data: [
        { value: 26, name: "产品A" },
        { value: 27, name: "产品B" },
        { value: 24, name: "产品C" },
        { value: 23, name: "产品D" },
        { value: 23, name: "产品E" },
      ],
    },
  ],
};

const chart = ref<any>("");

onMounted(() => {
  chart.value = markRaw(
    echarts.init(document.getElementById(props.id) as HTMLDivElement)
  );

  chart.value.setOption(options);

  window.addEventListener("resize", () => {
    chart.value.resize();
  });
});

onActivated(() => {
  if (chart.value) {
    chart.value.resize();
  }
});
</script>
