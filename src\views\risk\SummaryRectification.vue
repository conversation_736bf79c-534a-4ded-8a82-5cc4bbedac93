<template>
	<!--整改概要-->
	<div class="summary-rectification">
		<el-container>
			<el-header class="header">
				<el-form :model="queryForm" ref="queryFormRef" label-width="110">
					<el-row :gutter="24" style="margin-top: 15px;">
						<el-col :span="4">
							<el-form-item>
								<el-button
									type="primary"
									text
									@click="goBack"
								>
									< 返回
								</el-button>
							</el-form-item>
						</el-col>
						<el-col :offset="2" :span="4">
							<el-form-item label="切换区域：" prop="region">
								<el-select
									v-model="queryForm.region"
									placeholder="请选择"
									clearable
									@change="regionChange"
									:disabled="true"
								>
									<el-option
										v-for="option in regionOptions"
										:key="option.value"
										:label="option.label"
										:value="option.value"
									/>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="选择时间：" prop="dateRange">
								<el-date-picker
									v-model="queryForm.dateRange"
									type="monthrange"
									style="width: 50%"
									range-separator="至"
									start-placeholder="开始日期"
									end-placeholder="结束日期"
									:size="dateSize"
									@change="dateRangeChange"
								/>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
			</el-header>
			<el-main>
				<el-card style="width: 100%;" class="summary-card">
					<template #header>
						<img class="logo" :src="getAssetsFile('risk/title_logo.png')" alt="logo"/>
						<el-text
							class="mx-1"
							size="large"
							line-clamp="1"
							tag="b"
						>
							{{ provinceName }}{{ summaryTitle }}{{ dataRangeStr }}
						</el-text>
					</template>
					<div class="card-show-list">
						<risk-summary-card
							v-for="item in summaryCardArray"
							:title="item.title"
							:count="item.count"
							:img-url="item.imgUrl"
							style="margin: 5px;padding: 10px;width: 24%;"
							:risk-color="item.riskColor"
						>
						</risk-summary-card>
					</div>

					<div class="business-card-show-list">
						<business-card
							class="business-card"
							v-for="item in businessCardArray"
							:title="item.originTitle"
							:img-url="item.imgUrl"
							width="25%"
							:completion-rate="item.completionRate"
							:level-one-cnt="item.firstLevelCount"
							:level-two-cnt="item.secondLevelCount"
							:level-three-cnt="item.thirdCount"
							:work-order-count="item.cumulativeCount"
						>
						</business-card>
					</div>
				</el-card>

				<el-card class="detail-list">
					<template #header>
						<el-form :model="queryForm" ref="formRef" label-width="auto">
							<el-row :gutter="24">
								<el-col :span="5">
									<el-form-item>
										<img class="logo" :src="getAssetsFile('risk/business_log.png')" alt="logo"/>
										<el-text
											class="mx-1"
											size="large"
											line-clamp="1"
											tag="b"
										>
											{{ provinceName }}月预警工单明细清单
										</el-text>
									</el-form-item>
								</el-col>
								<el-col :offset="12" :span="4">
									<el-form-item label="整改状态：" prop="region">
										<dictionary
											v-model="queryForm.filter"
											type-code="rectificationStatus"
											:clearable="false"
											@change="filterChange"
										/>
									</el-form-item>
								</el-col>
								<el-col :span="2">
									<el-form-item>
										<el-button
											type="primary"
											@click="exportData"
											:icon="Download"
										>
											导出数据
										</el-button>
									</el-form-item>
								</el-col>
							</el-row>
						</el-form>
					</template>

					<el-table
						v-loading="loading"
						:data="tableData"
						border
						height="480"
						style="width: 100%"
						ref="multipleTableRef"
						@selection-change="handleSelectionChange"
					>
						<el-table-column type="selection" width="55"/>
						<el-table-column prop="recordId" label="工单编码" sortable align="center" width="133"/>
						<el-table-column
							prop="riskLevel"
							label="预警等级"
							sortable
							align="center"
							width="105"
							:formatter="riskLevelFormatter"
						>
						</el-table-column>
						<el-table-column prop="riskBusinessType" label="风险业务类型" sortable align="center"
						                 width="133"/>
						<el-table-column prop="riskWarningModel" label="风险预警模式" sortable align="center"
						                 width="133"/>
						<el-table-column prop="riskWarningTime" label="风险预警时间" sortable align="center"
						                 width="133"/>
						<el-table-column prop="businessDataTime" label="业务数据时间" sortable align="center"
						                 width="133"/>
						<el-table-column prop="riskWarningIssue" label="风险预警问题" align="left" width="330"/>
						<el-table-column
							prop="warningStatus"
							label="预警状态"
							sortable
							align="center"
							width="120"
							:formatter="warningStatusFormatter"
						/>
						<el-table-column prop="regionName" label="区域" sortable align="center" width="90"/>
						<el-table-column prop="rectificationDeadline" label="整改时限" sortable align="center"
						                 width="110"/>
						<el-table-column
							prop="isTimeout"
							label="超时情况"
							sortable
							align="center"
							width="115"
						>
							<template #default="scope">
                {{ typeCodeMap.get('riskOvertime').value[scope.row.isTimeout] }}
								<span :class="['timeout-situation',
								                 scope.row.isTimeout == 0 ? 'not-timed-out' :
							                   scope.row.isTimeout == 1 ? 'not-timed-out' :
							                   scope.row.isTimeout == 2 ? 'timed-out' : '']">

							     </span>
							</template>
						</el-table-column>
						<el-table-column label="操作" align="center" width="150">
							<template #default="scope">
								<el-button
									link
									type='primary'
									@click="detail(scope.row)"
								>
									详情
								</el-button>
								<el-button
									link
									type='primary'
									@click="close(scope.row)"
								>
									<span style="color: #F56C6C;">关闭</span>
								</el-button>
							</template>
						</el-table-column>
					</el-table>
					<el-pagination
						style="float: right;line-height: 60px"
						v-model:current-page="current"
						v-model:page-size="size"
						:page-sizes="[10, 50, 100, 200]"
						:small="false"
						:background="true"
						size="default"
						layout="total, sizes, prev, pager, next, jumper"
						:total="total"
						@size-change="handleSizeChange"
						@current-change="handleCurrentChange"
					/>
				</el-card>
			</el-main>
		</el-container>
	</div>
</template>

<script setup lang="ts">

import RiskSummaryCard from "@/views/risk/components/RiskSummaryCard.vue";
import BusinessCard from "@/views/risk/components/BusinessCard.vue";
import moment from "moment/moment";
import {Download} from "@element-plus/icons-vue";
import {getAssetsFile, initDictMap} from "@/utils/commonUtils";
import {useRouter} from "vue-router";
import {
	revokeWorkOrder,
	riskDataExport,
	riskRecordListQueryPage,
	riskRegionSummaryRcfQuery
} from "@/api/risk";
import {RiskSummaryQuery, RiskWaringRecordListQuery, WorkOrderForm} from "@/api/risk/types";
import {TableInstance} from "element-plus";

const dateSize = ref<'default' | 'large' | 'small'>('default')

const router = useRouter();

const riskMonth = reactive(history.state.riskMonth);
const regionLevel = reactive(history.state.regionLevel);
const provinceName = reactive(history.state.provinceName);

const queryForm = reactive<any>({
	region: provinceName,
	dateRange: [moment(riskMonth), moment(riskMonth)],
	filter: 'ALL',
	contractDateRange: null,
});

const tableData = ref([]);

const loading = ref(false)
const current = ref<number>(1)
const size = ref<number>(10)
const total = ref<number>(0)

const goBack = () => {
	router.push({
		path: '/risk-control-platform',
	});
}

const handleSizeChange = (val: number) => {
	size.value = val
	query()
}
const handleCurrentChange = (val: number) => {
	current.value = val
	query(val)
}

const queryRiskSummary = () => {
	let params: RiskSummaryQuery = {
		regionName: queryForm.region,
		regionLevel: regionLevel,
	}

	let dateRange = queryForm.dateRange;
	if (dateRange) {
		params.startDate = moment(dateRange[0]).format('YYYY-MM')
		params.endDate = moment(dateRange[1]).format('YYYY-MM')
	}
	riskRegionSummaryRcfQuery(params).then(response => {
		let data = response.data
		if (data) {
			summaryCardArray.value.forEach(item => {
				item.count = data[item.key]
			})
			businessCardArray.value.forEach(item => {
				let modelObj = data[item.key]
				Object.keys(modelObj).forEach(key => {
					item[key] = modelObj[key]
				})
			})
		}
	})
}

const multipleTableRef = ref<TableInstance>()
const multipleSelection = ref<Array<any>>([])

const handleSelectionChange = (val: Array<any>) => {
	multipleSelection.value = val
}

// 导出
const exportData = () => {
	if (!multipleSelection.value || multipleSelection.value.length === 0) {
		ElMessage.warning('请先勾选数据')
		return
	}
	let recordIds = multipleSelection.value.map(item => item.recordId);
	let params: WorkOrderForm = {
		recordIds: recordIds
	}
	riskDataExport(params).then((response: any) => {
		const fileData = response.data;
		const fileName = decodeURI(
			response.headers["content-disposition"].split(";")[1].split("=")[1]
		);
		const fileType =
			"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8";

		const blob = new Blob([fileData], {type: fileType});
		const downloadUrl = window.URL.createObjectURL(blob);

		const downloadLink = document.createElement("a");
		downloadLink.href = downloadUrl;
		downloadLink.download = fileName;

		document.body.appendChild(downloadLink);
		downloadLink.click();

		document.body.removeChild(downloadLink);
		window.URL.revokeObjectURL(downloadUrl);
	});
}

const query = (index?: number) => {
	if (index) {
		current.value = index
	}

	let params: RiskWaringRecordListQuery = {
		regionName: queryForm.region,
		status: queryForm.filter,
		regionLevel: regionLevel,
		pageNum: current.value,
		pageSize: size.value
	}

	let dateRange = queryForm.dateRange;
	if (dateRange) {
		params.startDate = moment(dateRange[0]).format('YYYY-MM')
		params.endDate = moment(dateRange[1]).format('YYYY-MM')
	}

	loading.value = true
	riskRecordListQueryPage(params).then(response => {
		let pageData = response.data
		if (pageData) {
			tableData.value = pageData.records
			current.value = pageData.current
			size.value = pageData.size
			total.value = pageData.total
		}
	}).finally(() => {
		loading.value = false
	})
}

const url = ref<string>('http://125.34.23.236:35790/demo/operations/#/order/detail?status=1')
const detail = (rowData: object) => {
	window.open(url.value)
}

const close = (data: any) => {
	let params: WorkOrderForm = {
		recordIds: [data.recordId]
	}
	revokeWorkOrder(params).then(response => {
		if (response.code === '00000') {
			ElMessage.success('关闭成功！')
		} else {
			ElMessage.error('关闭失败！')
		}
	})
}

const regionOptions = ref<Array<any>>([
	{
		label: '江苏',
		value: '江苏',
	}
])

const summaryCardArray = ref<Array<any>>([
	{
		key: 'finishedRiskRecordNumber',
		imgUrl: 'risk/summary-1.png',
		title: '已整改风险预警单数',
		count: 0,
		riskColor: 2,
	},
	{
		key: 'ictFinishedRiskRecordNumber',
		imgUrl: 'risk/summary-2.png',
		title: 'ICT业务-已整改风险预警单数',
		count: 0,
		riskColor: 2,
	},
	{
		key: 'mcFinishedRiskRecordNumber',
		imgUrl: 'risk/summary-3.png',
		title: '移动云-已整改业务风险预警单数',
		count: 0,
		riskColor: 2,
	},
	{
		key: 'ictOverTimeRiskRecordNumber',
		imgUrl: 'risk/summary-4.png',
		title: 'ICT业务-超时风险预警单数',
		count: 0,
		riskColor: 1,
	},
	{
		key: 'mcOverTimeRiskRecordNumber',
		imgUrl: 'risk/summary-5.png',
		title: '移动云业务-超时风险预警单数',
		count: 0,
		riskColor: 1,
	},
])

const businessCardArray = ref<Array<any>>([
	{
		key: 'ictAbModel',
		imgUrl: 'risk/ict.png',
		originTitle: 'ICT业务-上下游存在 AB/BA 关系',
	},
	{
		key: 'ictUpDownModel',
		imgUrl: 'risk/ict.png',
		originTitle: 'ICT业务-上下游存在关联关系',
	},
	{
		key: 'mcOrderModel',
		imgUrl: 'risk/cloud.png',
		originTitle: '移动云业务-产品订购组合不合理',
	},
	{
		key: 'mcIncomeModel',
		imgUrl: 'risk/cloud.png',
		originTitle: '移动云业务-产品收入异动',
	},
])

const regionChange = (value: any) => {

}

const riskLevelFormatter = (row: any, column: any, cellValue: any, index: number) => {
	return typeCodeMap.get('riskWarningLevel').value[cellValue]
}

const warningStatusFormatter = (row: any, column: any, cellValue: any, index: number) => {
	return typeCodeMap.get('riskWarningStatus').value[cellValue]
}

// 江苏省风险概要 | 江苏省风险整改概要
const summaryTitle = ref<string>('风险整改概要')

const dataRangeStr = ref<string>('')

const dateRangeChange = (value: any) => {
	if (value && value.length === 2) {
		let startTime = moment(value[0]).format('YYYY-MM')
		let endTime = moment(value[1]).format('YYYY-MM')
		dataRangeStr.value = '(' + startTime + '-' + endTime + ')'
	} else {
		dataRangeStr.value = ''
	}
	query(1)
}

const filterChange = (value: any) => {
	query(1)
}

const typeCodeMap = reactive<Map<string, any>>(new Map([
	["riskWarningLevel", ref<object>({})],
	["riskOvertime", ref<object>({})],
	["riskWarningStatus", ref<object>({})],
]));

onMounted(() => {
	initDictMap(typeCodeMap)
	queryRiskSummary()
	query()
})


</script>

<style scoped lang="scss">
.summary-rectification {
	height: 100%;
	width: 100%;
	max-width: 1440px;

	.header {
		height: 60px;
		border-bottom: 1px solid #cccccc;

		.center-vertical {
			display: flex;
			align-items: center;
		}

	}

	.logo {
		width: 1.5em;
		height: 1.5em;
		margin-right: 4px;
	}


	.summary-card {
		border-radius: 25px;
		background: url("@/assets/risk/backgroud.png") no-repeat center;
		background-size: 100% 100%;

		.card-show-list {
			display: flex;
			flex-direction: row;
			flex-wrap: nowrap;
		}

		.business-card-show-list {
			margin-top: 20px;
			display: flex;
			flex-direction: row;
			flex-wrap: nowrap;
			justify-content: space-around;

			.business-card {
				position: relative;

			}

		}
	}

	.business-card-show-list > :not(:last-child)::after {
		content: "";
		position: absolute;
		top: 25%;
		right: -5%;
		height: 108px;
		opacity: 0.20000000298023224;
		border-radius: 0;
		border: 1px solid #0079FD;
	}

	.detail-list {
		width: 100%;
		margin-top: 35px;

		:deep(.el-card__header) {
			padding-bottom: 0;
		}

	}
	
	.timeout-situation {
	
	}
	
	.timeout-situation::before {
		content: '';
		display: inline-block;
		width: 8px;
		height: 8px;
		border-radius: 50%;
		margin-right: 6px; /* 可选：右边距 */
		vertical-align: middle; /* 可选：垂直对齐方式 */
	}
	
	--not-timed-out-color: #00ff00;
	--timed-out-color: #F56C6C;
	
	.not-timed-out::before {
		background-color: var(--not-timed-out-color) !important;
	}
	
	.timed-out::before {
		background-color: var(--timed-out-color) !important;
	}
}
</style>
