import request from "@/utils/request";
import { AxiosPromise } from "axios";
import { CaptchaResult, LoginData, LoginResult } from "./types";

/**
 * 登录API
 *
 * @param data {LoginData}
 * @returns
 */
export function loginApi(data: LoginData): AxiosPromise<LoginResult> {
  const formData = new FormData();
  formData.append("username", data.username);
  formData.append("password", data.password);
  formData.append("captchaKey", data.captchaKey || "");
  formData.append("captchaCode", data.captchaCode || "");
  return request.service({
    url: "/api/v1/auth/login",
    method: "post",
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

/**
 * 注销API
 */
export function logoutApi() {
  return request.service({
    url: "/api/v1/auth/logout",
    method: "delete",
  });
}

/**
 * 获取验证码
 */
export function getCaptchaApi(): AxiosPromise<CaptchaResult> {
  return request.service({
    url: "/api/v1/auth/captcha",
    method: "get",
  });
}

/**
 * oa系统登录验证
 * @param ticket
 */
export function verifyTickett(ticket: string) {
  return request.service({
    url: "/api/v1/auth/verifyTicket",
    method: "get",
    params: {
      ticket
    },
  });
}

/**
 * 校验用户类型
 * @param staffCode
 */
export function verifyUserTypee(staffCode: string) {
  return request.service({
    url: "/api/v1/auth/verifyUserType",
    method: "get",
    params: {
      staffCode
    },
  });
}

export function verifySmsCodee(smsCode: string) {
  return request.service({
    url: "/api/v1/auth/verifySmsCode",
    method: "get",
    params: {
      smsCode
    },
  });
}

export function applyTickett() {
  return request.service({
    url: "/api/v1/auth/applyTicket",
    method: "get",
    params: {

    },
  });
}

export function getUserNamee() {
  return request.service({
    url: "/api/v1/auth/getUserName",
    method: "get",
    params: {
    },
  });
}

export function ebossSsoServerVerify(staffCode: string): AxiosPromise<CaptchaResult> {
  return request.service({
    url: "/api/v1/auth/ebossSsoServerVerify",
    method: "get",
    params: {
      staffCode: staffCode
    }
  });
}

export function getSmsCode(params: any): AxiosPromise<CaptchaResult> {
  return request.service({
    url: "/api/v1/auth/getSmsCode",
    method: "get",
    params: params,
  });
}

export function verifySmsCode(params: any): AxiosPromise<CaptchaResult> {
  return request.service({
    url: "/api/v1/auth/verifySmsCode",
    method: "get",
    params: params
  });
}
