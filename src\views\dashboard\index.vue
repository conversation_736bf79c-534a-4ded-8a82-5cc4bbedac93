<template>
	<div class="dashboard-container">
		<el-container style="min-width: 1024px;">
			<el-main>
				<div class="flex flex-row flex-nowrap justify-around main-row">
					<div class="w-23%">
						<monthly-increase-card
							title="月新增预警单总个数"
							:mom="indicatorData.monthMoMRiskRecordNumber"
							:count="indicatorData.monthNewRiskRecordNumber"
						></monthly-increase-card>
					</div>
					<div class="bell-card-list w-76%">
						<div v-for="(item, index) in bellList" class="bell-card">
							<div>
								<img class="logo" :src="getAssetsFile(item.imgUrl)" alt="logo"/>
							</div>
							<div class="title">
								{{ item.title }}
							</div>
							<div class="cnt">
								{{ item.count }}
							</div>
							<div>
								<img
									:class="index === 0 ? 'bottom-pic' : 'bottom-pic-bar'"
									:src="getAssetsFile(item.bottomUrl)"
									alt="bottomPic"
								/>
							</div>
						</div>
					</div>
				</div>
<!--				<el-row :gutter="24" class="main-row">
					<el-col :span="6">
						<monthly-increase-card
							title="月新增预警单总个数"
							:mom="indicatorData.monthMoMRiskRecordNumber"
							:count="indicatorData.monthNewRiskRecordNumber"
						></monthly-increase-card>
					</el-col>
					<el-col :span="18">
						<div class="bell-card-list">
							<div v-for="(item, index) in bellList" class="bell-card">
								<div>
									<img class="logo" :src="getAssetsFile(item.imgUrl)" alt="logo"/>
								</div>
								<div class="title">
									{{ item.title }}
								</div>
								<div class="cnt">
									{{ item.count }}
								</div>
								<div>
									<img
										:class="index === 0 ? 'bottom-pic' : 'bottom-pic-bar'"
										:src="getAssetsFile(item.bottomUrl)"
										alt="bottomPic"
									/>
								</div>
							</div>
						</div>
					</el-col>
				</el-row>-->

				<el-row :gutter="24" class="first-row-card">
					<el-col :span="12">
						<el-card class="custom-card sec-custom-card">
							<template #header>
								<div class="flex flex-row flex-nowrap justify-between">
									<div>
										<img class="card-log" :src="getAssetsFile('index/bell_blue.png')" alt="待办"/>
										<span class="card-title">待办事项</span>
									</div>
									
									<div>
										<!--<el-button link type="primary">查看更多 ></el-button>-->
									</div>
								</div>
							</template>
							<el-row :gutter="24"
							        v-for="(item,index) in toDoList"
							        :class="index % 2 === 0 ? 'to-do-list': 'to-do-list gray-bg-bar'"
							>
								<el-col :span="12">
									<span class="content">{{ item.title }}</span>
								</el-col>
								<el-col :span="4" class="user-info">
									<el-icon size="18px">
										<UserFilled/>
									</el-icon>
									<span class="username">{{ item.name }}</span>
								</el-col>
								<el-col :span="4" align="middle">
									<span class="status">{{ item.status }}</span>
								</el-col>
								<el-col :span="2">
									<el-button
										link
										type="primary"
										style="margin-left: 15px;"
									>
										详情
									</el-button>
								</el-col>
							</el-row>
						</el-card>
					</el-col>
					<el-col :span="12">
						<el-card class="custom-card">
							<template #header>
								<div class="flex flex-row flex-nowrap justify-between">
									<div>
										<img class="card-log" :src="getAssetsFile('index/secure.png')"
										     alt="风险模型介绍"/>
										<span class="card-title">风险模型介绍</span>
									</div>
									
									<div class="flex flex-row flex-nowrap">
										<div>
											<img class="add-logo" :src="getAssetsFile('index/add.png')" alt="添加"/>
											<el-button link type="primary">新增知识条目</el-button>
										</div>
										<div>
											<!--<el-button link type="primary">查看更多 ></el-button>-->
										</div>
									</div>
								</div>
							</template>
							<el-row :gutter="24">
								<el-col :span="12" v-for="item in riskCardList">
									<div class="risk-card" @click="viewDetail(item.modelType)">
										<div class="title-bar">
											<img class="risk-title-log" :src="getAssetsFile(item.url)"
											     :alt="item.title"/>
											<div class="risk-title">{{ item.title }}</div>
										</div>
										<div class="content">
											{{ item.content }}
										</div>
									</div>
								</el-col>
							</el-row>
						</el-card>
					</el-col>
				</el-row>

				<el-row :gutter="24" class="first-row-card">
					<el-col :span="12">
						<el-card class="custom-card sec-custom-card">
							<template #header>
								<div class="flex flex-row flex-nowrap justify-between">
									<div>
										<img class="card-log" :src="getAssetsFile('index/knowledge.png')"
										     alt="风险知识库"/>
										<span class="card-title">风险知识库</span>
									</div>
									
									<div class="flex flex-row flex-nowrap">
										<div>
											<img class="add-logo" :src="getAssetsFile('index/add.png')" alt="添加"/>
											<el-button link type="primary">新增知识条目</el-button>
										</div>
										<div>
											<!--<el-button link type="primary">查看更多 ></el-button>-->
										</div>
									</div>
								</div>
							</template>
							<el-row :gutter="24"
							        v-for="(item,index) in knowledgeList"
							        :class="index % 2 === 0 ? 'to-do-list': 'to-do-list gray-bg-bar'"
							>
								<el-col :span="16">
									<span class="content">{{ item.content }}</span>
								</el-col>
								<el-col :span="8" class="time-info">
									<el-icon size="18px">
										<Clock/>
									</el-icon>
									<span class="time">{{ item.time }}</span>
								</el-col>
							</el-row>
						</el-card>
					</el-col>
					<el-col :span="12">
						<el-card class="custom-card sec-custom-card">
							<template #header>
								<div class="flex flex-row flex-nowrap justify-between">
									<div>
										<img class="card-log" :src="getAssetsFile('index/setting.png')" alt="系统公告"/>
										<span class="card-title">系统公告</span>
									</div>
									
									<div class="flex flex-row flex-nowrap">
										<div>
											<img class="add-logo" :src="getAssetsFile('index/add.png')" alt="添加"/>
											<el-button link type="primary">发布公告</el-button>
										</div>
										<div>
											<!--<el-button link type="primary">查看更多 ></el-button>-->
										</div>
									</div>
								</div>
							</template>
							<el-row :gutter="24"
							        v-for="(item,index) in systemAnnouncement"
							        :class="index % 2 === 0 ? 'to-do-list': 'to-do-list gray-bg-bar'"
							>
								<el-col :span="16">
									<span class="content">{{ item.content }}</span>
								</el-col>
								<el-col :span="8" class="time-info">
									<el-icon size="18px">
										<Clock/>
									</el-icon>
									<span class="time">{{ item.time }}</span>
								</el-col>
							</el-row>
						</el-card>
					</el-col>
				</el-row>
			</el-main>
		</el-container>
	</div>
</template>

<script setup lang="ts">

import {getAssetsFile} from "@/utils/commonUtils";
import MonthlyIncreaseCard from "@/views/dashboard/components/MonthlyIncreaseCard.vue";
import {useRouter} from "vue-router";
import {queryHomePageIndex, queryToDoList} from "@/api/dashboard";
import {Indicator} from "@/api/dashboard/types";

const tableData = ref<Array<any>>([])
const router = useRouter();

const toDoList = ref<Array<any>>([])
const systemAnnouncement = ref<Array<any>>([
	{
		content: '【系统升级通知】计划于2025年5月15日进行系统更新',
		time: '2025-05-10 09:00',
	},
	{
		content: '【系统升级通知】计划于2025年5月15日进行系统更新',
		time: '2025-05-10 09:00',
	},
	{
		content: '【系统升级通知】计划于2025年5月15日进行系统更新',
		time: '2025-05-10 09:00',
	},
	{
		content: '【系统升级通知】计划于2025年5月15日进行系统更新',
		time: '2025-05-10 09:00',
	},
	{
		content: '【系统升级通知】计划于2025年5月15日进行系统更新',
		time: '2025-05-10 09:00',
	},
	{
		content: '【系统升级通知】计划于2025年5月15日进行系统更新',
		time: '2025-05-10 09:00',
	},
	{
		content: '【系统升级通知】计划于2025年5月15日进行系统更新',
		time: '2025-05-10 09:00',
	},
])
const knowledgeList = ref<Array<any>>([
	{
		content: '【风险模型】如何识别ICT-ABBA项目',
		time: '2025-05-10 09:00',
	},
	{
		content: '【风险模型】如何识别ICT-ABBA项目',
		time: '2025-05-10 09:00',
	},
	{
		content: '【风险模型】如何识别ICT-ABBA项目',
		time: '2025-05-10 09:00',
	},
	{
		content: '【风险模型】如何识别ICT-ABBA项目',
		time: '2025-05-10 09:00',
	},
	{
		content: '【风险模型】如何识别ICT-ABBA项目',
		time: '2025-05-10 09:00',
	},
	{
		content: '【风险模型】如何识别ICT-ABBA项目',
		time: '2025-05-10 09:00',
	},
	{
		content: '【风险模型】如何识别ICT-ABBA项目',
		time: '2025-05-10 09:00',
	},
])
const riskCardList = ref<Array<any>>([
	{
		url: 'index/ict_log.png',
		title: 'ICT-ABBA',
		content: '比对近 1 年新签约项目的上下游名称，若两项目存在 AB/BA 关系且金额差值达设定阈值，则触发对应等级预警。',
		modelType: 1,
	},
	{
		url: 'index/ict_log.png',
		title: 'ICT-上下游关联',
		content: '针对当月新签约下游供应商的 ICT 项目，调用数据查询上下游关联关系，符合条件时按规则触发各级预警。',
		modelType: 2,
	},
	{
		url: 'index/cloud.png',
		title: '移动云-收入异动',
		content: '监测移动云客户订购云硬盘却未购云主机等依赖产品的情况，按省 / 市 / 县订单量分三级触发预警。',
		modelType: 4,
	},
	{
		url: 'index/cloud.png',
		title: '移动云-组合不合理',
		content: '监测移动云客户订购云硬盘却未购云主机等依赖产品的情况，按省 / 市 / 县订单量分三级触发预警。',
		modelType: 3,
	},
])

const viewDetail = (modelType: number) => {
	router.push({
		path: '/model/detail',
		query: {
			modelType: modelType,
		}
	})
}

const indicatorData = ref<Indicator>({})
const bellList = ref<Array<any>>([
	{
		key: 'monthNewRiskRecordMoney',
		imgUrl: 'index/money.png',
		title: '月新增预警单涉及金额(元）',
		count: 0,
		bottomUrl: 'index/wave.png',
	},
	{
		key: 'allFirstLevelOverTimeRecordNumber',
		imgUrl: 'index/bell.png',
		title: '一级预警单数（个）',
		count: 0,
		bottomUrl: 'index/bar.png',
	},
	{
		key: 'allSecondLevelOverTimeRecordNumber',
		imgUrl: 'index/bell.png',
		title: '二级预警单数（个）',
		count: 0,
		bottomUrl: 'index/bar.png',
	},
	{
		key: 'allThirdLevelOverTimeRecordNumber',
		imgUrl: 'index/bell.png',
		title: '三级预警单数（个）',
		count: 0,
		bottomUrl: 'index/bar.png',
	},
])

const initIndexInfo = () => {
	queryHomePageIndex().then(response => {
		indicatorData.value = response.data
		bellList.value?.forEach(item => {
			let key = item.key
			item.count = response.data[key]
		})
	})
}
/**
 * dateTime:null
 * name:"小蕾"
 * recordId:"1111"
 * status:"待处理"
 * title:"ict一级风险预警单"
 */
const initToDo = () => {
	queryToDoList().then(response => {
		toDoList.value = response.data
	})
}

onMounted(() => {
	initToDo()
	initIndexInfo()
});

</script>

<style lang="scss" scoped>
.dashboard-container {
	display: flex;
	justify-content: center;
	
	.main-row {
		background: url("@/assets/index/index_row_bg.png") no-repeat center;
		background-size: 100% 100%;
		height: 220px;
		display: flex;
		flex-direction: row;
		flex-wrap: nowrap;
		align-items: center;
		padding: 30px;
	}

	.bell-card-list {
		display: flex;
		flex-direction: row;
		justify-content: space-evenly;

		--card-width: 24%;
		
		.bell-card {
			position: relative;
			width: var(--card-width);
			height: 149px;

			&:not(:nth-child(1))::before {
				position: absolute;
				top: 20px;
				left: -10%;
				content: "";
				height: 108px;
				border-left: 1px solid #0079FD;
				opacity: 0.20000000298023224;
			}


			.logo {
				width: 36px;
				height: 36px;
			}

			.title {
				margin-top: 10px;
				opacity: 1;
				color: #5A7597;
				font-family: PingFang SC;
				font-weight: 500;
				font-size: 16px;
				line-height: normal;
				letter-spacing: 0;
				text-align: left;
			}

			.cnt {
				opacity: 1;
				color: #475B75;
				font-family: PingFang SC;
				font-weight: 500;
				font-size: 39.16px;
				line-height: normal;
				letter-spacing: 0;
				text-align: left;
			}

			.bottom-pic {
				width: 90%;
				height: 31px;
			}

			.bottom-pic-bar {
				width: 90%;
			}
		}

	}

	.first-row-card {
		margin-top: 35px;

		.custom-card {
			width: 100%;
			height: 520px;
			border-radius: 16px;

			:deep(.el-card__body) {
				padding-top: 0;
			}

			.add-new-knowledge {
				display: flex;
				flex-direction: row;
				flex-wrap: nowrap;
				justify-content: center;
				align-items: center;

				.add-logo {
					height: 12px;
					width: 12px;
				}
			}

			.see-more {
				display: flex;
				align-items: center;
			}

			.card-log {
				height: 14px;
				width: 14px;
			}

			.card-title {
				opacity: 1;
				color: #3D3D3D;
				font-family: PingFang SC;
				font-weight: 500;
				font-size: 16px;
				line-height: normal;
				letter-spacing: 0;
				text-align: left;
				margin-left: 3px;
			}

			.risk-card {
				width: 100%;
				height: 208px;
				opacity: 1;
				border-radius: 11px;
				background: #EFF6FF;
				display: flex;
				flex-direction: column;
				padding: 10px;
				margin-top: 10px;
				cursor: pointer;

				.title-bar {
					display: flex;
					flex-direction: row;
					flex-wrap: nowrap;

					--title-bar-heigth: 55px;

					.risk-title-log {
						width: 62px;
						height: var(--title-bar-heigth);
					}

					.risk-title {
						opacity: 1;
						color: #2E6FC2;
						font-family: PingFang SC;
						font-weight: bold;
						font-size: 18px;
						letter-spacing: 0;
						text-align: left;
						line-height: var(--title-bar-heigth);
						margin-left: 10px;
					}
				}

				.content {
					opacity: 1;
					color: #666666;
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 14px;
					line-height: 28px;
					letter-spacing: 0;
					text-align: left;
					white-space: wrap;
					overflow: hidden; /* 超出部分隐藏 */
					text-overflow: ellipsis; /* 超出显示省略号（可选） */
				}
			}
		}
		
		.sec-custom-card {
			
			:deep(.el-card__body) {
				height: 100%;
				overflow-y: auto;
			}
		}

		.gray-bg-bar {
			opacity: 1;
			border-radius: 0;
			background: #548BCA19;
		}

		.to-do-list {
			height: 52px;

			:deep(.el-col) {
				line-height: 52px;
			}


			.content {
				opacity: 1;
				color: #3D3D3D;
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 14px;
				line-height: normal;
				letter-spacing: 0;
				text-align: left;
			}

			.user-info {
				display: flex;
				flex-direction: row;
				justify-content: center;
				align-items: center;

				.username {
					opacity: 1;
					color: #3D3D3D;
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 14px;
					line-height: normal;
					letter-spacing: 0;
					text-align: left;

				}
			}


			.status {
				color: #FF8432;
				font-size: 16px;
			}

			.time-info {
				display: flex;
				flex-direction: row;
				justify-content: center;
				align-items: center;

				.time {
					opacity: 1;
					color: #8C8C8C;
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 14px;
					line-height: normal;
					letter-spacing: 0;
					text-align: left;
				}
			}
		}

	}


}

</style>
