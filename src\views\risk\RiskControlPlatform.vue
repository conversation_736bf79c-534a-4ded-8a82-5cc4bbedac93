<template>
	<div class="risk-control-platform">
		<header>
			<el-button
				link
				@click="goBack"
				class="back-btn"
			>
				< 返回
			</el-button>
			<img
				class="companyLogo"
				:src="getAssetsFile('risk/companyLogo-white.png')"
				alt="加载中"
			/>
			<h1>政企业务风险防控信息化系统</h1>
			<el-select
				v-model="riskMonth"
				placeholder="请选择"
				class="risk-month"
				popper-class="transparent-select"
				:popper-append-to-body="false"
				@change="changeRiskMonth"
			>
				<el-option
					v-for="item in dateOptions"
					:key="item.value"
					:label="item.label"
					:value="item.value"
					:disabled="item.disabled"
				/>
			</el-select>
		</header>
		<section class="main-box">
			<!--第一列-->
			<div class="column">
				<div class="scale">
					<div class="model-name">
						<span class="name">风险影响规模</span>
					</div>
					<div class="model-content">
						<div class="model-one">
							<div class="single-bg">
								<span class="title">ICT-累计风险预警单 </span>
								<span class="count"> {{ riskScaleOfImpactModel.ictRecordNumber }}</span>
								<span class="unit">个</span>
							</div>
						
						</div>
						<div class="model-two">
							<div class="single-bg-down" style="width: 5rem;">
								<span class="sub-title">涉及项目数</span>
								<span class="count">{{ riskScaleOfImpactModel.ictProjectNumber }}个</span>
								<span class="sub-title" style="margin-left: 0.5rem;">涉及金额</span>
								<span class="count">{{ riskScaleOfImpactModel.ictProjectMoney }}万元</span>
							</div>
						</div>
						<div class="model-one">
							<div class="single-bg">
								<span class="title">移动云-累计风险预警单 </span>
								<span class="count">{{ riskScaleOfImpactModel.mcRecordNumber }}</span>
								<span class="unit">个</span>
							</div>
						</div>
						<div class="model-two">
							<div class="single-bg-down">
								<span class="sub-title">涉及客户数</span>
								<span class="count">{{ riskScaleOfImpactModel.mcCustomerNumber }}个</span>
								<span class="sub-title" style="margin-left: 0.5rem;">涉及订单数</span>
								<span class="count">{{ riskScaleOfImpactModel.mcProjectNumber }}个</span>
								<span class="sub-title" style="margin-left: 0.5rem;">涉及金额</span>
								<span class="count">{{ riskScaleOfImpactModel.mcProjectMoney }}万元</span>
							</div>
						</div>
					</div>
				</div>
				<div class="statistics">
					<div class="model-name" style="margin-top: 0;">
						<span class="name">风险场景统计</span>
					</div>
					<div class="pie-list">
						<div class="left">
							<div class="text-part">
								<span class="text">各场景风险预警单统计</span>
							</div>
							<div id="senseChart" class="sense-chart"></div>
						</div>
						<div class="right">
							<div class="text-part">
								<span class="text">各场景风险涉及金额统计</span>
							</div>
							<div id="amountChart" class="amount-chart"></div>
						</div>
					</div>
				</div>
				<div class="trends">
					<div class="model-name" style="margin-top: 0;">
						<span class="name">风险趋势</span>
					</div>
					<div class="sub">
						<span class="sub-title" style="margin-left: 0.4rem;">各场景风险涉及趋势</span>
						<div class="select-model">
							<el-select
								v-model="model"
								multiple
								collapse-tags
								collapse-tags-tooltip
								placeholder="全部模型"
								size="small"
								style="width: 1.8rem;"
								@change="modelChange"
							>
								<el-option
									v-for="item in modelOptions"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
							<el-select
								v-model="trend"
								placeholder="预警单趋势"
								size="small"
								style="width: 1.4rem;margin-left: 0.05rem;"
								@change="trendChange"
							>
								<el-option
									v-for="item in trendOptions"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
							<el-select
								v-if="trend === 2"
								v-model="target"
								multiple
								collapse-tags
								collapse-tags-tooltip
								placeholder="选择指标"
								size="small"
								style="width: 1.4rem;margin-left: 0.05rem;"
								@change="targetChange"
							
							>
								<el-option
									v-for="item in targetOptions"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
						</div>
					</div>
					<div id="riskTrendsChart" ref="trendsChartRef" class="chart"></div>
				</div>
			</div>
			
			<!--数据面板-->
			<div class="mid-list">
				<div v-for="(item,index) in midCardList" class="card">
					<div class="pic">
						<img class="pic-img" :src="getAssetsFile(item.img)" alt="图片"/>
					</div>
					<div class="content">
						<div class="title">
							{{ index === 0 ? monthString : '' }}{{ item.title }}
							<el-tooltip
								v-if="item.tip"
								class="right-end"
								effect="light"
								:content="item.tip"
								placement="bottom-start"
							>
								<el-button
									type="primary"
									class="question-filled"
									link
									icon="QuestionFilled"
								></el-button>
							</el-tooltip>
						</div>
						<div class="text">
							<span class="count" :style="{ color: item.color }">{{ item.count }}</span>
							<span class="unit">{{ item.unit }}</span>
						</div>
					</div>
				</div>
			</div>
			<!--第二列-->
			<div class="column">
				<div class="seat"></div>
				<div class="text-right">
					<el-button class="back-china-map" @click="goBackToChinaMap" v-show="!chinaMapVisible">
						返回到全国地图
					</el-button>
				</div>
				<div class="map-chart"
				     :style="chinaMapVisible ? { height: 'var(--map-china-chart-height)' }
				     : {height: 'var(--map-province-chart-height)'}"
				>
					<div
						id="chinaMap"
						ref="chinaMapChartRef"
						class="china-map"
						v-show="chinaMapVisible"
					></div>
					<div
						id="provinceMap"
						ref="provinceMapRef"
						class="province-map"
						v-show="!chinaMapVisible"
					></div>
				</div>
				<div class="china-map-btn-bar">
					<div :class="mapBtnFlag === 1 ? 'china-map-btn-active' : 'china-map-btn'" @click="summaryBtn">
						按风险预警单数统计
					</div>
					<div :class="mapBtnFlag === 2 ? 'china-map-btn-active': 'china-map-btn'"
					     style="margin-left: 0.1rem;" @click="modifyBtn">
						按整改情况统计
					</div>
				</div>
				<div class="ranking">
					<div class="model-name">
						<span class="name">{{ topTitleStr }}风险预警单排名</span>
					</div>
					<div id="rankChart" class="ranking-chart" ref="rankChartRef"></div>
				</div>
			</div>
			<!--第三列-->
			<div class="column">
				<div class="add-up">
					<div class="model-name" style="margin-left: 1rem;">
						<span class="name">风险预警单统计</span>
					</div>
					<div class="warning-form-list">
						<div v-for="item in warningFormList" class="form-card">
							<img :src="getAssetsFile(item.img)" alt="图片" class="pic-img"/>
							<div class="title">{{ item.title }}</div>
							<div class="third-row">
								<span class="count">{{ item.count }}</span>
								<span class="unit">{{ item.unit }}</span>
							</div>
						</div>
					</div>
					<div id="addUpChart" class="add-up-chart"></div>
				</div>
				<div class="correction">
					<div class="risk-btn-toolbar">
						<div class="model-name" style="margin-top: 0;margin-left: 0;width: 4.5rem;">
							<span class="name">风险预警单整改情况</span>
						</div>
						<div class="radio-btn">
							<div :class="timeoutFlag === 1? 'timely-active': 'timeout-bg'" @click="timeoutBtn">
								超时率
							</div>
							<div :class="timeoutFlag === 2? 'timely-active': 'timeout-bg'" style="margin-left: 0.1rem;"
							     @click="timelyBtn">
								及时率
							</div>
						</div>
					</div>
					<div class="correction-bar">
						<img
							:src="getAssetsFile('risk/platform/time_logo.png')"
							alt="图片"
							class="pic"
						/>
						
						<div class="correction-rate-bar">
							<div class="timeliness-rate-list">
								<div v-for="item in timelinessRateList" class="card">
									<div class="title">
										{{ item.title }}
										<el-tooltip
											v-if="item.tip"
											class="right-end"
											effect="light"
											:content="item.tip"
											placement="bottom-start"
										>
											<el-button
												type="primary"
												class="question-filled"
												link
												icon="QuestionFilled"
											></el-button>
										</el-tooltip>
									</div>
									<div class="rate">
										{{ item.rate }}
									</div>
								</div>
							</div>
						</div>
					</div>
					<el-table
						v-show="timeoutFlag === 1"
						v-loading="loading"
						:data="tableData"
						height="1.9rem"
						stripe
						class="table"
						max-height="0.34rem"
						:flexible="true"
						:row-class-name="tableRowClassName"
					>
						<el-table-column prop="unitName" label="单位名称" align="center"/>
						<el-table-column prop="onTimeNumber" label="超时预警单数" align="center">
							<template #default="scope">
								<span style="color: #DD593E;">{{ scope.row.onTimeNumber }}</span>
							</template>
						</el-table-column>
						<el-table-column prop="firstLevelOverTimeNumber" label="一级预警单数" align="center"/>
						<el-table-column prop="secondLevelOverTimeNumber" label="二级预警单数" align="center"/>
						<el-table-column prop="thirdLevelOverTimeNumber" label="三级预警单数" align="center"/>
					</el-table>
					<div v-show="timeoutFlag === 2" id="timelyChart" class="timely-chart"></div>
				</div>
				<div class="video-model">
					<div class="btn-bar">
						<div class="ai-btn" @click="aiAsk">
							AI问答
						</div>
						<div class="ai-btn" @click="aiReport">
							AI报告
						</div>
						<div class="ai-btn" @click="showRiskVideo">
							AI风险播报
						</div>
					</div>
					<!--<div class="video-bar">-->
					<!--	<video-->
					<!--		class="video"-->
					<!--		controls-->
					<!--		preload="metadata"-->
					<!--		:src="videoPath"-->
					<!--		type="video/mp4"-->
					<!--	>-->
					<!--	</video>-->
					<!--</div>-->
					<div class="video-bar">
						<risk-video
							ref="riskVideoRef"
							v-if="showVideo"
							:auto-generate="false"
							:custom-style="{width:'80%', maxWidth:'800px', margin: '0 auto',objectFit: 'contain'}"
							@video-loaded="handleVideoLoaded"
							@video-generated="handleVideoGenerated"
							@video-error="handleVideoError"
							@video-upload="handleVideoUpload"
							@video-paused="handleVideoPaused"
							@video-played="handleVideoPlay"
						/>
						<video
							v-else
							class="video"
							controls
							preload="metadata"
							:src="videoPath"
							type="video/mp4"
						>
						</video>
					</div>
					<div class="robot" @click="aiAsk"></div>
				</div>
			</div>
		</section>
		<warning-detail-dialog
			ref="warningDetailRef"
			:sum="warningDetailData.sum"
			:main-title="warningDetailData.title"
			:pending-rectification-count="warningDetailData.pendingRectificationCount"
			:cloud-count="warningDetailData.cloudCount"
			:ict-count="warningDetailData.ictCount"
			:completion-rate="warningDetailData.completionRate"
			:map-btn-flag="mapBtnFlag"
			:risk-month="riskMonth"
			:region-level="regionLevel"
			:province-name="movedProvinceName"
		></warning-detail-dialog>
		<top-province-dialog
			ref="topProvinceRef"
			:pending-rectification-count="topProvinceData.pendingRectificationCount"
			:cloud-count="topProvinceData.cloudCount"
			:sum="topProvinceData.sum"
			:ict-count="topProvinceData.ictCount"
			:main-title="topProvinceData.title"
			:risk-month="riskMonth"
			:province-name="topProvinceName"
		></top-province-dialog>
		<el-drawer
			v-model="drawer"
			:show-close="false"
			:modal="false"
			class="ai-drawer"
		>
			<template #header="{ close, titleId, titleClass }">
				<div class="flex flex-row justify-between items-center">
					<img
						:src="getAssetsFile('risk/platform/ai_dialog.png')"
						alt="对话"
						class="ai-dialog-log"
					/>
					<span
						:id="titleId"
						:class="titleClass"
						style="margin-left: 0.05rem;"
					>
								AI 问答
					</span>
					<el-button link @click="close">
						<img
							:src="getAssetsFile('risk/platform/close_btn.png')"
							alt="关闭"
							class="close-dialog-btn"
						/>
					</el-button>
				</div>
			</template>
			<el-container class="h-full">
				<el-main class="ai-ask-main">
					<el-row :gutter="24">
						<el-col :span="24">
							<div class="init-tag-bg">
								<div>
									<span class="ai-hello text-horizontal">您好，</span>
									<span
										class="ai-intro text-horizontal">我是您的智能风险防控助手。请问您需要了解哪些风险信息？</span>
								</div>
								<div class="flex flex-row justify-around">
									<el-tag
										v-for="item in questionArray"
										class="init-tag"
										@click="handleClickInitQuestion(item.questionDesc, item.answerDesc)"
									>
										{{ item.questionDesc }}
									</el-tag>
								</div>
							</div>
						</el-col>
					</el-row>
					
					<div v-for="item in aiMessageArray">
						<el-row
							v-if="item.type === 'user'"
							:gutter="24"
							class="flex flex-row flex-nowrap justify-end text-right"
							style="margin-top: 0.1rem;"
						>
							<el-col :span="18">
								<div class="user-msg-bg">
									<div class="user-msg">
										{{ item.content }}
									</div>
								</div>
							</el-col>
						</el-row>
						<el-row
							v-if="item.type === 'ai'"
							:gutter="24"
							class="flex flex-row flex-nowrap justify-start"
							style="margin-top: 0.1rem;"
						>
							<el-col :span="18">
								<div class="ai-msg-bg">
									<img
										v-if="item.loading"
										class="loading"
										:src="getAssetsFile('risk/load.gif')"
										alt="加载中"
									/>
									<div class="ai-msg" v-else>
										{{ item.content }}
									</div>
								</div>
							</el-col>
						</el-row>
					</div>
				</el-main>
				<el-footer>
					<el-form
						ref="sendFormRef"
						:model="sendMsgForm"
						:rules="sendMsgFormRules"
						@submit.native.prevent
					>
						<el-form-item prop="message">
							<div class="flex-y-center w-full">
								
								<el-input
									style="margin-left: 0.15rem"
									v-model="sendMsgForm.message"
									auto-complete="off"
									size="default"
									class="flex-1"
									placeholder="请输入想问的套餐"
									@keyup.enter.native="sendMessage"
								>
									<!--<template #prefix>
										<img
											:src="getAssetsFile('risk/platform/voice.png')"
											alt="语音"
											class="voice-btn"
										/>
										<el-divider direction="vertical"/>
									</template>-->
									<template #suffix>
										<el-image
											@click="sendMessage"
											:src="getAssetsFile('risk/platform/send.png')"
											class="rounded-tr-md rounded-br-md cursor-pointer"
										/>
									</template>
								</el-input>
							</div>
						</el-form-item>
					</el-form>
				
				</el-footer>
			</el-container>
		</el-drawer>
	</div>
</template>

<script setup lang="ts">

import * as echarts from "echarts";
import {getAssetsFile, stringToNumber, stringToNumberArray} from "@/utils/commonUtils";
import chinaMapJson from "@/assets/mapJson/china.json"
import {provinces} from "@/assets/mapJson/cityInfo"
import WarningDetailDialog from "@/views/risk/components/WarningDetailDialog.vue";
import {useRouter} from "vue-router";
import 'echarts-gl';
import TopProvinceDialog from "@/views/risk/components/TopProvinceDialog.vue";
import {queryMonthStr, queryRiskQuestion, queryRiskView} from "@/api/risk";
import {RiskQuestionQuery, RiskScaleOfImpactModel, RiskViewQuery, RiskViewVo} from "@/api/risk/types";
import {FormInstance} from "element-plus";
import moment from "moment";

//引入视频组件
import RiskVideo from "@/views/risk/components/RiskVideo.vue";
// 视频相关状态
const showVideo = ref(true);
const riskVideoRef = ref(null);
// 显示风险视频播报
const showRiskVideo = async () => {
	showVideo.value = true;
  // 使用单个nextTick等待
  await nextTick();
  if (riskVideoRef.value) {
    try {
      await riskVideoRef.value.initializePlayer();
      console.log('视频播放器初始化完成');
    } catch (error) {
      console.error('初始化视频播放器失败:', error);
    }
  } else {
    console.error('视频组件引用未找到');
  }
};
// 添加视频相关的事件处理方法
const handleVideoLoaded = (data: any) => {
	console.log('父组件收到视频加载完成事件:', data);
	// 可以在这里添加视频加载完成后的处理逻辑
	if (data.success) {
		// ElMessage.success('风险播报视频加载完成');
		console.log("风险播报视频加载完成")
	} else {
		// ElMessage.warning('视频已加载，但需要手动播放');
		console.log("视频已加载，但需要手动播放")
	}
};

const handleVideoGenerated = (data: any) => {
	console.log('父组件收到视频生成完成事件:', data);
	// 可以在这里添加视频生成完成后的处理逻辑
	if (data.success) {
		// ElMessage.success('风险播报视频生成完成');
		console.log("风险播报视频生成完成")
	}
};

const handleVideoError = (data: any) => {
	console.log('父组件收到视频处理错误事件:', data);
	// 可以在这里添加视频处理错误后的处理逻辑
	// ElMessage.error(`视频处理出错: ${data.message}`);
};

//监听上传视频
const handleVideoUpload = (data: any) => {
	console.log('父组件收到视频上传完成事件:', data);
	// 可以在这里添加视频处理错误后的处理逻辑
	// ElMessage.success(`视频上传完成: ${data.message}`);
};

//监听暂停视频
const handleVideoPaused = (data: any) => {
	console.log('父组件收到视频暂停事件:', data);
	// 可以在这里添加视频处理错误后的处理逻辑
	// ElMessage.success(`视频上传完成: ${data.message}`);
};

//监听播放视频
const handleVideoPlay = (data: any) => {
	console.log('父组件收到视频播放事件:', data);
	// 可以在这里添加视频处理错误后的处理逻辑
	// ElMessage.success(`视频上传完成: ${data.message}`);
};


echarts.registerMap('china', chinaMapJson)
const router = useRouter();

const videoPath = ref<string>('/eboss/ebosshome/ebrps/mp4/ebrps.mp4')
/**
 * 1:全国 2：省份 3：地市
 */
const regionLevel = ref<number>(2)
const tableRowClassName = ({
	                           row,
	                           rowIndex,
                           }: {
	row: any
	rowIndex: number
}) => {
	if (rowIndex % 2 === 0) {
		return 'table-bg'
	} else {
		return 'table-t-bg'
	}
}

const questionArray = ref<any>([])

const handleClickInitQuestion = (questionDesc: string, answerDesc: string) => {
	let aiMsgObject = {
		content: '',
		type: 'ai',
		loading: false, // 改为false以显示内容
	}
	aiMessageArray.value.push(
		{
			content: questionDesc,
			type: 'user',
		},
		aiMsgObject
	)
	// 预定义消息内容
	printText(aiMsgObject, answerDesc)
}

const printText = async (aiMsgObject: any, fullMessage: string) => {
	let charIndex = 0;
	// 使用 Promise 封装定时器逻辑
	await new Promise<void>((resolve) => {
		const timer = setInterval(() => {
			if (charIndex < fullMessage.length) {
				aiMsgObject.content += fullMessage[charIndex];
				charIndex++;
				// 使用深拷贝触发响应式更新
				aiMessageArray.value = [...aiMessageArray.value];
				scrollToBottom();
			} else {
				clearInterval(timer);
				resolve();
			}
		}, 20);
	});
}

const aiMessageArray = ref<any>([])
const sendFormRef = ref<FormInstance>()
const sendMsgForm = reactive<any>(
	{
		message: ''
	}
)

const sendMsgFormRules = computed(() => {
		return {
			message: [
				{
					required: true,
					trigger: "blur",
					message: '发送信息不能为空',
				},
			],
		}
	}
);

const baseUrl = import.meta.env.VITE_APP_API_URL ? import.meta.env.VITE_APP_API_URL : ''

const sendMessage = async () => {
	if (!sendMsgForm.message.trim()) return;
	
	aiMessageArray.value.push(
		{
			type: 'user',
			content: sendMsgForm.message
		}
	)
	// 添加初始AI响应对象
	let aiMessageObject = {
		type: 'ai',
		content: '',
		loading: true
	}
	aiMessageArray.value.push(aiMessageObject);
	
	let prompt = sendMsgForm.message
	
	// 清空输入框
	sendMsgForm.message = '';
	sendFormRef.value?.clearValidate()
	
	let aiResponseIndex
	try {
		const accessToken = localStorage.getItem("accessToken");
		const response = await fetch(baseUrl + `emall-api/api/v1/ebrps/ai/stream2?prompt=${encodeURIComponent(prompt)}`, {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				// 如果需要认证，添加认证头
				'Authorization': accessToken
			},
		});
		
		const reader = response.body?.getReader();
		if (!reader) return;
		
		const decoder = new TextDecoder();
		aiResponseIndex = aiMessageArray.value.length - 1;
		aiMessageArray.value[aiResponseIndex].loading = false;
		while (true) {
			const {done, value} = await reader.read();
			if (done) break;
			// 解码并追加内容
			const chunk = decoder.decode(value);
			await printText(aiMessageObject, chunk)
			//aiMessageArray.value[aiResponseIndex].content += chunk;
			// 触发视图更新并滚动到底部
			await nextTick();
			scrollToBottom();
		}
	} catch (error) {
		console.error('请求失败:', error);
		aiMessageArray.value[aiResponseIndex].content = '请求失败，请重试';
		aiMessageArray.value.push(
			{
				type: 'ai',
				content: '请求失败，请重试',
				loading: false,
			}
		)
	} finally {
		aiMessageArray.value[aiResponseIndex].loading = false;
		scrollToBottom();
	}
}

// 滚动到底部方法
const scrollToBottom = () => {
	const container = document.querySelector('.el-main');
	if (container) {
		container.scrollTop = container.scrollHeight;
	}
};

const initAiQuestion = () => {
	let params: RiskQuestionQuery = {
		monthStr: riskMonth.value,
	}
	queryRiskQuestion(params).then(response => {
		questionArray.value = response.data
	})
}

const riskMonth = ref<string>('202506')
// 当月字符
const monthString = ref<string>('6月')
const changeRiskMonth = (value: string) => {
	if (value) {
		let currentMonth = moment(value).get('month') + 1;
		monthString.value = currentMonth + '月'
		initRiskView()
	}
}

/**
 * 1：全国
 * 2：省份
 * 3：城市
 */
const level = ref<string>('1')

/**
 * 下钻省份名称
 */
const selectedProvinceName = ref<string>('')

/**
 * top点击
 */
const movedProvinceName = ref<string>('')
const topProvinceName = ref<string>('')

const dateOptions = ref<Array<any>>([])
const initDateOptions = () => {
	queryMonthStr().then(response => {
		let monthArray = response.data;
		if (monthArray && monthArray.length > 0) {
			riskMonth.value = monthArray[0];
			monthArray.forEach((item: string) => {
				dateOptions.value.push({
					label: item,
					value: item,
				})
			})
		}
		
	})
}
/**
 * 风险影响规模
 */
const riskScaleOfImpactModel = ref<RiskScaleOfImpactModel>({})
const riskViewData = reactive<RiskViewVo>({})
const topRiskModel = ref<any>({})
const initRiskView = () => {
	let riskViewQuery: RiskViewQuery = {
		level: level.value,
		monthStr: riskMonth.value,
		regionCode: '',
		regionName: selectedProvinceName.value,
	}
	queryRiskView(riskViewQuery).then(async (response: any) => {
		let data = response.data
		if (data) {
			await nextTick()
			midCardList.value?.forEach(item => {
				item.count = data[item.key]
			})
			riskScaleOfImpactModel.value = data.riskScaleOfImpactModel
			let riskScenarioStatisticsModel: any = data.riskScenarioStatisticsModel
			if (riskScenarioStatisticsModel) {
				senseOptionData.value?.forEach(item => {
					item.value = stringToNumber(riskScenarioStatisticsModel[item.key])
				})
				amountOptionData.value?.forEach(item => {
					item.value = stringToNumber(riskScenarioStatisticsModel[item.key])
				})
				initPlaneCharts('senseChart', senseOptionData.value, senseOption.value)
				initPlaneCharts('amountChart', amountOptionData.value, amountOption.value)
			}
			let reportTrendModel: any = data.reportTrendModel;
			if (reportTrendModel) {
				trendsOptions.value.xAxis[0].data = reportTrendModel.monthArray;
				trendsSeries.value?.forEach((item: any) => {
					item.data = reportTrendModel[item.key]
				})
				amountSeries.value.data = stringToNumberArray(reportTrendModel.projectMoney)
				originalYAxis.value[0].min = stringToNumber(reportTrendModel.leftMinRecordNumber)
				originalYAxis.value[0].max = stringToNumber(reportTrendModel.leftMaxRecordNumber)
				originalYAxis.value[0].interval = stringToNumber(reportTrendModel.leftIntervalRecordNumber)
				amountObj.value.min = stringToNumber(reportTrendModel.rightMinProjectNumber)
				amountObj.value.max = stringToNumber(reportTrendModel.rightMaxProjectNumber)
				amountObj.value.interval = stringToNumber(reportTrendModel.rightIntervalProjectNumber)
				itemObj.value.min = stringToNumber(reportTrendModel.leftMinProjectNumber)
				itemObj.value.max = stringToNumber(reportTrendModel.leftMaxProjectNumber)
				itemObj.value.interval = stringToNumber(reportTrendModel.leftIntervalProjectNumber)
				productPeopleSeries.value.forEach((item: any) => {
					item.data = stringToNumberArray(reportTrendModel[item.key])
				})
				initTrendsChart(trendsOptions.value)
			}
			
			let regionStatisticsList = data.regionStatisticsList;
			if (regionStatisticsList && regionStatisticsList.length > 0) {
				regionStatisticsList.forEach((item: any) => {
					item.name = item.regionName
					if (mapBtnFlag.value === 1) {
						item.value = stringToNumber(item.regionMonthRecordNumber)
					} else if (mapBtnFlag.value === 2) {
						item.value = stringToNumber(item.regionMonthRcfRecordNumber)
					}
				})
				switch (level.value) {
					case '1':
						chinaMapOptions.value.series[0].data = regionStatisticsList
						chinaMapOptions.value.visualMap.pieces = data.pieces
						if (chinaMapChartRef.value) {
							initChinaMapChart(chinaMapOptions.value)
							chinaMapChart.value.on('click', handleChinaMapClick);
							chinaMapChart.value.on('mouseover', handleChinaMapMouseover);
							chinaMapChart.value.on('dblclick', handleChinaMapDbClick);
						}
						break
					case '2':
						let option = provinceMapChart.value.getOption();
						option.series[0].data = regionStatisticsList
						provinceMapChart.value.setOption(option, true);
						break
				}
			}
			topRiskModel.value = data.topRiskModel;
			if (topRiskModel.value) {
				rankOptions.value.yAxis.data = topRiskModel.value.provinceName
				rankOptions.value.series[0].data = stringToNumberArray(topRiskModel.value.firstLevelRecordNumberArray)
				rankOptions.value.series[1].data = stringToNumberArray(topRiskModel.value.secondLevelRecordNumberArray)
				rankOptions.value.series[2].data = stringToNumberArray(topRiskModel.value.thirdLevelRecordNumberArray)
				topTitleStr.value = topRiskModel.value.topTitleStr
				if (rankChartRef.value) {
					initRankChart(rankOptions.value)
					rankChart.value.on('click', handleRankChartClick);
				}
			}
			
			let riskWarningRecordStatisticsModel = data.riskWarningRecordStatisticsModel;
			if (riskWarningRecordStatisticsModel) {
				warningFormList.value.forEach(item => {
					item.count = riskWarningRecordStatisticsModel[item.key]
					let unitKey = warningFormUnitMap.get(item.key);
					item.unit = '环比' + riskWarningRecordStatisticsModel[unitKey] + '个'
				})
				
				addUpOptions.value.xAxis[0].data = riskWarningRecordStatisticsModel.monthArray
				addUpOptions.value.yAxis[0].min = stringToNumber(riskWarningRecordStatisticsModel.leftMin)
				addUpOptions.value.yAxis[0].max = stringToNumber(riskWarningRecordStatisticsModel.leftMax)
				addUpOptions.value.yAxis[0].interval = stringToNumber(riskWarningRecordStatisticsModel.leftInterval)
				addUpOptions.value.yAxis[1].min = stringToNumber(riskWarningRecordStatisticsModel.rightMin)
				addUpOptions.value.yAxis[1].max = stringToNumber(riskWarningRecordStatisticsModel.rightMax)
				addUpOptions.value.yAxis[1].interval = stringToNumber(riskWarningRecordStatisticsModel.rightInterval)
				addUpOptions.value.series[0].data = stringToNumberArray(riskWarningRecordStatisticsModel.firstLevelRecordNumberArray)
				addUpOptions.value.series[1].data = stringToNumberArray(riskWarningRecordStatisticsModel.secondLevelRecordNumberArray)
				addUpOptions.value.series[2].data = stringToNumberArray(riskWarningRecordStatisticsModel.thirdLevelRecordNumberArray)
				addUpOptions.value.series[3].data = stringToNumberArray(riskWarningRecordStatisticsModel.allMoMRecordNumberArray)
				
				initAddUpChart(addUpOptions.value)
			}
			
			let statusOfCorrectiveForRiskWarningRecordModel = data.statusOfCorrectiveForRiskWarningRecordModel;
			if (statusOfCorrectiveForRiskWarningRecordModel) {
				onTimeRateModel.value = statusOfCorrectiveForRiskWarningRecordModel.onTimeRateModel
				overTimeRateModel.value = statusOfCorrectiveForRiskWarningRecordModel.overTimeRateModel
				timelyOptions.value.series[0].data = onTimeRateModel.value.onTimeRateArray;
				timelyOptions.value.xAxis.data = onTimeRateModel.value.provinceName
				initTimelyChart(timelyOptions.value)
				
				timelinessRateList.value = [
					{
						title: '整改及时率',
						rate: onTimeRateModel.value.allOnTimeRate,
						tip: '整改及时率=当月到期按时处理工单数/当月到期应处理工单数x100%',
					},
					{
						title: '一级整改及时率',
						rate: onTimeRateModel.value.firstLevelOnTimeRate,
					},
					{
						title: '二级整改及时率',
						rate: onTimeRateModel.value.secondLevelOnTimeRate,
					},
					{
						title: '三级整改及时率',
						rate: onTimeRateModel.value.thirdLevelOnTimeRate,
					},
				]
				tableData.value = overTimeRateModel.value.regionOverTimeRateObjectList
			}
		}
	})
}

// 返回首页
const goBack = () => {
	router.push({
		path: '/dashboard',
	});
}

const tableData = ref([]);
const loading = ref(false)
const warningDetailRef = ref()
const warningDetailData = reactive({
	title: '',
	sum: '0',
	ictCount: 0,
	cloudCount: 0,
	pendingRectificationCount: 0,
	completionRate: 0,
})

const topProvinceRef = ref()
const topProvinceData = reactive({
	title: '',
	sum: '0',
	ictCount: 0,
	cloudCount: 0,
	pendingRectificationCount: 0,
})

const trendsChart = ref<any>("");
const trendsChartRef = ref<HTMLElement>();
const trendsSeries = ref<any>(
	[
		{
			key: 'ictABRecordNumber',
			name: '上下游存在AB/BA关系',
			type: 'bar',
			tooltip: {
				valueFormatter: function (value: any) {
					return value as number + ' 个';
				}
			},
			data: [
				180, 180, 180, 180, 180, 180,
			],
			color: '#415DF4',
			barGap: 0,
		},
		{
			key: 'ictUpDownRecordNumber',
			name: '上下游存在关联关系',
			type: 'bar',
			tooltip: {
				valueFormatter: function (value: any) {
					return value as number + ' 个';
				}
			},
			data: [
				145, 145, 145, 145, 145, 145,
			],
			color: '#2CBE9C',
			barGap: 0,
		},
		{
			key: 'mcOrderRecordNumber',
			name: '产品订购组合不合理',
			type: 'bar',
			tooltip: {
				valueFormatter: function (value: any) {
					return value as number + ' 个';
				}
			},
			data: [
				105, 105, 105, 105, 105, 105,
			],
			color: '#5EDCFF',
			barGap: 0,
		},
		{
			key: 'mcIncomeRecordNumber',
			name: '产品收入异动',
			type: 'bar',
			tooltip: {
				valueFormatter: function (value: any) {
					return value as number + ' 个';
				}
			},
			data: [
				80, 80, 80, 80, 80, 80,
			],
			color: '#F08A50',
			barGap: 0,
		},
	]
);

// 原始 yAxis
const originalYAxis = ref<Array<any>>([
	{
		type: 'value',
		name: '预警单/个',
		min: 0,
		max: 200,
		interval: 50,
		axisLabel: {
			formatter: '{value}',
			color: '#FFFFFF',
			fontFamily: 'PingFang SC',
		},
		nameTextStyle: {
			color: '#FFFFFF',
		},
		splitLine: {
			show: false
		},
		axisTick: {
			show: false
		},
		axisLine: {
			show: false
		},
	}
])

// 风险业务趋势Y轴 1
const itemObj = ref<any>({
	type: 'value',
	name: '项目/客户数（个）',
	min: 0,
	max: 20,
	interval: 5,
	axisLabel: {
		formatter: '{value}',
		color: '#FFFFFF',
		fontFamily: 'PingFang SC',
	},
	nameTextStyle: {
		color: '#FFFFFF',
	},
	splitLine: {
		show: false
	},
	axisTick: {
		show: false
	},
	axisLine: {
		show: false
	},
})

// 风险业务趋势Y轴 2
const amountObj = ref<any>({
	type: 'value',
	name: '金额（万元）',
	min: 0,
	max: 20,
	interval: 5,
	axisLabel: {
		formatter: '{value}',
		color: '#FFFFFF',
		fontFamily: 'PingFang SC',
	},
	nameTextStyle: {
		color: '#FFFFFF',
	},
	splitLine: {
		show: false
	},
	axisTick: {
		show: false
	},
	axisLine: {
		show: false
	},
})

// 新增 trend=2 时的 yAxis
const businessValueYAxis = [
	itemObj.value,
	amountObj.value
];

const amountSeries = ref<any>({
	name: '项目金额',
	type: 'line',
	smooth: true,
	yAxisIndex: 1,
	tooltip: {
		valueFormatter: function (value: any) {
			return value as number + ' 万元';
		}
	},
	data: [18, 2.2, 3.3, 4.5, 6.3, 10.2, 20.3, 23.4, 23.0, 16.5, 12.0],
	color: '#F08A50',
	barGap: 0,
})

const productPeopleSeries = ref<any>([
	{
		key: 'ictABProjectNumber',
		name: '上下游存在AB/BA关系',
		type: 'bar',
		tooltip: {
			valueFormatter: function (value: any) {
				return value as number + ' 个';
			}
		},
		data: [
			18, 18, 18, 18, 18, 18, 18, 18, 18, 18, 18
		],
		color: '#415DF4',
		barGap: 0,
	},
	{
		key: 'ictUpDownProjectNumber',
		name: '上下游存在关联关系',
		type: 'bar',
		tooltip: {
			valueFormatter: function (value: any) {
				return value as number + ' 个';
			}
		},
		data: [
			16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16
		],
		color: '#2CBE9C',
		barGap: 0,
	},
	{
		key: 'mcOrderABProjectNumber',
		name: '产品订购组合不合理',
		type: 'bar',
		tooltip: {
			valueFormatter: function (value: any) {
				return value as number + ' 个';
			}
		},
		data: [
			14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14
		],
		color: '#5EDCFF',
		barGap: 0,
	},
	{
		key: 'mcIncomeProjectNumber',
		name: '产品收入异动',
		type: 'bar',
		tooltip: {
			valueFormatter: function (value: any) {
				return value as number + ' 个';
			}
		},
		data: [
			12, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12
		],
		color: '#F08A50',
		barGap: 0,
	},
])

// 新增的 line 系列
const productIncomeSeries = ref<any>([
	...productPeopleSeries.value,
	amountSeries.value,
]);

const trend = ref<number>(1)
const trendOptions = ref<Array<any>>([
	{
		label: '预警单趋势',
		value: 1,
	},
	{
		label: '风险业务值趋势',
		value: 2,
	},
])

const target = ref<Array<number>>([1, 2])
const targetOptions = ref<Array<any>>([
	{
		label: '项目/客户数',
		value: 1,
	},
	{
		label: '金额',
		value: 2,
	},
])

const trendsOptions = ref<any>(
	{
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'cross',
				crossStyle: {
					color: '#999'
				}
			}
		},
		zoom: 0.9,
		legend: {
			data: ['上下游存在AB/BA关系', '上下游存在关联关系', '产品订购组合不合理', '产品收入异动'],
			itemWidth: 13,       // 图例标记的图形宽度
			itemHeight: 10,      // 图例标记的图形高度
			itemGap: 5,         // 图例项之间的间隔
			// 其他常用配置
			type: 'plain',       // 普通图例模式（非滚动）
			orient: 'horizontal', // 水平排列（可选 vertical 垂直）
			top: -5,
			//right: 25,
			left: 20,
			bottom: 20,
			textStyle: {
				color: '#FFFFFF',
				fontSize: 12,       // 字体大小调整
				fontFamily: 'PingFang SC',
			},
		},
		xAxis: [
			{
				type: 'category',
				data: [],
				axisPointer: {
					type: 'shadow'
				},
				splitLine: {
					show: false       // 关闭网格线（可选）
				},
				axisTick: {
					show: false       // 关闭刻度线
				},
				axisLine: {
					show: false        // 保持轴线显示（若需隐藏轴线此处改为 false）
				},
				axisLabel: {
					formatter: '{value}',
					color: '#FFFFFF',
					fontFamily: 'PingFang SC',
				},
			}
		],
		yAxis: [
			{
				type: 'value',
				name: '预警单/个',
				min: 0,
				max: 200,
				interval: 50,
				axisLabel: {
					formatter: '{value}',
					color: '#FFFFFF',
					fontFamily: 'PingFang SC',
				},
				nameTextStyle: {
					color: '#FFFFFF',           // 单位颜色
					//fontSize: 12,
					//padding: [0, 0, 0, 20]  // 可选：调整单位位置
				},
				splitLine: {
					show: false         // 隐藏Y轴分割线（背景网格线）
				},
				axisTick: {
					show: false       // 关闭刻度线
				},
				axisLine: {
					show: false        // 保持轴线显示（若需隐藏轴线此处改为 false）
				},
			}
		],
		series: trendsSeries.value,
	}
)

const initTrendsChart = (options: any) => {
	trendsChart.value = markRaw(
		echarts.init(document.getElementById('riskTrendsChart') as HTMLDivElement)
	);
	
	trendsChart.value.setOption(options);
	// 大小自适应
	window.addEventListener("resize", () => {
		trendsChart.value.resize();
	});
}

const trendChange = async (value: number) => {
	await nextTick();
	if (value === 1) {
		trendsOptions.value.yAxis = originalYAxis.value;
		trendsOptions.value.series = trendsSeries.value;
		trendsChart.value.setOption(trendsOptions.value, true);
		return;
	} else {
		target.value = [1, 2]
		await targetChange(target.value)
	}
}

const targetChange = async (value: Array<number>) => {
	await nextTick();
	if (!value || value.length === 0) {
		trendsOptions.value.yAxis = businessValueYAxis; // 显示两个 y 轴
		trendsOptions.value.series = productIncomeSeries.value;
	} else {
		if (value.includes(1) && value.includes(2)) {
			trendsOptions.value.yAxis = businessValueYAxis; // 显示两个 y 轴
			trendsOptions.value.series = adjustSeriesYAxis(productIncomeSeries.value, 2);
		} else if (value.includes(1)) {
			trendsOptions.value.yAxis = [itemObj.value]; // 只显示项目/客户数
			trendsOptions.value.series = adjustSeriesYAxis(productPeopleSeries.value, 1);
			
		} else if (value.includes(2)) {
			trendsOptions.value.yAxis = amountObj.value; // 只显示金额
			trendsOptions.value.series = adjustSeriesYAxis([amountSeries.value], 1);
		}
	}
	trendsChart.value.setOption(trendsOptions.value, true);
}

const adjustSeriesYAxis = (seriesList: any[], axisCount: number) => {
	return seriesList.map(s => {
		if (s.yAxisIndex !== undefined && s.yAxisIndex >= axisCount) {
			s.yAxisIndex = 0;
		}
		return s;
	});
}

const rankChart = ref<any>("");
const rankChartRef = ref<HTMLElement>();
const topTitleStr = ref<string>('')
const rankBarWidth = ref<string>('40%')
// 风险预警单整改情况柱状图粗细
const timelyBarWidth = ref<string>('20%')
const rankOptions = ref<any>(
	{
		/*tooltip: {
			trigger: 'axis',
			axisPointer: {
				// Use axis to trigger tooltip
				type: 'shadow' // 'shadow' as default; can also be 'line' or 'shadow'
			}
		},*/
		legend: {
			itemWidth: 13,       // 图例标记的图形宽度
			itemHeight: 10,      // 图例标记的图形高度
			itemGap: 5,         // 图例项之间的间隔
			type: 'plain',       // 普通图例模式（非滚动）
			orient: 'horizontal', // 水平排列（可选 vertical 垂直）
			textStyle: {
				color: '#FFFFFF',
				fontSize: 12,       // 字体大小调整
				fontFamily: 'PingFang SC',
			},
		},
		grid: {
			left: '3%',
			right: '4%',
			bottom: '3%',
			top: '10%',
			containLabel: true
		},
		xAxis: {
			type: 'value',
			splitLine: {
				show: false       // 关闭网格线（可选）
			},
			axisLabel: {
				color: '#FFFFFF',
				fontFamily: 'PingFang SC',
			}
		},
		yAxis: {
			type: 'category',
			data: ['广东', '浙江', '江苏', '江西', '河南'],
			axisTick: {
				show: false       // 关闭刻度线
			},
			axisLine: {
				show: false        // 保持轴线显示（若需隐藏轴线此处改为 false）
			},
			axisLabel: {
				color: '#FFFFFF',
				fontFamily: 'PingFang SC',
			}
		},
		series: [
			{
				name: '一级预警单数',
				type: 'bar',
				stack: 'total',
				color: '#3263FE',
				label: {
					show: false
				},
				data: [105, 150, 110, 120, 90],
				barWidth: rankBarWidth.value
			},
			{
				name: '二级预警单数',
				type: 'bar',
				stack: 'total',
				color: '#84A1FD',
				label: {
					show: false
				},
				data: [120, 80, 100, 60, 100],
				barWidth: rankBarWidth.value
			},
			{
				name: '三级预警单数',
				type: 'bar',
				stack: 'total',
				color: '#C2D0FF',
				label: {
					show: false
				},
				data: [20, 30, 25, 15, 20],
				barWidth: rankBarWidth.value
			}
		]
	}
);
const initRankChart = (options: any) => {
	rankChart.value = markRaw(
		echarts.init(document.getElementById('rankChart') as HTMLDivElement)
	);
	
	rankChart.value.setOption(options);
	// 大小自适应
	window.addEventListener("resize", () => {
		rankChart.value.resize();
	});
}

const handleRankChartClick = async (params: echarts.ECElementEvent) => {
	let seriesName = params.seriesName;
	if (seriesName) {
		let name = params.name;
		topProvinceName.value = name
		let map = topRiskModel.value.map;
		let topRiskObject = map[name];
		let element = topRiskModel.value[topRiskObject];
		if (element) {
			let seriesIndex = params.seriesIndex;
			topProvinceData.title = params.seriesName + '：'
			switch (seriesIndex) {
				case 0:
					topProvinceData.sum = element.firstLevelRecordNumber
					topProvinceData.ictCount = element.firstLevelIctRecordNumber
					topProvinceData.cloudCount = element.firstLevelMcRecordNumber
					topProvinceData.pendingRectificationCount = element.firstLevelPdRcfRecordNumber
					break
				case 1:
					topProvinceData.sum = element.secondLevelRecordNumber
					topProvinceData.ictCount = element.secondLevelIctRecordNumber
					topProvinceData.cloudCount = element.secondLevelMcRecordNumber
					topProvinceData.pendingRectificationCount = element.secondLevelPdRcfRecordNumber
					break
				case 2:
					topProvinceData.sum = element.thirdLevelRecordNumber
					topProvinceData.ictCount = element.thirdLevelIctRecordNumber
					topProvinceData.cloudCount = element.thirdLevelMcRecordNumber
					topProvinceData.pendingRectificationCount = element.thirdLevelPdRcfRecordNumber
					break
			}
			topProvinceRef.value?.init('view')
		}
	}
}

const addUpChart = ref<any>("");
const addUpOptions = ref<any>({
	tooltip: {
		trigger: 'axis',
		axisPointer: {
			type: 'cross',
			crossStyle: {
				color: '#999'
			}
		}
	},
	grid: {
		left: '10%',
		right: '10%',
		bottom: '20%'
	},
	legend: {
		data: ['一级', '二级', '三级'],
		itemWidth: 13,       // 图例标记的图形宽度
		itemHeight: 10,      // 图例标记的图形高度
		itemGap: 5,         // 图例项之间的间隔
		// 其他常用配置
		type: 'plain',       // 普通图例模式（非滚动）
		orient: 'horizontal', // 水平排列（可选 vertical 垂直）
		top: 5,
		textStyle: {
			color: '#FFFFFF',
			fontSize: 12,       // 字体大小调整
			fontFamily: 'PingFang SC',
		},
	},
	xAxis: [
		{
			type: 'category',
			data: ['1月', '2月', '3月', '4月', '5月', '6月'],
			axisPointer: {
				type: 'shadow'
			},
			axisLabel: {
				formatter: '{value}',
				color: '#FFFFFF',
				fontFamily: 'PingFang SC',
			},
			splitLine: {
				show: false       // 关闭网格线（可选）
			},
			axisTick: {
				show: false       // 关闭刻度线
			},
			axisLine: {
				show: false        // 保持轴线显示（若需隐藏轴线此处改为 false）
			},
		}
	],
	yAxis: [
		{
			type: 'value',
			name: '预警单/个',
			min: 0,
			max: 250,
			interval: 50,
			axisLabel: {
				formatter: '{value}',
				color: '#FFFFFF',
				fontFamily: 'PingFang SC',
			},
			nameTextStyle: {
				color: '#FFFFFF',
			},
			splitLine: {
				show: false       // 关闭网格线（可选）
			},
			axisTick: {
				show: false       // 关闭刻度线
			},
			axisLine: {
				show: false        // 保持轴线显示（若需隐藏轴线此处改为 false）
			},
		},
		{
			type: 'value',
			name: '环比预警单数（个）',
			min: 0,
			max: 250,
			interval: 50,
			axisLabel: {
				formatter: '{value}',
				color: '#FFFFFF',
				fontFamily: 'PingFang SC',
			},
			nameTextStyle: {
				color: '#FFFFFF',
			},
			splitLine: {
				show: false         // 隐藏Y轴分割线（背景网格线）
			}
		}
	],
	series: [
		{
			name: '一级',
			type: 'bar',
			tooltip: {
				valueFormatter: function (value: any) {
					return value as number;
				}
			},
			data: [
				200, 140, 150, 70, 170, 200
			],
			color: '#415DF4',
			barGap: 0,
		},
		{
			name: '二级',
			type: 'bar',
			tooltip: {
				valueFormatter: function (value: any) {
					return value as number;
				}
			},
			data: [
				110, 90, 140, 105, 175, 160
			],
			color: '#84A1FD',
			barGap: 0,
		},
		{
			name: '三级',
			type: 'bar',
			tooltip: {
				valueFormatter: function (value: any) {
					return value as number;
				}
			},
			data: [
				70, 160, 165, 175, 60, 140
			],
			color: '#C2D0FF',
			barGap: 0,
		},
		{
			name: '环比预警单数（个）',
			type: 'line',
			yAxisIndex: 1,
			smooth: true,  // ← 关键配置：启用贝塞尔曲线平滑
			tooltip: {
				valueFormatter: function (value: any) {
					return value as number;
				}
			},
			data: [200, 140, 150, 190, 210, 165],
			color: '#1781FA',
			barGap: 0,
		}
	]
});

const initAddUpChart = (options: any) => {
	addUpChart.value = markRaw(
		echarts.init(document.getElementById('addUpChart') as HTMLDivElement)
	);
	
	addUpChart.value.setOption(options);
	// 大小自适应
	window.addEventListener("resize", () => {
		addUpChart.value.resize();
	});
}

const goBackToChinaMap = () => {
	level.value = '1'
	selectedProvinceName.value = ''
	chinaMapVisible.value = true
	initRiskView()
}

const chinaMapVisible = ref<boolean>(true);
// 中国地图
const chinaMapChartRef = ref<HTMLElement>();
const chinaMapChart = ref<any>("");

const chinaMapOptions = ref<any>(
	{
		/*tooltip: {
			show: true,
			formatter: function (params: any) {
				return '&nbsp;&nbsp;' + params.name + '&nbsp;&nbsp;&nbsp;' + params.value + '人&nbsp;&nbsp;';
			}
		},*/
		//   中国地图颜色调整
		visualMap: {
			type: 'piecewise',
			left: '15',
			bottom: '15',
			itemWidth: 14,
			itemHeight: 14,
			textStyle: {
				color: '#FFFFFF',
				fontSize: 12,
			},
			pieces: [
				{
					max: 100,
					min: 51,
					label: '51-100',
				},
				{
					max: 50,
					min: 21,
					label: '21-50',
				},
				{
					max: 20,
					min: 1,
					label: '1-20',
				},
				{
					value: 0,
					label: '0',
				},
			],
			inRange: {
				color: ['rgb(218,233,255)', '#8AB3F0', '#649BEB', '#3976CF']
			},
			outOfRange: {
				color: ['#052be8']
			}
		},
		geo: {
			map: 'china',
			show: true,
			roam: false,
			aspectScale: 0.76,
			zoom: 1.2,
			label: {
				emphasis: {
					show: false
				}
			},
			itemStyle: {
				normal: {
					borderColor: 'rgba(0,63,140,0.2)',
					shadowColor: 'rgb(0,0,0,0)',
					shadowOffsetY: 0,
					shadowOffsetX: 0,
					shadowBlur: 0,
					areaColor: 'rgba(0,0,0,0)',
				}
			}
		},
		series: [
			{
				type: 'map',
				map: 'china',
				aspectScale: 0.76,
				center: [103.82, 36.56], // 初始中心点坐标（中国居中）
				layoutCenter: ['49%', '46.6%'],
				layoutSize: '100%',
				zoom: 1.2,
				label: {
					normal: {
						show: false,
						color: '#FFFFFF', // 白色字体
					},
					emphasis: {
						show: false,
						color: '#FFFFFF', // 白色字体
					}
				},
				itemStyle: {
					normal: {
						areaColor: 'rgb(218,233,255)',
						borderColor: 'rgba(147,147,147)',
						borderWidth: 1,
						shadowColor: '#5daee2',
						//shadowOffsetY: 2,
						//shadowOffsetX: 2,
						//shadowBlur: 10
					},
					emphasis: {
						areaColor: '#FFAE00',
						label: {
							show: true,
							color: '#FFFFFF'
						}
					}
				},
				data: []
			}
		]
	}
)

const initChinaMapChart = (options: any) => {
	chinaMapChart.value = markRaw(
		echarts.init(document.getElementById('chinaMap') as HTMLDivElement)
	);
	chinaMapChart.value.setOption(options);
	// 大小自适应
	window.addEventListener("resize", () => {
		chinaMapChart.value.resize();
	});
}

const timelyChart = ref<any>("");
const timelyOptions = ref<any>(
	{
		xAxis: {
			type: 'category',
			data: [],
			splitLine: {
				show: false       // 关闭网格线（可选）
			},
			axisTick: {
				show: false       // 关闭刻度线
			},
			axisLine: {
				show: false        // 保持轴线显示（若需隐藏轴线此处改为 false）
			},
			axisLabel: {
				formatter: '{value}',
				color: '#FFFFFF',
				fontFamily: 'PingFang SC',
			},
		},
		yAxis: {
			type: 'value',
			name: '整改及时率%',
			min: 0,
			max: 100,
			interval: 20,
			axisLabel: {
				formatter: '{value}',
				color: '#FFFFFF',
				fontFamily: 'PingFang SC',
			},
			nameTextStyle: {
				color: '#FFFFFF',           // 单位颜色
				//fontSize: 12,
				//padding: [0, 0, 0, 20]  // 可选：调整单位位置
			},
			splitLine: {
				show: false         // 隐藏Y轴分割线（背景网格线）
			},
			axisTick: {
				show: false       // 关闭刻度线
			},
			axisLine: {
				show: false        // 保持轴线显示（若需隐藏轴线此处改为 false）
			},
		},
		series: [
			{
				data: [],
				type: 'bar',
				color: '#3263FE',
				barWidth: timelyBarWidth.value
			}
		],
	}
)

const initTimelyChart = (options: any) => {
	timelyChart.value = markRaw(
		echarts.init(document.getElementById('timelyChart') as HTMLDivElement)
	);
	
	timelyChart.value.setOption(options);
	// 大小自适应
	window.addEventListener("resize", () => {
		timelyChart.value.resize();
	});
}

// 1 or 2
const mapBtnFlag = ref<number>(1)
const summaryBtn = () => {
	mapBtnFlag.value = 1
}

const modifyBtn = () => {
	mapBtnFlag.value = 2
}

const timeoutFlag = ref<number>(2)

const onTimeRateModel = ref<any>({})
const overTimeRateModel = ref<any>({})

const timeoutBtn = () => {
	timeoutFlag.value = 1
	timelinessRateList.value = [
		{
			title: '整改超时率',
			rate: overTimeRateModel.value.allOverTimeRate,
			tip: '整改超时率=当月到期超时处理工单数/当月到期应处理工单数x100%',
		},
		{
			title: '一级整改超时率',
			rate: overTimeRateModel.value.firstLevelOverTimeRate,
		},
		{
			title: '二级整改超时率',
			rate: overTimeRateModel.value.secondLevelOverTimeRate,
		},
		{
			key: 'thirdLevelOverTimeRate',
			title: '三级整改超时率',
			rate: overTimeRateModel.value.thirdLevelOverTimeRate,
		},
	]
}

const timelyBtn = () => {
	timeoutFlag.value = 2
	timelinessRateList.value = [
		{
			title: '整改及时率',
			rate: onTimeRateModel.value.allOnTimeRate,
			tip: '整改及时率=当月到期按时处理工单数/当月到期应处理工单数x100%',
		},
		{
			title: '一级整改及时率',
			rate: onTimeRateModel.value.firstLevelOnTimeRate,
		},
		{
			title: '二级整改及时率',
			rate: onTimeRateModel.value.secondLevelOnTimeRate,
		},
		{
			title: '三级整改及时率',
			rate: onTimeRateModel.value.thirdLevelOnTimeRate,
		},
	]
}

//各场景风险预警单统计
const senseOptionData = ref<Array<any>>([
	{
		key: 'mcOrderRecordNumber',
		name: '移动云-产品订购组合不合理',
		value: 10,
		itemStyle: {
			color: 'rgba(246,116,4,0.8)',
		},
		unit: '个',
	},
	{
		key: 'mcIncomeRecordNumber',
		name: '移动云-产品收入异动',
		value: 15,
		itemStyle: {
			color: 'rgba(34,196,255,0.8)',
		},
		unit: '个',
	},
	{
		key: 'ictUpDownRecordNumber',
		name: 'ICT-上下游存在关联关系',
		value: 8,
		itemStyle: {
			color: 'rgba(170,255,0,0.8)'
		},
		unit: '个',
	},
	{
		key: 'ictABRecordNumber',
		name: 'ICT-上下游存在AB/BA关系',
		value: 12,
		itemStyle: {
			color: 'rgba(255,170,255,0.8)'
		},
		unit: '个',
	}
])

//各场景风险涉及金额统计
const amountOptionData = ref<Array<any>>([
	{
		key: 'mcOrderProjectMoney',
		name: '移动云-产品订购组合不合理',
		value: 300,
		itemStyle: {
			color: 'rgba(246,116,4,0.8)',
		},
		unit: '万元',
	},
	{
		key: 'mcIncomeProjectMoney',
		name: '移动云-产品收入异动',
		value: 500,
		itemStyle: {
			color: 'rgba(34,196,255,0.8)',
		},
		unit: '万元',
	},
	{
		key: 'ictUpDownProjectMoney',
		name: 'ICT-上下游存在关联关系',
		value: 600,
		itemStyle: {
			color: 'rgba(170,255,0,0.8)'
		},
		unit: '万元',
	},
	{
		key: 'ictABProjectMoney',
		name: 'ICT-上下游存在AB/BA关系',
		value: 400,
		itemStyle: {
			color: 'rgba(255,170,255,0.8)'
		},
		unit: '万元',
	}
])

const senseOption = ref<any>()
const amountOption = ref<any>()

//改为2D平面图形（饼图颜色调整）
const initPlaneCharts = (chartId: string, optionData: any, option: any) => {
	let myChart = echarts.init(document.getElementById(chartId));
	option = {
		tooltip: {
			trigger: 'item',
			position: function (point: any, params: any, dom: any, rect: any, size: any) {
				if (size) {
					return [size.viewSize[0] - 100, '50%']; // 右侧固定位置
				}
				return ['center', 'center']; // 备用位置
			},
			//formatter: '{a}<br/>{b}: {c} 万元'  // 使用字符串模板添加单位
			/*formatter: (params) => {
				return `${params.name}: ${params.value} ${params.data.unit || ''}`
			}*/
		},
		series: [
			{
				name: '各场景风险预警单统计',
				type: 'pie',
				radius: ['30%', '60%'],
				center: ['50%', '50%'],
				// adjust the start and end angle
				startAngle: 0,
				endAngle: 360,
				color: ['rgba(64,95,229,0.8)', 'rgba(78,135,246,0.8)', 'rgba(93,193,82,0.8)', 'rgba(229,143,71,0.8)'],
				data: optionData,
				label: {
					show: true,
					opacity: 1,
					position: 'outside',
					alignTo: 'edge',
					padding: [0, -50, -50, -50],
					formatter: (params: any) => {
						const name = params.name.length > 6 ?
							params.name.substring(0, 6) + '\n\r' + params.name.substring(6) :
							params.name;
						const value = params.data.value
						const unit = params.data.unit
						return `${name}\n${value}${unit}`;//\n${params.percent}%
					},
					verticalAlign: 'bottom',
					distance: 20,
					color: '#FFFFFF',
					fontSize: 10,
					lineHeight: 15,
					overflow: 'none',      // 禁用默认溢出隐藏:ml-citation{ref="8" data="citationList"}
					width: 80,            // 标签区域宽度限制
					//fontFamily: 'YouSheBiaoTiYuan',
				},
				labelLine: {
					show: true,
					length: 12,
					length2: 48,
					smooth: 0,
					color: (params: any) => {
						return params.data.itemStyle.color;
					},
				},
				labelLayout: {
					hideOverlap: false,
				},
				emphasis: {               // 高亮样式
					label: {
						show: true,
						//fontSize: 12,
						//fontWeight: 'bold'
					}
				},
			}
		]
	};
	myChart.setOption(option);
	bindListen(myChart, option);
}
const init3dCharts = (chartId: string, optionData: any, option: any) => {
	//构建3d饼状图
	let myChart = echarts.init(document.getElementById(chartId));
	// 传入数据生成 option
	option = getPie3D(optionData, 0.8);
	myChart.setOption(option);
	// 是否需要label指引线，如果要就添加一个透明的2d饼状图并调整角度使得labelLine和3d的饼状图对齐，并再次setOption
	option.series.push({
		name: 'pie2d',
		type: 'pie',
		startAngle: -20, // 起始角度，支持范围[0, 360]。
		clockwise: false,// 饼图的扇区是否是顺时针排布。上述这两项配置主要是为了对齐3d的样式
		radius: ['40%', '50%'],
		center: ['50%', '50%'],
		data: optionData,
		//zoom: 0.1,
		itemStyle: {
			opacity: 0
		},
		legend: {
			left: 'center',
			textStyle: {
				color: '#FFFFFF',
				fontSize: 10,       // 字体大小调整
				fontFamily: 'PingFang SC',
			},
		},
		label: {
			show: true,
			opacity: 1,
			position: 'outside',
			alignTo: 'edge',
			padding: [0, -50, 50, -50],
			formatter: (params: any) => {
				const name = params.name.length > 6 ?
					params.name.substring(0, 6) + '\n\r' + params.name.substring(6) :
					params.name;
				const value = params.data.value
				const unit = params.data.unit
				return `${name}\n${value}${unit}`;//\n${params.percent}%
			},
			verticalAlign: 'bottom',
			distance: 20,
			color: '#FFFFFF',
			fontSize: 10,
			lineHeight: 15,
			overflow: 'none',      // 禁用默认溢出隐藏:ml-citation{ref="8" data="citationList"}
			width: 80,            // 标签区域宽度限制
			//fontFamily: 'YouSheBiaoTiYuan',
		},
		labelLine: {
			show: true,
			length: 12,
			length2: 48,
			smooth: 0,
			color: (params: any) => {
				return params.data.itemStyle.color;
			},
		},
		labelLayout: {
			hideOverlap: false,
		},
		emphasis: {
			label: {
				show: true,
				fontSize: '12',
				//fontWeight: 'bold'
			}
		},
	});
	myChart.setOption(option);
	bindListen(myChart, option);
}


const getPie3D = (pieData: any, internalDiameterRatio: any) => {
	//internalDiameterRatio:透明的空心占比
	let series: Array<any> = [];
	let sumValue = 0;
	let startValue = 0;
	let endValue = 0;
	let legendData: Array<any> = [];
	let legendBfb: Array<any> = [];
	let k = 1 - internalDiameterRatio;
	pieData.sort((a: any, b: any) => {
		return (b.value - a.value);
	});
	// 为每一个饼图数据，生成一个 series-surface 配置
	for (let i = 0; i < pieData.length; i++) {
		sumValue += pieData[i].value;
		let seriesItem: any = {
			name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
			type: 'surface',
			parametric: true,
			wireframe: {
				show: false
			},
			pieData: pieData[i],
			pieStatus: {
				selected: false,
				hovered: false,
				k: k
			},
			center: ['10%', '50%'],
			label: {
				show: true,
				formatter: (params: any) => {
					const total = data.value.reduce((a: any, b: any) => a + b.value, 0);
					const percent = ((params.data.value / total) * 100).toFixed(2); // 百分比保留两位小数
					return `${percent}%`;
				},
				textStyle: {
					color: "#fff",       // 标签文字颜色
					fontSize: 14,        // 字体大小
					backgroundColor: "rgba(0,0,0,0)", // 背景透明
				},
				position: "outside",   // 标签位置：外侧
				distance: 30,// 标签距离扇区边缘的距离
			},
		};
		
		if (typeof pieData[i].itemStyle != 'undefined') {
			let itemStyle: any = {};
			typeof pieData[i].itemStyle.color != 'undefined' ? itemStyle.color = pieData[i].itemStyle.color : null;
			typeof pieData[i].itemStyle.opacity != 'undefined' ? itemStyle.opacity = pieData[i].itemStyle.opacity : null;
			seriesItem.itemStyle = itemStyle;
		}
		series.push(seriesItem);
	}
	
	// 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
	// 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
	legendData = [];
	legendBfb = [];
	for (let i = 0; i < series.length; i++) {
		endValue = startValue + series[i].pieData.value;
		series[i].pieData.startRatio = startValue / sumValue;
		series[i].pieData.endRatio = endValue / sumValue;
		series[i].parametricEquation = getParametricEquation(series[i].pieData.startRatio, series[i].pieData.endRatio,
			false, false, k, series[i].pieData.value);
		startValue = endValue;
		let bfb = formatFloat(series[i].pieData.value / sumValue, 4);
		legendData.push({
			name: series[i].name,
			value: bfb
		});
		legendBfb.push({
			name: series[i].name,
			value: bfb,
			actualValue: series[i].pieData.value,
		});
	}
	let boxHeight = getHeight3D(series, 30);//通过传参设定3d饼/环的高度，26代表26px
	// 准备待返回的配置项，把准备好的 legendData、series 传入。
	let option = {
		labelLine: {
			show: true,
			lineStyle: {
				color: '#7BC0CB'
			}
		},
		label: {
			show: true,
			position: 'top',
			rich: {
				b: {
					color: '#FFFFFF',
					fontSize: 12,
					lineHeight: 12
				},
				c: {
					fontSize: 12,
				},
			},
			formatter: '{b}: {c}万元',
		},
		tooltip: {
			position: function (point: any, params: any, dom: any, rect: any, size: any) {
				return ['center', 'center'];
			},
			formatter: (params: any) => {
				if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== 'pie2d') {
					let bfb = ((option.series[params.seriesIndex].pieData.endRatio - option.series[params.seriesIndex].pieData.startRatio) *
						100).toFixed(2);
					return `${params.seriesName}<br/>` +
						`<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${params.color};"></span>` +
						`${bfb}%`;
				}
			}
		},
		xAxis3D: {
			min: -1,
			max: 1
		},
		yAxis3D: {
			min: -1,
			max: 1
		},
		zAxis3D: {
			min: -1,
			max: 1
		},
		grid3D: {
			show: false,
			boxHeight: boxHeight, //圆环的高度
			viewControl: { //3d效果可以放大、旋转等，请自己去查看官方配置
				alpha: 35, //角度
				distance: 300,//调整视角到主体的距离，类似调整zoom
				rotateSensitivity: 0, //设置为0无法旋转
				zoomSensitivity: 0, //设置为0无法缩放
				panSensitivity: 0, //设置为0无法平移
				autoRotate: false //自动旋转
			}
		},
		series: series
	};
	return option;
}

//获取3d丙图的最高扇区的高度
const getHeight3D = (series: any, height: any) => {
	series.sort((a: any, b: any) => {
		return (b.pieData.value - a.pieData.value);
	})
	return height * 25 / series[0].pieData.value;
}

// 生成扇形的曲面参数方程，用于 series-surface.parametricEquation
const getParametricEquation = (startRatio: any, endRatio: any, isSelected: any, isHovered: any, k: any, h: any) => {
	// 计算
	let midRatio = (startRatio + endRatio) / 2;
	let startRadian = startRatio * Math.PI * 2;
	let endRadian = endRatio * Math.PI * 2;
	let midRadian = midRatio * Math.PI * 2;
	// 如果只有一个扇形，则不实现选中效果。
	if (startRatio === 0 && endRatio === 1) {
		isSelected = false;
	}
	// 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
	k = typeof k !== 'undefined' ? k : 1 / 3;
	// 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
	let offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
	let offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;
	// 计算高亮效果的放大比例（未高亮，则比例为 1）
	let hoverRate = isHovered ? 1.05 : 1;
	// 返回曲面参数方程
	return {
		u: {
			min: -Math.PI,
			max: Math.PI * 3,
			step: Math.PI / 32
		},
		v: {
			min: 0,
			max: Math.PI * 2,
			step: Math.PI / 20
		},
		x: function (u: any, v: any) {
			if (u < startRadian) {
				return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
			}
			if (u > endRadian) {
				return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
			}
			return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
		},
		y: function (u: any, v: any) {
			if (u < startRadian) {
				return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
			}
			if (u > endRadian) {
				return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
			}
			return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
		},
		z: function (u: any, v: any) {
			if (u < -Math.PI * 0.5) {
				return Math.sin(u);
			}
			if (u > Math.PI * 2.5) {
				return Math.sin(u) * h * .1;
			}
			return Math.sin(v) > 0 ? 1 * h * .1 : -1;
		}
	};
}

const formatFloat = (num: any, n: any) => {
	var f = parseFloat(num);
	if (isNaN(f)) {
		return false;
	}
	f = Math.round(num * Math.pow(10, n)) / Math.pow(10, n); // n 幂
	var s = f.toString();
	var rs = s.indexOf('.');
	//判定如果是整数，增加小数点再补0
	if (rs < 0) {
		rs = s.length;
		s += '.';
	}
	while (s.length <= rs + n) {
		s += '0';
	}
	return s;
}

const bindListen = (myChart: any, option: any) => {
	// 监听鼠标事件，实现饼图选中效果（单选），近似实现高亮（放大）效果。
	let selectedIndex = '';
	let hoveredIndex = '';
	// 监听点击事件，实现选中效果（单选）
	myChart.on('click', function (params: any) {
		// 从 option.series 中读取重新渲染扇形所需的参数，将是否选中取反。
		let isSelected = !option.series[params.seriesIndex].pieStatus.selected;
		let isHovered = option.series[params.seriesIndex].pieStatus.hovered;
		let k = option.series[params.seriesIndex].pieStatus.k;
		let startRatio = option.series[params.seriesIndex].pieData.startRatio;
		let endRatio = option.series[params.seriesIndex].pieData.endRatio;
		// 如果之前选中过其他扇形，将其取消选中（对 option 更新）
		if (selectedIndex !== '' && selectedIndex !== params.seriesIndex) {
			option.series[selectedIndex].parametricEquation = getParametricEquation(option.series[
				selectedIndex].pieData
				.startRatio, option.series[selectedIndex].pieData.endRatio, false, false, k, option.series[
				selectedIndex].pieData
				.value);
			option.series[selectedIndex].pieStatus.selected = false;
		}
		// 对当前点击的扇形，执行选中/取消选中操作（对 option 更新）
		option.series[params.seriesIndex].parametricEquation = getParametricEquation(startRatio, endRatio,
			isSelected,
			isHovered, k, option.series[params.seriesIndex].pieData.value);
		option.series[params.seriesIndex].pieStatus.selected = isSelected;
		// 如果本次是选中操作，记录上次选中的扇形对应的系列号 seriesIndex
		isSelected ? selectedIndex = params.seriesIndex : null;
		// 使用更新后的 option，渲染图表
		myChart.setOption(option);
	});
	// 监听 mouseover，近似实现高亮（放大）效果
	myChart.on('mouseover', function (params: any) {
		// 准备重新渲染扇形所需的参数
		let isSelected;
		let isHovered;
		let startRatio;
		let endRatio;
		let k;
		// 如果触发 mouseover 的扇形当前已高亮，则不做操作
		if (hoveredIndex === params.seriesIndex) {
			return;
			// 否则进行高亮及必要的取消高亮操作
		} else {
			// 如果当前有高亮的扇形，取消其高亮状态（对 option 更新）
			if (hoveredIndex !== '') {
				// 从 option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 false。
				isSelected = option.series[hoveredIndex].pieStatus.selected;
				isHovered = false;
				startRatio = option.series[hoveredIndex].pieData.startRatio;
				endRatio = option.series[hoveredIndex].pieData.endRatio;
				k = option.series[hoveredIndex].pieStatus.k;
				// 对当前点击的扇形，执行取消高亮操作（对 option 更新）
				option.series[hoveredIndex].parametricEquation = getParametricEquation(startRatio, endRatio,
					isSelected,
					isHovered, k, option.series[hoveredIndex].pieData.value);
				option.series[hoveredIndex].pieStatus.hovered = isHovered;
				// 将此前记录的上次选中的扇形对应的系列号 seriesIndex 清空
				hoveredIndex = '';
			}
			// 如果触发 mouseover 的扇形不是透明圆环，将其高亮（对 option 更新）
			if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== 'pie2d') {
				// 从 option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 true。
				isSelected = option.series[params.seriesIndex].pieStatus.selected;
				isHovered = true;
				startRatio = option.series[params.seriesIndex].pieData.startRatio;
				endRatio = option.series[params.seriesIndex].pieData.endRatio;
				k = option.series[params.seriesIndex].pieStatus.k;
				// 对当前点击的扇形，执行高亮操作（对 option 更新）
				option.series[params.seriesIndex].parametricEquation = getParametricEquation(startRatio, endRatio,
					isSelected, isHovered, k, option.series[params.seriesIndex].pieData.value + 5);
				option.series[params.seriesIndex].pieStatus.hovered = isHovered;
				// 记录上次高亮的扇形对应的系列号 seriesIndex
				hoveredIndex = params.seriesIndex;
			}
			// 使用更新后的 option，渲染图表
			myChart.setOption(option);
		}
	});
	// 修正取消高亮失败的 bug
	myChart.on('globalout', function () {
		// 准备重新渲染扇形所需的参数
		let isSelected;
		let isHovered;
		let startRatio;
		let endRatio;
		let k;
		if (hoveredIndex !== '') {
			// 从 option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 true。
			isSelected = option.series[hoveredIndex].pieStatus.selected;
			isHovered = false;
			k = option.series[hoveredIndex].pieStatus.k;
			startRatio = option.series[hoveredIndex].pieData.startRatio;
			endRatio = option.series[hoveredIndex].pieData.endRatio;
			// 对当前点击的扇形，执行取消高亮操作（对 option 更新）
			option.series[hoveredIndex].parametricEquation = getParametricEquation(startRatio, endRatio,
				isSelected,
				isHovered, k, option.series[hoveredIndex].pieData.value);
			option.series[hoveredIndex].pieStatus.hovered = isHovered;
			// 将此前记录的上次选中的扇形对应的系列号 seriesIndex 清空
			hoveredIndex = '';
		}
		// 使用更新后的 option，渲染图表
		myChart.setOption(option);
	});
}

const historySelectName = ref<string>('');
const handleChinaMapClick = async (params: echarts.ECElementEvent) => {
	if (params.name) {
		historySelectName.value = params.name
	}
}

const handleChinaMapMouseover = async (params: echarts.ECElementEvent) => {
	if (params.name) {
		movedProvinceName.value = params.name
		regionLevel.value = 2
		if (historySelectName.value !== params.name) {
			chinaMapChart.value.dispatchAction({
				type: 'unselect',
				name: historySelectName.value,
			})
		}
		if (mapBtnFlag.value === 1) {
			warningDetailData.title = params.name + `-${monthString.value}风险预警单：`
			warningDetailData.sum = params.data.regionMonthRecordNumber
			warningDetailData.ictCount = params.data.regionIctRecordNumber
			warningDetailData.cloudCount = params.data.regionMcRecordNumber
			warningDetailData.pendingRectificationCount = params.data.regionPdRcfRecordNumber
			
		} else if (mapBtnFlag.value === 2) {
			warningDetailData.title = params.name + `-${monthString.value}历史整改单：`
			warningDetailData.sum = params.data.regionMonthRcfRecordNumber
			warningDetailData.ictCount = params.data.regionIctRcfRecordNumber
			warningDetailData.cloudCount = params.data.regionMcRcfRecordNumber
			warningDetailData.completionRate = params.data.regionMonthRcfRate
		}
		if (movedProvinceName.value !== '南海诸岛') {
			warningDetailRef.value?.init('view')
		}
	}
}

const handleProvinceMapMouseover = async (params: echarts.ECElementEvent) => {
	if (params.name) {
		movedProvinceName.value = params.name
		regionLevel.value = 3
		if (historySelectName.value !== params.name) {
			chinaMapChart.value.dispatchAction({
				type: 'unselect',
				name: historySelectName.value,
			})
		}
		if (mapBtnFlag.value === 1) {
			warningDetailData.title = params.name + `-${monthString.value}风险预警单：`
			warningDetailData.sum = params.data.regionMonthRecordNumber
			warningDetailData.ictCount = params.data.regionIctRecordNumber
			warningDetailData.cloudCount = params.data.regionMcRecordNumber
			warningDetailData.pendingRectificationCount = params.data.regionPdRcfRecordNumber
			
		} else if (mapBtnFlag.value === 2) {
			warningDetailData.title = params.name + `-${monthString.value}历史整改单：`
			warningDetailData.sum = params.data.regionMonthRcfRecordNumber
			warningDetailData.ictCount = params.data.regionIctRcfRecordNumber
			warningDetailData.cloudCount = params.data.regionMcRcfRecordNumber
			warningDetailData.completionRate = params.data.regionMonthRcfRate
		}
		warningDetailRef.value?.init('view')
	}
}

// 中国地图双击事件
const handleChinaMapDbClick = async (params: echarts.ECElementEvent) => {
	level.value = '2'
	if (params.name) {
		selectedProvinceName.value = params.name;
		let provinceJsonPath = provinces[selectedProvinceName.value];
		
		if (provinceJsonPath) {
			let provinceJson
			try {
				const response = await fetch(provinceJsonPath)
				provinceJson = await response.json()
			} catch (error) {
				console.error('获取json文件内容失败', error);
			}
			
			//       省份地图颜色调整
			if (provinceJson) {
				chinaMapVisible.value = false
				echarts.registerMap(selectedProvinceName.value, provinceJson)
				let options = {
					tooltip: {
						show: false,
						//trigger: 'item'
					},
					toolbox: {
						show: false,
						orient: 'vertical',
						x: 'center',
						y: 'center',
						feature: {
							mark: {
								show: true
							},
						}
					},
					geo: [{
						map: selectedProvinceName.value,
						roam: false, //是否允许缩放
						zoom: 1.2,
						scaleLimit: {
							min: 0,
							max: 3
						}, //缩放级别
						itemStyle: {
							normal: {
								areaColor: 'rgb(218,233,255)',
								borderColor: 'rgba(147,147,147)',
								shadowColor: 'rgba(0,0,0,0)',
								shadowOffsetY: 0,
								shadowBlur: 0
							}
						},
						tooltip: {
							show: false
						},
						emphasis: {
							itemStyle: {
								areaColor: '#2D8CF0', // 悬停区域颜色
								borderColor: '#FFF',
								borderWidth: 0
							},
							// 悬停标签样式
							/*label: {
								show: true,
								color: '#FFF',
								fontSize: 14
							}*/
						}
					}],
					series: [
						{
							name: selectedProvinceName.value,
							type: 'map',
							mapType: selectedProvinceName.value,
							roam: false, // 缩放
							aspectScale: 0.75,
							zoom: 1.2,
							selectedMode: 'single',
							itemStyle: {
								normal: {
									label: {
										show: false
									}
								},
								emphasis: {
									label: {
										show: true
									}
								}
							},
							data: [],
						},
					],
					/*legend: {
						left: 'right',
						//data: ['随机数据']
					},*/
					visualMap: {
						show: false,
						type: 'piecewise',
						left: '15',
						bottom: '15',
						itemWidth: 14,
						itemHeight: 14,
						textStyle: {
							color: '#FFFFFF',
							fontSize: 12,
						},
						pieces: [
							{
								max: 100,
								min: 51,
								label: '51-100',
							},
							{
								max: 50,
								min: 21,
								label: '21-50',
							},
							{
								max: 20,
								min: 1,
								label: '1-20',
							},
							{
								value: 0,
								label: '0',
							},
						],
						inRange: {
							color: ['rgb(218,233,255)', '#8AB3F0', '#649BEB', '#3976CF']
						},
						outOfRange: {
							color: ['#052be8']
						}
					},
				}
				await initProvinceMapChart(options)
				initRiskView()
			}
			return;
		}
	}
};

// 省份地图
const provinceMapRef = ref<HTMLElement>();
const provinceMapChart = ref<any>("");
const initProvinceMapChart = async (options: any) => {
	await nextTick()
	provinceMapChart.value = markRaw(
		echarts.init(document.getElementById('provinceMap') as HTMLDivElement)
	);
	
	provinceMapChart.value.on('click', handleProvinceMapMouseover);
	provinceMapChart.value.setOption(options);
	// 大小自适应
	window.addEventListener("resize", () => {
		provinceMapChart.value.resize();
	});
}

const midCardList = ref<Array<any>>([
	{
		key: 'monthRecordNumber',
		img: 'risk/platform/order_one.png',
		title: `风险预警单`,
		count: 0,
		unit: '个'
	},
	{
		key: 'allRecordNumber',
		img: 'risk/platform/order_two.png',
		title: '累计风险预警单',
		count: 0,
		unit: '个',
		tip: '系统建设以来所产生的风险预警单总数',
	},
	{
		key: 'allCorrectionRecordNumber',
		img: 'risk/platform/order_three.png',
		title: '累计整改完成数',
		count: 0,
		unit: '个',
		color: '#EB7629',
		tip: '系统建设以来所产生的风险预警单整改完成总数',
	},
	{
		key: 'allCorrectionRecordRate',
		img: 'risk/platform/order_four.png',
		title: '累计整改完成率',
		count: 0,
		unit: '%',
		color: '#EB7629',
		tip: '系统建设以来所产生的风险预警单整改完成率',
	},
])

const model = ref<Array<any>>([1, 2, 3, 4])
const modelOptions = ref<Array<any>>([
	{
		label: '上下游存在AB/BA关系',
		value: 1,
	},
	{
		label: '上下游存在关联关系',
		value: 2,
	},
	{
		label: '产品订购组合不合理',
		value: 3,
	},
	{
		label: '产品收入异动',
		value: 4,
	},
])

const modelMap = new Map([
	[1, '上下游存在AB/BA关系'],
	[2, '上下游存在关联关系'],
	[3, '产品订购组合不合理'],
	[4, '产品收入异动'],
])

const modelChange = async (value) => {
	await nextTick();
	
	let filteredSeries;
	
	if (value && value.length > 0) {
		if (trend.value === 1) {
			const selectedNames = value.map(v => modelMap.get(v)).filter(name => name);
			filteredSeries = trendsSeries.value.filter(item => selectedNames.includes(item.name));
		} else {
			const selectedNames = value.map(v => modelMap.get(v)).filter(name => name);
			filteredSeries = productIncomeSeries.filter(item => selectedNames.includes(item.name));
			filteredSeries.push(amountSeries)
		}
	} else {
		if (trend.value === 1) {
			filteredSeries = trendsSeries.value;
		} else {
			filteredSeries = productIncomeSeries;
		}
	}
	
	// 更新 series
	trendsOptions.value.series = filteredSeries;
	
	// 可选：更新 legend
	trendsOptions.value.legend.data = value && value.length > 0
		? Array.from(modelMap).filter(([k]) => value.includes(k)).map(([, v]) => v)
		: Array.from(modelMap.values());
	
	// 设置 ECharts 图表
	trendsChart.value.setOption(trendsOptions.value, true);
}

const warningFormUnitMap = new Map<string, string>([
		['allRecordNumber', 'allMoMRecordNumber'],
		['firstLevelRecordNumber', 'firstLevelMoMNumber'],
		['secondLevelRecordNumber', 'secondLevelMoMNumber'],
		['thirdLevelRecordNumber', 'thirdLevelMoMNumber'],
	]
)

const warningFormList = ref<Array<any>>([
	{
		key: 'allRecordNumber',
		img: 'risk/platform/risk_blue.png',
		title: '总预警单',
		count: 0,
		unit: 0,
	},
	{
		key: 'firstLevelRecordNumber',
		img: 'risk/platform/risk_red.png',
		title: '一级预警单',
		count: 0,
		unit: 0,
	},
	{
		key: 'secondLevelRecordNumber',
		img: 'risk/platform/risk_orange.png',
		title: '二级预警单',
		count: 0,
		unit: 0,
	},
	{
		key: 'thirdLevelRecordNumber',
		img: 'risk/platform/risk_green.png',
		title: '三级预警单',
		count: 0,
		unit: 0,
	},
])

const timelinessRateList = ref<Array<any>>()

const drawer = ref<boolean>(false)

// ai问答页面
const aiAsk = () => {
	drawer.value = true
	initAiQuestion()
}

const aiReport = () => {
	router.push({
		path: '/analysis/report',
	})
}

const initFlexContainer = () => {
	let docEl = document.documentElement;
	let dpr = window.devicePixelRatio || 1;
	
	// adjust body font size
	function setBodyFontSize() {
		if (document.body) {
			document.body.style.fontSize = 12 * dpr + "px";
		} else {
			document.addEventListener("DOMContentLoaded", setBodyFontSize);
		}
	}
	
	setBodyFontSize();
	
	// set 1rem = viewWidth / 10
	function setRemUnit() {
		let rem = docEl.clientWidth / 24;
		docEl.style.fontSize = rem + "px";
	}
	
	setRemUnit();
	
	// reset rem unit on page resize
	window.addEventListener("resize", setRemUnit);
	window.addEventListener("pageshow", function (e) {
		if (e.persisted) {
			setRemUnit();
		}
	});
	
	// detect 0.5px supports
	if (dpr >= 2) {
		let fakeBody = document.createElement("body");
		let testElement = document.createElement("div");
		testElement.style.border = ".5px solid transparent";
		fakeBody.appendChild(testElement);
		docEl.appendChild(fakeBody);
		if (testElement.offsetHeight === 1) {
			docEl.classList.add("hairlines");
		}
		docEl.removeChild(fakeBody);
	}
}

onMounted(() => {
	initFlexContainer()
	initDateOptions()
	initRiskView()
})

onUnmounted(() => {
	if (rankChartRef.value) {
		rankChart.value.off('click', handleRankChartClick);
		rankChart.value.dispose();
	}
	if (chinaMapChart.value) {
		chinaMapChart.value.off('click', handleChinaMapClick);
		chinaMapChart.value.off('mouseover', handleChinaMapMouseover);
		chinaMapChart.value.off('dblclick', handleChinaMapDbClick);
		chinaMapChart.value.dispose();
	}
	if (provinceMapRef.value) {
		provinceMapChart.value.off('click', handleProvinceMapMouseover);
		provinceMapChart.value.dispose();
	}
});


</script>

<style scoped lang="scss">
.risk-control-platform {
	width: 100vw;
	height: 100vh;
	background: url("@/assets/risk/platform/background.png") no-repeat center;
	background-size: 100% 100%;
	line-height: 0.6rem;
	overflow: hidden;
	
	
	header {
		position: relative;
		display: flex;
		flex-direction: row;
		flex-wrap: nowrap;
		justify-content: space-between;
		align-items: center;
		
		height: 1rem;
		//background: url("@/assets/images/login-bg.jpg") no-repeat;
		//background-size: 100% 100%;
		
		.back-btn {
			width: 2rem;
			z-index: 2;
		}
		
		.companyLogo {
			position: absolute;
			left: 1.8rem; // 返回按钮宽度+间距
			top: 52%;
			transform: translateY(-50%);
			height: 0.3rem;
			object-fit: contain;
			z-index: 1;
		}
		
		h1 {
			font-size: 0.4rem;
			text-align: center;
			
			opacity: 1;
			color: #FFFFFF;
			text-shadow: 0 0.04rem 0.1rem 0 #3483DC;
			font-family: Alimama ShuHeiTi;
			font-weight: bold;
			line-height: normal;
			letter-spacing: 0;
			box-shadow: 0 0 0.12rem rgb(0, 0, 0, .12);
		}
		
		.risk-month {
			width: 2rem;
		}
		
		:deep(.risk-month) {
			.el-select__wrapper {
				background: transparent !important;
				border: none !important;
				box-shadow: none !important;
				
				// 字体颜色修改
				.el-select__input {
					color: #FFFFFF !important;
				}
				
				span {
					color: #FFFFFF !important;
				}
			}
			
			// 下拉图标颜色
			.el-select__caret {
				color: #FFFFFF !important;
			}
		}
		
		// 修改下拉弹窗样式
		:deep(.transparent-select) {
			background: rgba(255, 255, 255, 0.1) !important; // 透明度控制:ml-citation{ref="3" data="citationList"}
			border: none !important;
			box-shadow: none !important;
			
			// 选项字体颜色
			.el-select-dropdown__item {
				color: #FFFFFF !important;
				
				// 悬停/选中状态
				&.hover, &.selected {
					background: rgba(0, 0, 255, 0.1) !important;
				}
			}
		}
	}
	
	.main-box {
		display: flex;
		min-width: 1024px;
		width: 100%;
		margin: 0 auto;
		padding: 0.125rem 0.125rem 0;
		justify-content: center;
		height: calc(100% - 1rem);
	}
	
	.mid-list {
		position: absolute;
		top: 1.4rem;
		display: flex;
		flex-direction: row;
		flex-wrap: nowrap;
		justify-content: center;
		z-index: 299;
		
		.card {
			display: flex;
			flex-direction: row;
			width: 2.4rem;
			height: 0.7121rem;
			opacity: 1;
			border-radius: 0.04rem;
			background: linear-gradient(180deg, #3263FEB7 0%, #3263FE7A 47%, #3263FE00 96%);
			
			&:not(:nth-child(1)) {
				margin-left: 0.0736rem;
			}
			
			.pic {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 34%;
				
				.pic-img {
					width: 0.48rem;
					height: 0.52rem;
				}
			}
			
			.content {
				
				.title {
					opacity: 1;
					color: #FFFFFF;
					font-family: PingFang SC;
					font-weight: bold;
					font-size: 0.17rem;
					line-height: 0.2262rem;
					letter-spacing: 0;
					text-align: left;
					margin-top: 0.05rem;
					white-space: nowrap;
				}
				
				.text {
					height: 0.3rem;
					line-height: 0.3rem;
					
					.count {
						opacity: 1;
						color: #45ABFF;
						font-family: YouSheBiaoTiYuan;
						font-weight: 400;
						font-size: 0.21rem;
						letter-spacing: 0;
						text-align: left;
					}
					
					.unit {
						opacity: 1;
						color: #FFFFFF;
						font-family: PingFang SC;
						font-weight: bold;
						font-size: 0.08rem;
						line-height: 0.15rem;
						letter-spacing: 0;
						text-align: left;
						
					}
				}
			}
		}
	}
	
	.model-name {
		opacity: 1;
		border-radius: 0;
		//background: linear-gradient(270deg, #5DDBFF91 0%, #096EEE 68%);
		//backdrop-filter: blur(347px);
		width: 6rem;
		background: url("@/assets/risk/platform/model-name-bg.png") no-repeat center;
		background-size: 100% 100%;
		margin-left: 0.4rem;
		line-height: 0.47rem;
		
		.name {
			color: #FFFFFF;
			font-family: PingFang SC;
			font-weight: bold;
			font-size: 0.24rem;
			letter-spacing: 0;
			text-align: left;
			margin-left: 0.8rem;
			height: 0.47rem;
		}
	}
	
	.radio-btn {
		display: inline-block;
		line-height: 0.3rem;
		
		.time-out {
			opacity: 1;
			border-radius: 0.12rem;
			background: #2C467E7F;
			backdrop-filter: blur(27.33px);
			width: 0.7rem;
			height: 0.28rem;
			
		}
		
		.timely {
			opacity: 1;
			border-radius: 0.12rem;
			background: #2C467E7F;
			backdrop-filter: blur(27.33px);
			width: 0.7rem;
			height: 0.28rem;
		}
		
		.timeout-bg {
			display: inline-block;
			background: url("@/assets/risk/platform/chaoshi.png");
			background-size: 100% 100%;
			width: 1rem;
			height: 0.4rem;
			
			
			opacity: 1;
			color: #FFFFFF;
			font-family: PingFang SC;
			font-weight: 400;
			font-size: 0.18rem;
			line-height: 0.4rem;
			letter-spacing: 0;
			text-align: center;
			cursor: pointer;
		}
		
		.timely-active {
			display: inline-block;
			background: url("@/assets/risk/platform/jishi.png");
			background-size: 100% 100%;
			width: 1rem;
			height: 0.4rem;
			
			opacity: 1;
			color: #FFFFFF;
			font-family: PingFang SC;
			font-weight: 400;
			font-size: 0.18rem;
			line-height: 0.4rem;
			letter-spacing: 0;
			text-align: center;
			cursor: pointer;
		}
	}
	
	.main-box .column {
		flex: 3;
		position: relative;
		
		.scale {
			position: relative;
			height: 3.1rem;
			
			
			display: flex;
			flex-direction: column;
			
			.model-content {
				/*height: 100%;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;*/
				
				.model-one {
					position: relative;
					margin-left: 0.6rem;
					//margin: 0.5rem auto 0 2rem;
					
					.single-bg {
						width: 3.15rem;
						height: 0.54rem;
						margin-top: 0.05rem;
						margin-left: 0.6rem;
						background: url("@/assets/risk/platform/single-bg.png") no-repeat center;
						background-size: 100% 100%;
						display: flex;
						align-items: center;
						white-space: nowrap;
						
						.title {
							font-size: 0.17rem;
							font-weight: bold;
							opacity: 1;
							color: #FFFFFF;
							font-family: DingTalk JinBuTi;
							line-height: 0.2262rem;
							letter-spacing: 0;
							text-align: left;
							margin-left: 0.7rem;
						}
						
						.count {
							font-size: 0.21rem;
							opacity: 1;
							color: #45ABFF;
							font-family: YouSheBiaoTiYuan;
							font-weight: bold;
							line-height: 0.1997rem;
							letter-spacing: 0;
							text-align: center;
							margin-left: 0.05rem;
						}
						
						.unit {
							font-size: 0.08rem;
							font-weight: 400;
							
							opacity: 1;
							color: #FFFFFF;
							font-family: PingFang SC;
							line-height: 15.08px;
							letter-spacing: 0;
							text-align: left;
						}
					}
					
					
					.sub-title {
						opacity: 1;
						color: #FFFFFF;
						font-family: PingFang SC;
						font-weight: 400;
						font-size: 0.15rem;
						line-height: 0.2262rem;
						letter-spacing: 0;
						text-align: left;
					}
					
					.count {
						font-size: 0.08rem;
						opacity: 1;
						color: #45ABFF;
						font-family: PingFang SC;
						font-weight: bold;
						line-height: 0.1997rem;
						letter-spacing: 0;
						text-align: center;
					}
					
					.unit {
						font-size: 0.08rem;
						font-weight: 400;
						
						opacity: 1;
						color: #FFFFFF;
						font-family: PingFang SC;
						line-height: 15.08px;
						letter-spacing: 0;
						text-align: left;
					}
					
				}
				
				.model-two {
					position: relative;
					margin-left: 0.4rem;
					
					.single-bg-down {
						width: 6rem;
						height: 0.6rem;
						line-height: 0.6rem;
						background: url("@/assets/risk/platform/single-bg-down.png") no-repeat center;
						background-size: 100% 100%;
						white-space: nowrap;
						
						.title {
							font-size: 0.2rem;
							font-weight: bold;
							opacity: 1;
							color: #FFFFFF;
							font-family: PingFang SC;
							line-height: 0.2262rem;
							letter-spacing: 0;
							text-align: left;
						}
						
						.sub-title {
							opacity: 1;
							color: #FFFFFF;
							font-family: PingFang SC;
							font-weight: 400;
							font-size: 0.2rem;
							line-height: 0.2262rem;
							letter-spacing: 0;
							text-align: left;
							margin-left: 0.4rem;
						}
						
						.count {
							font-size: 0.15rem;
							opacity: 1;
							color: #45ABFF;
							font-family: PingFang SC;
							font-weight: bold;
							line-height: 0.1997rem;
							letter-spacing: 0;
							text-align: center;
							margin-left: 0.05rem;
						}
						
						.unit {
							font-size: 0.08rem;
							font-weight: 400;
							
							opacity: 1;
							color: #FFFFFF;
							font-family: PingFang SC;
							line-height: 0.1508rem;
							letter-spacing: 0;
							text-align: left;
						}
					}
				}
			}
			
			
		}
		
		.statistics {
			height: 4.2rem;
			
			.pie-list {
				display: flex;
				flex-direction: row;
				flex-wrap: nowrap;
				
				.text-part {
					text-align: center;
					line-height: 0.4rem;
					margin-top: 0.08rem;
					
					.text {
						opacity: 1;
						color: #FFFFFF;
						font-family: PingFang SC;
						font-weight: bold;
						font-size: 0.16rem;
						line-height: 0.31rem;
						letter-spacing: 0;
						text-align: left;
					}
					
					.text::before {
						content: '';
						display: inline-block;
						width: 0.08rem;
						height: 0.08rem;
						background-color: #409EFF;
						border-radius: 50%;
						margin-right: 0.06rem;
						vertical-align: middle;
					}
				}
				
				.left {
					width: 50%;
					
					.sense-chart {
						height: 3.2rem;
					}
				}
				
				.right {
					width: 50%;
					
					.amount-chart {
						height: 3.2rem;
					}
				}
			}
		}
		
		.trends {
			height: 3.6rem;
			
			
			.sub {
				margin-top: 0.05rem;
				display: flex;
				justify-content: space-between;
				align-items: center;
				
				.sub-title {
					opacity: 1;
					color: #FFFFFF;
					font-family: PingFang SC;
					font-weight: bold;
					font-size: 0.15rem;
					line-height: 0.3102rem;
					letter-spacing: 0;
					text-align: left;
				}
				
				.select-model {
					display: inline-block;
				}
			}
			
			.sub-title::before {
				content: '';
				display: inline-block;
				width: 0.08rem;
				height: 0.08rem;
				background-color: #409EFF;
				border-radius: 50%;
				margin-right: 0.06rem;
				vertical-align: middle;
			}
			
			.chart {
				/*height: 2.19rem;
				width: 5.5rem;*/
				height: 4rem;
				margin-top: 0.2rem;
			}
		}
		
		.seat {
			height: 1rem;
		}
		
		.map-chart {
			--map-china-chart-height: 6.5rem;
			--map-province-chart-height: 6rem;
			
			height: var(--map-china-chart-height);
			
			.china-map {
				height: var(--map-china-chart-height);
				opacity: 0.75;
			}
			
			.province-map {
				height: var(--map-province-chart-height);
				opacity: 0.75;
			}
			
		}
		
		.back-china-map {
			opacity: 1;
			border-radius: 0.12rem;
			background: #2C467E7F;
			backdrop-filter: blur(27.33px);
			width: 2.08rem;
			height: 0.5rem;
			color: #FFFFFF;
		}
		
		.china-map-btn-bar {
			text-align: center;
			line-height: normal;
			margin-bottom: 0.2rem;
			/*position: absolute;
			left: 2rem;
			bottom: 4rem;*/
			
			.time-out {
				opacity: 1;
				border-radius: 0.12rem;
				background: #426FEB8C;
				backdrop-filter: blur(27.33px);
				
			}
			
			.timely {
				opacity: 1;
				border-radius: 0.12rem;
				background: #426FEB8C;
				backdrop-filter: blur(27.33px);
			}
			
			.china-map-btn-active {
				display: inline-block;
				background: url("@/assets/risk/platform/tongji.jpg") no-repeat center;
				background-size: 100% 100%;
				width: 2.5rem;
				height: 0.7rem;
				
				opacity: 1;
				color: #FFFFFF;
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 0.16rem;
				line-height: 0.7rem;
				letter-spacing: 0;
				text-align: center;
				cursor: pointer;
			}
			
			.china-map-btn {
				display: inline-block;
				background: url("@/assets/risk/platform/map_zhenggai.png") no-repeat center;
				background-size: 100% 100%;
				width: 2.3rem;
				height: 0.6rem;
				
				opacity: 1;
				color: #FFFFFF;
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 0.16rem;
				line-height: 0.6rem;
				letter-spacing: 0;
				text-align: center;
				cursor: pointer;
			}
		}
		
		.ranking {
			height: 3.6rem;
			
			.ranking-chart {
				height: 3rem;
				/*width: 5.83rem;*/
				
			}
		}
		
		.add-up {
			height: 4.6rem;
			
			h3 {
				margin-left: 0.16rem;
				margin-top: 0;
				margin-bottom: 0;
			}
			
			.warning-form-list {
				display: flex;
				flex-direction: row;
				flex-wrap: nowrap;
				width: 100%;
				justify-content: space-evenly;
				margin: 0.05rem 0.05rem 0.05rem 0.15rem;
				
				.form-card {
					height: 1.5rem;
					width: 1rem;
					display: flex;
					flex-direction: column;
					
					&:not(:nth-child(1)) {
						margin-left: 0.05rem;
					}
					
					.pic {
						text-align: center;
						
						.pic-img {
							width: 0.56rem;
							height: 0.56rem;
						}
					}
					
					.title {
						opacity: 1;
						color: #FFFFFF;
						font-family: DingTalk JinBuTi;
						font-weight: 500;
						font-size: 0.16rem;
						line-height: 0.28rem;
						letter-spacing: 0;
						text-align: left;
					}
					
					.third-row {
						line-height: 0.28rem;
						white-space: nowrap;
						
						.count {
							opacity: 1;
							color: #FF8432;
							font-family: PingFang SC;
							font-weight: bold;
							font-size: 0.24rem;
							line-height: 0.28rem;
							letter-spacing: 0;
							text-align: left;
							
						}
						
						.unit {
							opacity: 1;
							color: #FFFFFF;
							font-family: PingFang SC;
							font-weight: 400;
							font-size: 0.14rem;
							line-height: 0.28rem;
							letter-spacing: 0;
							text-align: left;
							margin-left: 0.05rem;
						}
					}
				}
			}
			
			.add-up-chart {
				height: 2.6rem;
				margin-top: 0.2rem;
				/*width: 5.5rem;*/
			}
		}
		
		.correction {
			height: 3.5rem;
			margin-top: 0.3rem;
			
			.risk-btn-toolbar {
				display: flex;
				flex-direction: row;
				flex-wrap: nowrap;
				justify-content: space-between;
				height: 0.47rem;
				line-height: 0.47rem;
			}
			
			.correction-bar {
				position: relative;
				display: flex;
				flex-direction: row;
				align-items: center;
				margin-top: 0.1rem;
				//width: 5.76rem;
				height: 0.54rem;
				
				.pic {
					position: absolute;
					left: -0.14rem;
					width: 0.55rem;
					height: 0.55rem;
				}
				
				.correction-rate-bar {
					
					height: 0.6rem;
					width: 100%;
					background: url("@/assets/risk/platform/time_bar.png") no-repeat center;
					background-size: 100% 100%;
					
					.timeliness-rate-list {
						display: flex;
						flex-direction: row;
						flex-wrap: nowrap;
						justify-content: space-evenly;
						width: 100%;
						margin-left: 0.15rem;
						
						.card {
							display: flex;
							flex-direction: column;
							padding: 0.05rem;
							position: relative;
							
							&:not(:nth-child(1)) {
								margin-left: 0.05rem;
							}
							
							&:nth-child(2)::before {
								position: absolute;
								top: 0.1rem;
								left: -0.2rem;
								content: "";
								height: 0.4rem;
								border-left: 0.01rem solid #FFFFFF;
							}
							
							.title {
								opacity: 1;
								color: #FFFFFF;
								font-family: PingFang SC;
								font-weight: bold;
								font-size: 0.15rem;
								line-height: 0.2262rem;
								letter-spacing: 0;
								text-align: left;
								
								.question-filled {
									margin-left: 0.05rem;
									width: 0.05rem;
									height: 0.05rem;
								}
							}
							
							.rate {
								opacity: 1;
								color: #FFFFFF;
								font-family: YouSheBiaoTiYuan;
								font-weight: 500;
								font-size: 0.21rem;
								line-height: 0.3017rem;
								letter-spacing: 0;
								text-align: center;
							}
						}
					}
				}
			}
			
			.table {
				width: 100%;
				
				:deep(.el-table__header) {
					font-size: smaller;
				}
			}
			
			--table-backgroun-t-color: #FFFFFF33;
			
			:deep(.table-bg) {
				opacity: 1;
				border-radius: 0.12rem;
				background: var(--table-backgroun-t-color) !important;
				
			}
			
			:deep(.table-t-bg) {
				background-color: transparent !important;
			}
			
			:deep(.el-scrollbar) {
				height: 1.9rem;
				overflow: auto;
			}
			
			:deep(.el-table) {
				background-color: transparent;
				--el-table-border-color: transparent; // border透明色
			}
			
			:deep(.el-table__expanded-cell) {
				background-color: transparent;
			}
			
			:deep(.el-table--default .el-table__cell) {
				padding: 2px 0;
			}
			
			:deep(.el-table th) {
				background-color: transparent;
				color: #ffffff;
			}
			
			:deep(.el-table tr) {
				background-color: transparent;
				color: #ffffff;
			}
			
			:deep(.el-table tr td) {
				background-color: transparent;
			}
			
			:deep(.el-table tr td:first-child) {
				background-color: transparent;
				border: 1px solid transparent;
				border-radius: 0.12rem 0 0 0.12rem;
			}
			
			:deep(.el-table tr td:last-child) {
				background-color: transparent;
				border: 1px solid transparent;
				border-radius: 0 0.12rem 0.12rem 0;
			}
			
			.el-table__fixed::before {
				background-color: transparent;
			}
			
			.timely-chart {
				height: 3.3rem;
			}
		}
		
		.video-model {
			height: 4rem;
			position: relative;
			margin-top: 0.3rem;
			
			.btn-bar {
				margin-top: 0.05rem;
				width: 100%;
				display: flex;
				flex-direction: row;
				flex-wrap: nowrap;
				justify-content: space-evenly;
				
				.ai-ask {
					opacity: 1;
					width: 1.8rem;
					border-radius: 0.1117rem;
					background: #426FEB;
					box-shadow: inset 0 0 3.72px 2px #FFFFFF66;
				}
				
				.ai-btn {
					background: url("@/assets/risk/platform/ai-btn.png") no-repeat center;
					background-size: 100% 100%;
					width: 1.4rem;
					height: 0.5rem;
					
					opacity: 1;
					color: #FFFFFF;
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.2rem;
					line-height: 0.5rem;
					letter-spacing: 0;
					text-align: center;
					cursor: pointer;
				}
			}
			
			.video-bar {
				text-align: center;
				margin-top: 0.1rem;
				
				.video {
					margin-top: 0.05rem;
					height: 2.85rem;
					width: 6rem;
				}
			}
			
			.robot {
				position: absolute;
				background: url("@/assets/risk/platform/jiqiren.png") no-repeat center;
				background-size: 100% 100%;
				width: 1rem;
				height: 1rem;
				right: 0.5rem;
				bottom: 0.5rem;
				cursor: pointer;
			}
		}
	}
	
	.main-box .column:nth-child(2) {
		flex: 3;
		margin: 0 0.125rem 0.1875rem;
		overflow: hidden;
	}
	
	:deep(.el-drawer) {
		opacity: 1;
		border-radius: 12px;
		background: linear-gradient(180deg, #C6D3FF 0%, #C8E1FC 4%, #F1F6FB 56.00000000000001%, #F1F6FB 100%);
		height: 60%;
		top: auto;
	}
	
	.ai-dialog-log {
		width: 0.25rem;
		height: 0.25rem;
	}
	
	.close-dialog-btn {
		width: 0.15rem;
		height: 0.15rem;
	}
	
	.voice-btn {
		width: 0.16rem;
		height: 0.21rem;
	}
	
	.ai-ask-main {
		margin-bottom: 0.15rem;
		scrollbar-width: thin;
		
		
		.init-tag-bg {
			opacity: 1;
			border-radius: 0.08rem;
			background: #FFFFFF;
			height: 1.5rem;
			padding: 0.2rem;
			
			.ai-hello {
				opacity: 1;
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 0.2rem;
				line-height: normal;
				letter-spacing: 0;
				text-align: left;
			}
			
			.text-horizontal {
				background: linear-gradient(230deg, #538FE8 0%, #48AFBC 24%, #4EA8BE 43%, #6CA7DB 68%, #78A4E8 83%, #6B77D6 100%);
				-webkit-background-clip: text;
				background-clip: text;
				color: transparent;
			}
			
			
			.ai-intro {
				opacity: 1;
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 0.16rem;
				line-height: normal;
				letter-spacing: 0;
				text-align: left;
			}
			
			.init-tag {
				opacity: 1;
				border-radius: 5.84px;
				background: #F0F8FF;
				cursor: pointer;
				
				color: #4D4D4D;
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 0.12rem;
				line-height: normal;
				letter-spacing: 0;
				text-align: left;
			}
		}
	}
	
	.user-msg-bg {
		opacity: 1;
		border-radius: 0.08rem;
		background: #768EDF;
		padding: 0.15rem;
		line-height: 0.15rem;
		display: inline-block;
		
		.user-msg {
			
			opacity: 1;
			color: #FFFFFF;
			font-family: PingFang SC;
			font-weight: 400;
			font-size: 0.14rem;
			line-height: 0.24rem;
			letter-spacing: 0;
			text-align: right;
		}
	}
	
	.ai-msg-bg {
		opacity: 1;
		border-radius: 0.08rem;
		background: #FFFFFF;
		padding: 0.15rem;
		line-height: 0.15rem;
		display: inline-block;
		
		.loading {
			width: 0.2rem;
			height: 0.2rem;
		}
		
		.ai-msg {
			opacity: 1;
			color: #3D3D3D;
			font-family: PingFang SC;
			font-weight: 400;
			font-size: 0.14rem;
			line-height: 0.24rem;
			letter-spacing: 0;
			text-align: left;
		}
	}
	
	:deep(.el-drawer__header) {
		margin-bottom: 0;
		height: 0.3rem;
	}
	
	:deep(.el-main) {
		padding: 0 0.08rem 0 0;
		width: 100%;
		overflow-x: hidden;
	}
	
	:deep(.el-row) {
		margin-left: 0;
		margin-right: 0;
	}
	
	:deep(.el-footer) {
		--el-footer-height: 40px;
		padding: 0;
	}
	
}

</style>
