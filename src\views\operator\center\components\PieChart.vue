<!-- 饼图 -->
<template>
	<el-card>
		<template #header>
			<el-text
				class="mx-1"
				size="large"
				line-clamp="1"
				tag="b"
			>
				{{ cardTitle }}
			</el-text>
		</template>
		<div :id="id" :class="className" :style="{ height, width }"></div>
	</el-card>
</template>

<script setup lang="ts">
import * as echarts from "echarts";

const props = defineProps({
	id: {
		type: String,
		default: "pieChart",
	},
	className: {
		type: String,
		default: "",
	},
	width: {
		type: String,
		default: "200px",
		required: true,
	},
	height: {
		type: String,
		default: "200px",
		required: true,
	},
	cardTitle: {
		type: String,
		default: "产品分类饼图",
		required: false,
	},
	chartData: {
		type: Array,
		default: [
			{value: 10, name: 'Used'},
			{value: 90, name: 'Unused'}
		],
		required: true,
	},
});

const chart = ref<any>("");

const initChartData = () => {
	if (props.chartData && props.chartData.length > 0) {
		chart.value = markRaw(
			echarts.init(document.getElementById(props.id) as HTMLDivElement)
		);
		// 变更父组件传过来的值
		options.series[0].data = props.chartData
		chart.value.setOption(options);
		window.addEventListener("resize", () => {
			chart.value.resize();
		});
	}
}

const options = {
	title: {
		text: '使用情况',
		subtext: '',
		left: 'center'
	},
	tooltip: {
		trigger: 'item'
	},
	legend: {
		orient: 'vertical',
		left: 'left'
	},
	series: [
		{
			name: '使用情况',
			type: 'pie',
			radius: '50%',
			data: props.chartData,
			emphasis: {
				itemStyle: {
					shadowBlur: 10,
					shadowOffsetX: 0,
					shadowColor: 'rgba(0, 0, 0, 0.5)'
				}
			}
		}
	]
};

watch(() => props.chartData, (newVal, oldVal) => {
	if (newVal !== null) {
		initChartData();
	}
}, {immediate: false, deep: true});

onMounted(() => {
	initChartData()
});

onActivated(() => {
	if (chart.value) {
		chart.value.resize();
	}
});
</script>
