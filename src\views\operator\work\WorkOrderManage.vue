<template>
	<div>
		<el-card style="width: 100%;">
			<template #header>
				<el-text
					class="mx-1"
					size="large"
					line-clamp="1"
					tag="b"
				>
					{{ orderTitle }}
				</el-text>
			</template>

			<el-container>
				<el-header>
					<el-form :model="queryForm" ref="queryFormRef" label-width="auto">
						<el-row :gutter="24">
							<el-col :span="6">
								<el-form-item label="工单编号" prop="workOrderId">
									<el-input v-model="queryForm.workOrderId" placeholder="请输入订单编号"
									          clearable></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="6">
								<el-form-item label="工单状态" prop="workOrderStatus">
									<dictionary v-model="queryForm.workOrderStatus" type-code="workOrderStatus"/>
								</el-form-item>
							</el-col>
							<el-col :span="8">
								<el-form-item label="创建时间" prop="createTime">
									<el-date-picker
										v-model="queryForm.createTime"
										type="daterange"
										range-separator="至"
										start-placeholder="请选择开始时间"
										end-placeholder="请选择结束时间"
										size="default"
										clearable
									/>
								</el-form-item>
							</el-col>
							<el-col :span="4">
								<el-button type="primary" @click="query(1)">查询</el-button>
								<el-button plain @click="resetForm(queryFormRef)">重置</el-button>
							</el-col>
						</el-row>
					</el-form>
				</el-header>
				<el-main>
					<el-table
						v-loading="loading"
						:data="tableData"
						border
						height="450"
						style="width: 100%"
						tooltip-effect="dark"
					>
						<el-table-column prop="workOrderId" label="工单编号" align="center" width="200"/>
						<el-table-column prop="person" label="提出人" align="center"/>
						<el-table-column prop="appRover" label="审批人" align="center"/>
						<el-table-column prop="processTime" label="审批时间" align="center"/>
						<el-table-column
							prop="workOrderContent"
							label="工单内容"
							align="center"
							width="200"
						>
							<template #default="scope">
								<el-tooltip
									effect="dark"
									placement="bottom"
									:content="getContent(scope.row.workOrderContent)"
									raw-content
								>
									<div class="cell el-tooltip">{{scope.row.workOrderContent}}</div>
								</el-tooltip>
							</template>
						</el-table-column>
						<el-table-column
							prop="workOrderStatus"
							label="工单状态"
							align="center"
							:formatter="statusFormatter"
						/>
						<el-table-column prop="createTime" label="订单创建时间" align="center"/>
						<el-table-column label="操作" align="center">
							<template #default="scope">
								<el-button link
								           :type="scope.row.workOrderStatus == 1 ?  'info' : 'primary'"
								           @click="proceed(scope.row)"
								           :disabled="scope.row.workOrderStatus == 1"
								>{{ scope.row.workOrderStatus == 1 ? '已处理' : '处理' }}
								</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-main>
				<el-footer>
					<el-pagination
						style="float: right;line-height: 60px"
						v-model:current-page="current"
						v-model:page-size="size"
						:page-sizes="[10, 50, 100, 200]"
						:small="false"
						:background="true"
						size="default"
						layout="total, sizes, prev, pager, next, jumper"
						:total="total"
						@size-change="handleSizeChange"
						@current-change="handleCurrentChange"
					/>
				</el-footer>
			</el-container>

		</el-card>
	</div>
</template>

<script setup lang="ts">
import {FormInstance} from "element-plus";
import {workOrderDeal, workOrderQuery} from "@/api/workOrder";
import {QueryData, WorkOrderQuery} from "@/api/workOrder/types";
import moment from "moment/moment";
import {initDictMap} from "@/utils/commonUtils";

const orderTitle = ref('工单管理')

const queryForm = ref<WorkOrderQuery>({
	workOrderId: '',
	workOrderStatus: null,
	createTime: null,
});

const tableData = ref([]);

const loading = ref(false)
const current = ref<number>(1)
const size = ref<number>(10)
const total = ref<number>(0)

const getContent = (cellValue: string) => {
	if (cellValue) {
		let array = cellValue.split(',');
		if (array && array.length > 0) {
			return array.join('<br/>')
		} else {
			return ''
		}
	}
}

const statusFormatter = (row: any, column: any, cellValue: any, index: number) => {
	return typeCodeMap.get('workOrderStatus').value[cellValue]
}

const handleSizeChange = (val: number) => {
	size.value = val
	query()
}
const handleCurrentChange = (val: number) => {
	current.value = val
	query(val)
}

const query = (index?: number) => {
	if (index) {
		current.value = index
	}
	let data: QueryData = {
		...queryForm.value,
		pageNum: current.value,
		pageSize: size.value,
		level: 2
	}

	delete data.createTime
	let createTime: any = queryForm.value.createTime
	if (createTime && createTime.length === 2) {
		data.createTimeBegin = moment(createTime[0]).format('YYYY-MM-DD')
		data.createTimeEnd = moment(createTime[1]).format('YYYY-MM-DD')
	}

	loading.value = true
	workOrderQuery(data).then(response => {
		let pageData = response.data;
		if (pageData) {
			tableData.value = pageData.records
			current.value = pageData.current
			size.value = pageData.size
			total.value = pageData.total
		}
	}).finally(() => {
		loading.value = false
	})
}
const queryFormRef = ref<FormInstance>()

const resetForm = (formEl: FormInstance | undefined) => {
	if (!formEl) return
	formEl.resetFields()
}

const proceed = (rowData: object) => {
	workOrderDeal(rowData.workOrderId).then(response => {
		if (response.code === '00000') {
			query()
		}
	})
}

const typeCodeMap = reactive<Map<string, any>>(new Map([
	["workOrderStatus", ref<object>({})],
]));

onMounted(() => {
	query()
	initDictMap(typeCodeMap)
})

</script>

<style scoped lang="scss">

</style>