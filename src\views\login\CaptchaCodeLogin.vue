<template>
	<div class="login-container">
		<el-container class="h-full">
			<el-header>
				<el-image
					:src="getAssetsFile('china_mobile.png')"
					class="w-140px h-43px ml-3 mt-3"
					fit="fill"
				>
				</el-image>
			</el-header>
			<el-main style="height: calc(100% - 120px)">
				<el-row :gutter="24">
					<el-col :offset="2" :span="18">
						<div class="login-title-bar">
							<div class="login-title">政企业务风险防控信息化系统</div>
							<div class="login-title-en-desc">Government enterprise business risk prevention and control
								information system
							</div>
						</div>
					</el-col>
				</el-row>
				<el-row :gutter="24" class="mt-5" style="height: calc(100% - 132px);">
					<el-col :offset="2" :span="18" class="login-card">
						<el-card style="width: 360px;height: 200px;">
							<el-form
								ref="loginFormRef"
								:model="loginData"
								:rules="loginRules"
								class="login-form"
								label-position="top"
								@submit.native.prevent
							>
								<el-form-item label="验证码" prop="captchaCode">
									<el-input
										v-model="loginData.captchaCode"
										auto-complete="off"
										size="default"
										class="flex-1"
										placeholder="请输入验证码"
										@keyup.enter="handleLogin"
									>
										<template #append>
											<el-button
												v-if="obtainFlag"
												class="w-40"
												:disabled="true"
											>
												{{ reGetText }} ({{ countDown }}s)
											</el-button>
											<el-button
												v-else
												type="primary"
												@click="getCaptcha"
												:disabled="obtainFlag"
											>
												{{ getText }}
											</el-button>
										</template>
									</el-input>
								</el-form-item>
							</el-form>
							<el-button
								:loading="loading"
								type="primary"
								size="large"
								class="w-full"
								@click.prevent="handleLogin"
							>{{ $t("login.login") }}
							</el-button>
						</el-card>
					</el-col>
				</el-row>
			</el-main>
			<el-footer>
				<el-row :gutter="24">
					<el-col :offset="2" :span="6">
						<div class="foot-text">Copyright @ 中国移动，All Rights Reserved.</div>
						<div class="foot-text" style="text-align: center;">政企风控预警管理平合</div>
					</el-col>
				</el-row>
			</el-footer>
		</el-container>
	</div>
</template>

<script setup lang="ts">
import {useSettingsStore, useUserStore, usePermissionStore} from "@/store";
import {ebossSsoServerVerify, getSmsCode, verifySmsCode} from "@/api/auth";
import {LocationQuery, LocationQueryValue, RouteRecordRaw, useRoute} from "vue-router";
import {ThemeEnum} from "@/enums/ThemeEnum";

//点击卡片，路由跳转
import {useRouter} from "vue-router";
import {getAssetsFile} from "@/utils/commonUtils";
import {LoginData} from "@/api/auth/types";

const router = useRouter();
const route = useRoute();
// Stores
const userStore = useUserStore();
const permissionStore = usePermissionStore();
const settingsStore = useSettingsStore();

// Internationalization
const {t} = useI18n();

// Reactive states
const icpVisible = ref(true);
const loading = ref(false); // 按钮loading
const isCapslock = ref(false); // 是否大写锁定
const loginFormRef = ref(ElForm); // 登录表单ref
const {height} = useWindowSize();

const getText = ref<string>('获取验证码')
const reGetText = ref<string>('重新获取')
const countDown = ref<number>(60)

const interval = ref<any>()

watch(() => countDown.value, (newVal, oldValue) => {
	if (newVal <= 0) {
		clearInterval(interval.value)
		obtainFlag.value = false
		countDown.value = 120
	}
}, {immediate: false, deep: true});

const obtainFlag = ref<boolean>(false)

const loginData = reactive<any>({
	captchaCode: '',
});

const loginRules = computed(() => {
	return {
		captchaCode: [
			{
				required: true,
				trigger: "blur",
				message: t("login.message.captchaCode.required"),
			},
		],
	};
});

const msgId = ref<string>('')
const mainAcctNew = ref<string>('')
/**
 * 获取验证码
 */
const getCaptcha = async () => {
  let staffCode: string = route.query.staffCode;
  const response = await ebossSsoServerVerify(staffCode);
  mainAcctNew.value = response.data

	let params: any = {
		staffCode: route.query.staffCode,
		mainAcct: mainAcctNew.value,
	}
	getSmsCode(params).then((response: any) => {
		if (response.code === '00000') {
			msgId.value = response.data
			obtainFlag.value = true
			interval.value = setInterval(() => {
				countDown.value--
			}, 1000);
		}
	})
}


/**
 * 登录
 */
const initSsoServerVerify = () => {

}

// 进入大屏页面角色编码
const riskPlatformRoles = ["2001","2003","2004","2005","2006",
	"2007","2008","2012","2013","2014","2015","2016","2018",
	"2019","2020","2021","2022","2023","2024"];

const handleLogin = () => {
	let params: any = {
    mainAcct: mainAcctNew.value,
    staffCode: route.query.staffCode,
		smsCode: loginData.captchaCode,
		msgId: msgId.value,
	}
	loading.value = true
	verifySmsCode(params).then((response: any) => {
			if (response.data === '0') {
				console.log(response.data)
				ElMessage.success('验证成功');
				const loginDataA = ref<LoginData>({
					username: params.staffCode,
					password: ''
				});

				userStore
					.login(loginDataA.value)
					.then(async () => {
						const query: LocationQuery = route.query;
						const redirect = (query.redirect as LocationQueryValue) ?? "/";
						const otherQueryParams = Object.keys(query).reduce(
							(acc: any, cur: string) => {
								if (cur !== "redirect") {
									acc[cur] = query[cur];
								}
								return acc;
							},
							{}
						);

						// 根据相关角色进入不同的页面
						const { roles } = await userStore.getUserInfo();
						const accessRoutes = await permissionStore.generateRoutes(roles);
						accessRoutes.forEach((route: RouteRecordRaw) => {
							if (!router.hasRoute(route)) {
								router.addRoute(route);
							}
						});
						if (roles.some(role => riskPlatformRoles.includes(role))) {
							await router.push({path: '/risk-control-platform'});
						} else {
							await router.push({path: redirect, query: otherQueryParams});
						}
					})
			} else {
				ElMessage.error('验证码错误')
			}
		}
	).finally(() => {
		loading.value = false
	})
}

/**
 * 主题切换
 */

const toggleTheme = () => {
	const newTheme =
		settingsStore.theme === ThemeEnum.DARK ? ThemeEnum.LIGHT : ThemeEnum.DARK;
	settingsStore.changeTheme(newTheme);
};
/**
 * 根据屏幕宽度切换设备模式
 */

watchEffect(() => {
	icpVisible.value = height.value >= 600;
});

/**
 * 检查输入大小写
 */
function checkCapslock(event: KeyboardEvent) {
	// 防止浏览器密码自动填充时报错
	if (event instanceof KeyboardEvent) {
		isCapslock.value = event.getModifierState("CapsLock");
	}
}

onMounted(() => {
	initSsoServerVerify()
	getCaptcha();
});


</script>

<style lang="scss" scoped>
html.dark .login-container {
	overflow-y: auto;
	background: url("@/assets/images/login-bg-dark.jpg") no-repeat center right;
}

.login-container {
	width: 100%;
	height: 100%;
	overflow-y: auto;
	background: url("@/assets/login_bg.png") no-repeat center;
	background-size: 100% 100%;
	//@apply wh-full flex-center;

	.login-form {
		padding: 10px 10px;
	}

	:deep(.el-card__body) {
		background: #FFFFFF;
	}

	.login-title-bar {
		margin-top: 65px;

		.login-title {
			opacity: 1;
			color: #404040;
			font-family: YouSheBiaoTiHei;
			//font-weight: bold;
			font-size: 50px;
			line-height: 24px;
			letter-spacing: 0;
			text-align: left;
		}

		.login-title-en-desc {
			opacity: 1;
			color: #7C7C7C;
			font-family: PingFang SC;
			font-weight: 400;
			font-size: 16px;
			line-height: 24px;
			letter-spacing: 0;
			text-align: left;
			margin-top: 19px;
		}
	}

	.login-card {
		height: 100%;
		display: flex;
		align-items: center;
	}

	.foot-text {
		opacity: 1;
		color: #D3D3D3;
		font-family: PingFang SC;
		font-weight: 400;
		font-size: 16px;
		line-height: 24px;
		letter-spacing: 0;
		text-align: left;
		white-space: nowrap;
	}
}
</style>
