/**
 * 视频上传请求参数
 */
export interface VideoUploadParams {
    file: File | Blob;
    videoType?: string;
    dataVersion?: string;
}

/**
 * 视频上传响应结果
 */
export interface VideoUploadResult {
    code: number;
    message: string;
    data?: {
        filePath?: string;
        fileName?: string;
        fileSize?: number;
        uploadTime?: string;
    };
}

/**
 * 视频元数据信息
 */
export interface VideoMetadata {
    id?: string;
    fileName?: string;
    originalFileName?: string;
    filePath?: string;
    fileSize?: number;
    contentType?: string;
    dataVersion?: string;
    videoType?: string;
    createdAt?: string;
    updatedAt?: string;
    isLatest?: boolean;
}

/**
 * 获取最新视频响应
 */
export interface LatestVideoResponse {
    code: number;
    message: string;
    success: boolean;
    data?: {
        metadata: VideoMetadata;
        url: string;
        dataVersion: string;
        isLatest: boolean;
    };
}

/**
 * 数据版本检查响应
 */
export interface VersionCheckResponse {
    code: number;
    message: string;
    data?: {
        currentVersion: string;
    };
}
