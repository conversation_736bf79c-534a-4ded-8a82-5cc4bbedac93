<script setup lang="ts">
import {useRouter} from "vue-router";
const routeNew = useRouter();
const paramNew = new URLSearchParams(routeNew.currentRoute.value.query);
const loginName = paramNew.get('loginName'); // 从 URL 参数获取登录名

// 调用实际登录接口
if (loginName) {
  window.open('http://localhost:3000/#/login4a?loginName=' + loginName)
}

</script>

<template>

</template>

<style scoped lang="scss">

</style>
