<template>
	<el-card shadow="always" class="main-card">
		<el-row :gutter="24">
			<el-col :span="6" style="text-align: center;">
				<el-image
					class="card-image"
					:src="imageBase64Code"
					fit="fill"
				></el-image>
			</el-col>
			<el-col :span="18">
				<el-row :gutter="24">
					<el-col :span="24">
						<el-text
							class="mx-1 title"
							size="large"
							line-clamp="1"
							tag="b"
						>
							{{ title }}
						</el-text>
					</el-col>
				</el-row>
				<el-row :gutter="24" class="content">
					<el-col :span="24">
						<el-text
							class="mx-1"
							type="info"
							size="default"
							line-clamp="9"
						>
							{{ description }}
						</el-text>
					</el-col>
				</el-row>
				<el-row :gutter="24">
					<el-col :span="24">
						<el-button type="primary" @click="toOrderPage">{{ buttonTxt }}</el-button>
					</el-col>
				</el-row>
			</el-col>
		</el-row>
	</el-card>
</template>

<script setup lang="ts">

const router = useRouter();

const props = defineProps({
	imageBase64Code: {
		type: String,
		required: true,
		default: ''
	},
	title: {
		type: String,
		required: true,
		default: ''
	},
	productNumber: {
		type: String,
		required: true,
		default: ''
	},
	productClass: {
		type: String,
		required: true,
		default: ''
	},
	description: {
		type: String,
		required: false,
		default: ''
	},
});

const buttonTxt = ref('订购')
/**
 * 订购按钮跳转
 */
const toOrderPage = () => {
	router.push({
		path: '/product/porder',
		query: {
			productNumber: props.productNumber,
			productClass: props.productClass
		},
		state: {
			title: props.title
		}
	})
}

</script>


<style scoped lang="scss">
.main-card {
	--product-card-height: 300px;
	--height-diff: 100px;

	width: 100%;
	height: var(--product-card-height);

	.card-image {
		width: 60px;
		height: 60px;
	}

	.title {
		font-weight: bold;
		font-size: 18px;
	}

	.content {
		height: calc(var(--product-card-height) - var(--height-diff));
	}

	.description {
		font-size: 16px;
		color: #1b2a47;
	}

}

</style>