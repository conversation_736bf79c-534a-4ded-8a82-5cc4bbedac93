/* 声明字体*/
@font-face {
    font-family: DingTalk JinBuTi;
    src: url('./DingTalk_JinBuTi_Regular.ttf');
}

/*@font-face {
    font-family: PingFang SC;
    src: url('./PingFang.ttc');
}*/

@font-face {
    font-family: <PERSON><PERSON><PERSON>;
    src: url('./阿里妈妈数黑体.ttf');
}

@font-face {
    font-family: YouSheBiaoTiYuan;
    src: url('./优设标题圆.otf');
}

@font-face {
    font-family: Alibaba-PuHuiTi-Bold;
    src: url('./Alibaba-PuHuiTi-Bold.otf');
}

@font-face {
    font-family: Alibaba-PuHuiTi-Heavy;
    src: url('./Alibaba-PuHuiTi-Heavy.otf');
}

@font-face {
    font-family: Alibaba-PuHuiTi-Light;
    src: url('./Alibaba-PuHuiTi-Light.otf');
}

@font-face {
    font-family: Alibaba-PuHuiTi-Medium;
    src: url('./Alibaba-PuHuiTi-Medium.otf');
}

@font-face {
    font-family: PingFang SC;
    src: url('./Alibaba-PuHuiTi-Regular.otf');
}