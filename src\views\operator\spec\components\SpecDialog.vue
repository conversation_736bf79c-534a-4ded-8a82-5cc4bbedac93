<template>
	<div class="spec-dialog">
		<el-dialog
			v-model="dialogVisible"
			:title="title"
			top="10vh"
			width="80%"
			:close-on-click-modal="false"
			:close-on-press-escape="true"
			:before-close="handleClose"
			draggable
		>
			<el-container>
				<el-main>
					<el-card style="width: 100%;">
						<el-form
							:model="form"
							ref="formRef"
							label-width="120px"
							:rules="rules"
							:disabled="formDisabled"
						>
							<el-row :gutter="24">
								<el-col :span="12">
									<el-text
										class="mx-1"
										size="large"
										line-clamp="1"
										tag="b"
									>
										{{ baseInfoTitle }}
									</el-text>
								</el-col>
							</el-row>

							<el-row :gutter="24">
								<el-col :span="12">
									<el-form-item label="产品大类" prop="bigType">
										<el-select
											v-model="form.bigType"
											placeholder=""
											clearable
											@change="productClassChange"
											:disabled="opt === 'update'"
										>
											<el-option
												v-for="option in productClassOption"
												:key="option.value"
												:label="option.label"
												:value="option.value"
											/>
										</el-select>
									</el-form-item>
								</el-col>
							</el-row>

							<el-row :gutter="24" class="mt-10">
								<el-col :span="12">
									<el-text
										class="mx-1"
										size="large"
										line-clamp="1"
										tag="b"
									>
										规格属性
									</el-text>
								</el-col>
							</el-row>

							<el-row :gutter="24">
								<el-col :span="4">
									<el-button
										size="small"
										:icon="Plus"
										type="primary"
										@click="addData"
										class="w-full"
									>新增规格属性
									</el-button>
								</el-col>
							</el-row>
							<el-row :gutter="24" class="mt-3">
								<el-col :span="24">
									<el-table
										:data="form.tableData"
										border
										:header-cell-class-name="mustField"
										style="width: 100%"
									>
										<el-table-column type="index" label="序号" width="55"/>
										<el-table-column prop="name" label="属性名称" align="center"
										                 class="is-required">
											<template #default="scope">
												<el-select
													v-if="form.bigType === ProductClassEnum.GPU && !scope.row.attrNameInputVisible"
													v-model="scope.row.name"
													placeholder=""
													clearable
													@change="(value) => attrNameChange(scope.$index, value)"
												>
													<el-option
														v-for="option in attrNameOption"
														:key="option.value"
														:label="option.label"
														:value="option.value"
													/>
												</el-select>
												<el-input
													v-else-if="form.bigType !== ProductClassEnum.GPU || scope.row.attrNameInputVisible"
													v-model="scope.row.name"
													clearable
													placeholder="请输入属性名称"
												>
												</el-input>
											</template>
										</el-table-column>
										<el-table-column prop="isNeed" label="是否必填" class="is-required"
										                 align="center">
											<template #default="scope">
												<el-select
													v-model="scope.row.isNeed"
													placeholder="请选择"
													clearable
												>
													<el-option value="true" label="是"/>
													<el-option value="false" label="否"/>
												</el-select>
											</template>
										</el-table-column>
										<el-table-column prop="defaultValue" label="默认值" align="center">
											<template #default="scope">
												<el-input v-model="scope.row.defaultValue" clearable
												          placeholder="请输入默认值"></el-input>
											</template>
										</el-table-column>
										<el-table-column label="操作" align="center" width="200">
											<template #default="scope">
												<el-button link type='primary' @click="addData">新增</el-button>
												<el-button link type='primary' @click="deleteData(scope.$index)">
													删除
												</el-button>
												<el-button link type='primary' @click="upData(scope.$index)">
													上移
												</el-button>
												<el-button link type='primary' @click="downData(scope.$index)">
													下移
												</el-button>
											</template>
										</el-table-column>
									</el-table>
								</el-col>
							</el-row>
						</el-form>
					</el-card>
				</el-main>
			</el-container>
			<template #footer>
				<div style="text-align: center;">
					<el-button plain @click="back">取消</el-button>
					<el-button type="primary" @click="save(formRef)" v-if="opt !== 'view'">保存</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import {initDictMap} from "@/utils/commonUtils";
import {FormInstance, FormRules} from "element-plus";
import {Plus} from "@element-plus/icons-vue";
import {SpecForm, Specification, SpecificationAddReq, SpecificationQueryParams} from "@/api/spec/types";
import {defaultSpecification, specificationAdd, specificationEdit, specificationUpdate} from "@/api/spec";
import {ProductClassEnum} from "@/enums/ProductClassEnum";
import {OperationTypeEnum} from "@/enums/OperationTypeEnum";

const dialogVisible = ref<boolean>(false)
const title = ref<string>('规格详情')
const baseInfoTitle = ref<string>('基本信息')
const opt = ref<string>('')
const formDisabled = ref<boolean>(false)

const props = defineProps({
		productClassOption: {
			type: Array<any>,
			required: true,
			default: []
		}
	}
)

const attrNameOption = ref<Array<any>>([])
const attrNameChange = (index: number, value: number) => {
	if (value === 6) {
		form.tableData[index].name = ''
		form.tableData[index].attrNameInputVisible = true
	} else {
		form.tableData[index].attrNameInputVisible = false
	}
}

const form = reactive<SpecForm>({
	id: '',
	bigType: '',
	tableData: [],
});

const rules = reactive<FormRules<SpecForm>>({
	bigType: [
		{required: true, message: '请输入产品名称', trigger: 'blur'},
	],
})

const addData = () => {
	form.tableData?.push({})
}

const deleteData = (index: number) => {
	form.tableData?.splice(index, 1)
}

const upData = (index: number) => {
	if (index === 0) {
		ElMessage.warning('已经在最顶部位置')
		return
	}
	if (form.tableData && form.tableData.length > 0) {
		form.tableData[index] = form.tableData.splice(index - 1, 1, form.tableData[index])[0]
	}
}

const downData = (index: number) => {
	if (form.tableData && form.tableData.length > 0) {
		if (index === form.tableData.length - 1) {
			ElMessage.warning('已经在最底部位置')
			return
		}
		form.tableData[index] = form.tableData.splice(index + 1, 1, form.tableData[index])[0]
	}
}

const init = (optType: string, data: Specification) => {
	dialogVisible.value = true
	opt.value = optType
	switch (optType) {
		case OperationTypeEnum.ADD:
			title.value = '新增规格'
			formDisabled.value = false
			break
		case OperationTypeEnum.UPDATE:
			title.value = '编辑规格'
			formDisabled.value = false
			singleInfo(data)
			break
		case OperationTypeEnum.VIEW:
			formDisabled.value = true
			title.value = '规格详情'
			singleInfo(data)
			break
	}
}

const productClassChange = (value: string) => {
	form.tableData = []
}

// 表头必填的字段
const mustField = (obj: any) => {
	if (obj.column.property == 'name' ||
		obj.column.property == 'isNeed') {
		return 'mustField'
	}
}

const singleInfo = (data: any) => {
	let params: SpecificationQueryParams = {
		type: data.bigType
	}
	specificationEdit(params).then(response => {
		let respData = response.data;
		form.bigType = data.bigType
		form.id = respData.id
		form.tableData = respData.specificationEditResList
	})
}

const formRef = ref<FormInstance>()
const handleClose = (done: () => void) => {
	dialogVisible.value = false
	form.tableData = []
	resetForm(formRef.value)
	done()
}

const resetForm = (formEl: FormInstance | undefined) => {
	if (!formEl) return
	formEl.resetFields()
}

const back = () => {
	dialogVisible.value = false
	form.bigType = ''
	form.tableData = []
	resetForm(formRef.value)
}

let emits = defineEmits(['query']);
const save = async (formEl: FormInstance | undefined) => {
	if (!formEl) return
	await formEl.validate((valid, fields) => {
		if (valid) {
			let tableData = form.tableData;
			if (tableData && tableData.length > 0) {
				for (let i = 0; i < tableData.length; i++) {
					let tableDatum = tableData[i]
					if (!tableDatum.name) {
						ElMessage.warning(`请填写规格属性第${i + 1}行属性名称！`)
						return
					}
					if (!tableDatum.isNeed) {
						ElMessage.warning(`请选择规格属性第${i + 1}行是否必填！`)
						return
					}
				}
			} else {
				ElMessage.warning('请填写规格属性！')
				return
			}
			let spfJson = []
			tableData.forEach(row => {
				let temp = {}
				Object.keys(row).filter(key => key !== 'attrNameInputVisible')
					.forEach(key => {
						temp[key] = row[key]
					})
				spfJson.push(temp)
			})
			let params: SpecificationAddReq = {
				bigType: form.bigType,
				spfJson: spfJson,
			}
			let promise
			if (opt.value === OperationTypeEnum.ADD) {
				promise = specificationAdd(params);
			} else if (opt.value === OperationTypeEnum.UPDATE) {
				params.id = form.id
				promise = specificationUpdate(params)
			}
			if (promise) {
				promise.then((response: any) => {
					if (response.code === '00000') {
						ElMessage.success('保存成功！')
						back()
						emits('query')
					} else {
						ElMessage.error('保存失败！')
					}
				})
			}
		}
	})
}

const typeCodeMap = reactive<Map<string, any>>(new Map([
	["billMethod", ref<object>({})],
]));

const initAttrNameOption = () => {
	defaultSpecification().then((response: any) => {
		let data = response.data
		if (data && data.length > 0) {
			data.forEach(item => {
				attrNameOption.value.push({
					label: item.name,
					value: item.id
				})
			})
		}
	})
}

onMounted(() => {
	initDictMap(typeCodeMap)
	initAttrNameOption()
})

defineExpose({
	init
})

</script>

<style scoped lang="scss">

.spec-dialog {

	:deep(.el-table__header th.mustField > .cell:before) {
		content: '*';
		color: #ff1818;
		vertical-align: sub;
		margin-right: 5px;
	}
}

</style>