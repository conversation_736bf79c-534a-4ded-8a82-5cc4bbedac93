import request from "@/utils/request";
import {SpecificationAddReq, SpecificationQueryParams} from "@/api/spec/types";

export function specificationQuery(data: any) {
    return request.service({
        url: "/api/v1/sync/EBOSS/specificationQuery",
        method: "post",
        data: data,
    });
}

export function specificationAdd(data: SpecificationAddReq) {
    return request.service({
        url: "/api/v1/sync/EBOSS/specificationAdd",
        method: "post",
        data: data,
    });
}

export function specificationEdit(params: SpecificationQueryParams) {
    return request.service({
        url: "/api/v1/sync/EBOSS/specificationEdit",
        method: "get",
        params: params
    });
}

export function specificationUpdate(data: SpecificationAddReq) {
    return request.service({
        url: "/api/v1/sync/EBOSS/specificationUpdate",
        method: "post",
        data: data,
    });
}

/**
 * 规格产品大类枚举下拉选项
 */
export function categoriesEnumQuery() {
    return request.service({
        url: "/api/v1/sync/EBOSS/categoriesEnumQuery",
        method: "get",
    });
}

/**
 * 默认规格属性查询
 */
export function defaultSpecification() {
    return request.service({
        url: "/api/v1/sync/EBOSS/defaultSpecification",
        method: "get",
    });
}
