import {queryDictValueLabel} from "@/api/dict";

/**
 * 获取assets静态资源
 *
 * @param url 文件地址
 */
export function getAssetsFile(url: string) {
    return new URL(`../assets/${url}`, import.meta.url).href;
}

/**
 * 按指定大小切割数组
 *
 * @param arr 原数组
 * @param size 切割长度
 */
export function getNewArray(arr: Array<any>, size: number) {
    const arrNum: number = Math.ceil(arr.length / size)
    let index: number = 0 // 定义初始索引
    let resIndex: number = 0 // 用来保存每次拆分的长度
    const result: any[] = []
    while (index < arrNum) {
        result[index] = arr.slice(resIndex, size + resIndex)
        resIndex += size
        index++
    }
    return result
}

/**
 * 初始化字典值对应的显示名称
 *
 * @param typeCodeMap typeCode对应的值与键映射
 */
export function initDictMap(typeCodeMap: Map<string, any>) {
    let typeCodeIterator: IterableIterator<string> = typeCodeMap.keys()
    const typeCodes: string[] = Array.from(typeCodeIterator);
    queryDictValueLabel(typeCodes).then(response => {
            let data = response.data;
            if (data) {
                typeCodes.forEach(key => {
                    typeCodeMap.get(key).value = data[key]
                })
            }
        }
    )
}

/**
 * 获取数字位数
 *
 * @param number
 */
export function getNumberOfDigits(number: number): number {
    if (number) {
        const numberAsString = number.toString();
        return numberAsString.length;
    } else {
        return 0
    }
}

/**
 * 对象数组按照指定的Key排序
 *
 * @param array 数组
 * @param key 指定字段
 */
export function sortArrayByKey(array: Array<any>, key: string) {
    array?.sort((a, b) => {
        const rankA = (a[key] as number) || 0
        const rankB = (b[key] as number) || 0
        // 使用三元运算符来确保返回正确的排序顺序
        return rankB - rankA > 0 ? 1 : rankB - rankA < 0 ? -1 : 0;
    })
}

/**
 * 字符转换成number
 *
 * @param value 原始值
 */
export function stringToNumber(value: string | number | undefined): number {
    if (value) {
        if (typeof value === 'number') {
            return value
        } else {
            const trimmedValue: string = value.trim();
            const num: number = Number(trimmedValue);
            if (isNaN(num)) {
                return 0; // 如果转换失败，返回默认值0
            }
            return num; // 返回转换后的数字
        }
    } else {
        return 0;
    }
}

export function stringToNumberArray(array: Array<string>): Array<number> {
    if (!array) return [];
    return array?.map((item: string) => stringToNumber(item))
}