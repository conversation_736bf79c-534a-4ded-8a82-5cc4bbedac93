<template>
	<div>
		<el-dialog
			v-model="dialogVisible"
			:title="title"
			top="10vh"
			width="80%"
			:close-on-click-modal="false"
			:close-on-press-escape="true"
			:before-close="handleClose"
			draggable
		>
			<el-container>
				<el-main>
					<el-card style="width: 100%;">
						<el-form
							:model="form"
							ref="formRef"
							label-width="120px"
							:rules="rules"
							:disabled="formDisabled"
						>
							<el-row :gutter="24">
								<el-col :span="12">
									<el-text
										class="mx-1"
										size="large"
										line-clamp="1"
										tag="b"
									>
										{{ baseInfoTitle }}
									</el-text>
								</el-col>
							</el-row>

							<el-row :gutter="24">
								<el-col :span="18">
									<el-form-item label="大模型名称" prop="llmName">
										<el-input v-model="form.llmName" clearable></el-input>
									</el-form-item>
								</el-col>
							</el-row>

							<el-row :gutter="24">
								<el-col :span="18">
									<el-form-item label="大模型描述" prop="llmDesc">
										<el-input
											type="textarea"
											:rows="5"
											v-model="form.llmDesc"
											clearable
										></el-input>
									</el-form-item>
								</el-col>
							</el-row>

							<el-row :gutter="24">
								<el-col :span="18">
									<el-form-item label="大模型链接" prop="llmLink">
										<el-input
											v-model="form.llmLink"
											clearable
										></el-input>
									</el-form-item>
								</el-col>
							</el-row>
							<el-row :gutter="24">
								<el-col :span="18">
									<el-form-item label="排名" prop="llmOrder">
										<el-input-number
											v-model="form.llmOrder"
											clearable
										></el-input-number>
									</el-form-item>
								</el-col>
							</el-row>
						</el-form>
					</el-card>
				</el-main>
			</el-container>
			<template #footer>
				<div style="text-align: center;">
					<el-button plain @click="back">取消</el-button>
					<el-button type="primary" @click="save(formRef)" v-if="opt !== 'view'">保存</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import {initDictMap} from "@/utils/commonUtils";
import {FormInstance, FormRules} from "element-plus";
import {LargeModel, LargeModelForm} from "@/api/model/types";
import {getLargeModelById, llmInfoAdd, llmInfoUpdate} from "@/api/model";
import {OperationTypeEnum} from "@/enums/OperationTypeEnum";

const dialogVisible = ref<boolean>(false)
const title = ref<string>('规格详情')
const baseInfoTitle = ref<string>('基本信息')
const opt = ref<string>('')
const formDisabled = ref<boolean>(false)


const form = reactive<LargeModelForm>({
	llmName: '',
	llmDesc: '',
	llmLink: '',
	llmOrder: 1,
});

const rules = reactive<FormRules<LargeModelForm>>({
	llmName: [
		{required: true, message: '请输入名称', trigger: 'blur'},
	],
	llmDesc: [
		{required: true, message: '请输入描述', trigger: 'blur'},
	],
	llmLink: [
		{required: true, message: '请输入链接', trigger: 'blur'},
	],
})

const init = (optType: string, data: any) => {
	dialogVisible.value = true
	opt.value = optType
	switch (optType) {
		case OperationTypeEnum.ADD:
			title.value = '新增大模型'
			formDisabled.value = false
			break
		case OperationTypeEnum.UPDATE:
			title.value = '编辑大模型'
			formDisabled.value = false
			singleInfo(data)
			break
		case OperationTypeEnum.VIEW:
			formDisabled.value = true
			title.value = '大模型详情'
			singleInfo(data)
			break
	}
}


const singleInfo = (data: LargeModel) => {
	getLargeModelById(data.id).then(response => {
		let respData = response.data
		if(respData){
			form.id = respData.id
			form.llmName = respData.llmName
			form.llmDesc = respData.llmDesc
			form.llmLink = respData.llmLink
			form.llmOrder = respData.llmOrder
		}
	})
}

const formRef = ref<FormInstance>()
const handleClose = (done: () => void) => {
	dialogVisible.value = false
	resetForm(formRef.value)
	done()
}

const resetForm = (formEl: FormInstance | undefined) => {
	if (!formEl) return
	formEl.resetFields()
}


const back = () => {
	dialogVisible.value = false
	resetForm(formRef.value)
}

const save = async (formEl: FormInstance | undefined) => {
	if (!formEl) return
	await formEl.validate((valid, fields) => {
		if (valid) {
			let params: LargeModel = {...form}
			if (opt.value === OperationTypeEnum.ADD) {
				handleSave(llmInfoAdd, params)
			} else if (opt.value === OperationTypeEnum.UPDATE) {
				handleSave(llmInfoUpdate, params)
			}
		}
	})
}

let emits = defineEmits(['query']);
const handleSave = (func: typeof Function, params: LargeModel) => {
	func(params).then(response => {
		if (response.code === '00000') {
			ElMessage.success('保存成功！')
			dialogVisible.value = false
			resetForm(formRef.value)
			emits('query')
		} else {
			ElMessage.error('保存失败！')
		}
	}).catch(() => {
		ElMessage.error('保存失败！')
	})
}

const typeCodeMap = reactive<Map<string, any>>(new Map([
	["billMethod", ref<object>({})],
]));

onMounted(() => {
	initDictMap(typeCodeMap)
})

defineExpose({
	init
})

</script>

<style scoped lang="scss">

</style>