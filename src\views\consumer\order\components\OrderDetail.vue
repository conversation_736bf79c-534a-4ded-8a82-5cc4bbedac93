<template>

	<el-dialog
		v-model="dialogVisible"
		:title="title"
		width="80%"
		top="10vh"
		:close-on-click-modal="false"
		:close-on-press-escape="true"
		:before-close="handleClose"
		draggable
	>
		<el-container>
			<el-main>
				<el-card style="width: 100%;">
					<template #header>
						<el-text
							class="mx-1"
							size="large"
							line-clamp="1"
							tag="b"
						>
							订单概况
						</el-text>
					</template>
					<el-form :model="form" ref="formRef" label-width="auto" :disabled="true">
						<el-row :gutter="24">
							<el-col :span="12">
								<el-form-item label="订单编号" prop="productOrderNumber">
									<el-input v-model="form.productOrderNumber"></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="订单创建时间" prop="prodordTime">
									<el-input v-model="form.prodordTime"></el-input>
								</el-form-item>
							</el-col>
						</el-row>
						<el-row :gutter="24">
							<el-col :span="12">
								<el-form-item label="订单批次号" prop="batchNumber">
									<el-input v-model="form.batchNumber"></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="类型" prop="operationSubType">
									<dictionary
										:disabled="true"
										v-model="form.operationSubType"
										type-code="orderType"
									/>
								</el-form-item>
							</el-col>
						</el-row>
					</el-form>
				</el-card>

				<el-card style="width: 100%;margin-top: 15px;">
					<template #header>
						<el-text
							class="mx-1"
							size="large"
							line-clamp="1"
							tag="b"
						>
							订单详情
						</el-text>
					</template>

					<el-table
						:data="tableData"
						border
						style="width: 100%"
					>
						<el-table-column prop="productOrderNumber" label="订单编号" align="center" width="175"/>
						<el-table-column prop="productName" label="产品名称" align="center"/>
						<el-table-column prop="subsId" label="订购关系ID" align="center" width="140"/>
						<el-table-column prop="specName" label="规格" align="center">
							<template #default="scope">
								{{scope.row.specName}}<br/>
								{{scope.row.specInfo}}
							</template>
						</el-table-column>
						<el-table-column prop="prodordStatus" label="订单状态" align="center" :formatter="statusFormatter"/>
						<el-table-column prop="time" label="开通时间/到期时间" align="center"/>
						<el-table-column prop="expenses" label="资费套餐" align="center"/>
					</el-table>

				</el-card>
			</el-main>
		</el-container>
		<template #footer>
			<div style="text-align: center;">
				<el-button type="primary" @click="back">返回</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">

import {orderDetailQuery} from "@/api/order";
import {OrderFormData} from "@/api/order/types";

const props = defineProps({
	typeCodeMap: {
		type: Map<string, any>,
		default: () => {
			return new Map();
		},
	},
})

const dialogVisible = ref<boolean>(false)
const title = ref<string>('订单详情')
const tableData = ref([])
const opt = ref<string>('')

const form = reactive<OrderFormData>({
	productOrderNumber: '',
	operationSubType: null,
	batchNumber: '',
	prodordTime: '',
});

const statusFormatter = (row: any, column: any, cellValue: any, index: number) => {
	return props.typeCodeMap.get('orderStatus').value[cellValue]
}

const init = (optType: string, data: any) => {
	dialogVisible.value = true
	opt.value = optType
	switch (optType) {
		case 'view':
			singleInfo(data)
			break
	}
}

const singleInfo = (data?: Object) => {
	let params = {
		productOrderNumber: data.productOrderNumber
	}
	orderDetailQuery(params).then(response => {
		let productOrderDetail = response.data.productOrderDetail
		if (productOrderDetail) {
			form.productOrderNumber = productOrderDetail.productOrderNumber
			form.operationSubType = productOrderDetail.operationSubType
			form.batchNumber = productOrderDetail.orderNumber
			form.prodordTime = productOrderDetail.prodordTime
			let obj = {
				productOrderNumber: productOrderDetail.productOrderNumber,
				productName: productOrderDetail.productName,
				subsId: productOrderDetail.subsId,
				prodordStatus: productOrderDetail.prodordStatus,
				specName: productOrderDetail.productSpec[0]?.specName,
				specInfo: productOrderDetail.productSpec[0]?.specInfo,
				time: productOrderDetail.effTime + '/' + productOrderDetail.expTime,
				expenses: productOrderDetail.productOrderRatePlan[0]?.ratePriceAndDis
			}
			tableData.value.push(obj)
		}
	})
}

const handleClose = (done: () => void) => {
	dialogVisible.value = false
	tableData.value = []
	done()
}

const back = () => {
	dialogVisible.value = false
	tableData.value = []
}

defineExpose({
	init
})

</script>

<style scoped lang="scss">

</style>