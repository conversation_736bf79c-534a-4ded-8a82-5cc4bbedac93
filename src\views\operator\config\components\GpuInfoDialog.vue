<template>
	<el-dialog
		v-model="dialogVisible"
		:title="title"
		top="10vh"
		width="80%"
		:close-on-click-modal="false"
		:close-on-press-escape="true"
		:before-close="handleClose"
		draggable
	>
		<el-card style="width: 100%;">
			<el-form
				:model="form"
				ref="formRef"
				label-width="120px"
				:rules="rules"
				:disabled="formDisabled"
			>
				<el-row :gutter="24">
					<el-col :span="12">
						<el-text
							class="mx-1"
							size="large"
							line-clamp="1"
							tag="b"
						>
							规格信息
						</el-text>
					</el-col>
				</el-row>

				<el-row :gutter="24">
					<el-col :span="24">
						<el-form-item label="规格名称" prop="spfName">
							<el-input v-model="form.spfName" clearable placeholder="请输入规格名称"></el-input>
						</el-form-item>
					</el-col>
				</el-row>

				<el-row :gutter="24">
					<el-col :span="12" v-for="item in spfList">
						<el-form-item
							:label="item.name"
							:prop="item.id"
							:rules="[{required: item.isNeed, message: `请输入${item.name}`, trigger: 'blur'},]"
						>
							<el-input
								v-model="form[item.id]"
								:placeholder="`请输入${item.name}`"
								clearable
							></el-input>
						</el-form-item>
					</el-col>
				</el-row>

				<el-row :gutter="24">
					<el-col :span="12">
						<el-text
							class="mx-1"
							size="large"
							line-clamp="1"
							tag="b"
						>
							资费信息
						</el-text>
					</el-col>
				</el-row>

				<expense-form
					:form="form"
				></expense-form>
			</el-form>
		</el-card>
		<template #footer>
			<div style="text-align: center;">
				<el-button plain @click="cancel">取消</el-button>
				<el-button type="primary" @click="save(formRef)" v-if="opt !== 'view'">保存</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import {FormInstance, FormRules} from "element-plus";
import {SpecObj, SpfObj} from "@/api/product/types";
import ExpenseForm from "@/views/operator/config/components/ExpenseForm.vue";
import {OperationTypeEnum} from "@/enums/OperationTypeEnum";

const dialogVisible = ref<boolean>(false)
const title = ref<string>('新增规格')
const formDisabled = ref<boolean>(false)

//const props = defineProps({})

const formRef = ref<FormInstance>()

const form = ref<SpecObj>(<SpecObj>{});

const rules = reactive<FormRules<SpecObj>>({
	spfName: [
		{required: true, message: '请输入规格名称', trigger: 'blur'},
	],
	ratePlanName: [
		{required: true, message: '请输入资费名称', trigger: 'blur'},
	],
	ratePlanType: [
		{required: true, message: '请选择', trigger: 'blur'},
	],
	price: [
		{required: true, message: '请输入目录价', trigger: 'blur'},
		{
			pattern: /^\d+(\.\d+)?$/,
			message: '请填写整数',
			trigger: "blur",
		},
	],
	discount: [
		{required: false, message: '请填写折扣', trigger: 'blur'},
		{
			pattern: /^(?:100(?:\.0{1,2})?|(?:[1-9]?[0-9])(?:\.[0-9]{1,2})?|0\.\d{1,2})$/,
			message: '请填写0～100数字，可保留两位小数',
			trigger: "blur",
		},
	],
})

const opt = ref<string>('')
// 编辑当前数组索引
const currentIndex = ref<number>(0)

const init = (optType: string, formFieldData: any, data: any, index: number) => {
	dialogVisible.value = true
	opt.value = optType
	resetForm(formRef.value)
	switch (optType) {
		case OperationTypeEnum.ADD:
			title.value = '新增规格'
			formDisabled.value = false
			initFormFieldInfo(formFieldData)
			break
		case OperationTypeEnum.UPDATE:
			currentIndex.value = index
			title.value = '修改规格'
			formDisabled.value = false
			initFormFieldInfo(formFieldData)
			singleInfo(data)
			break
	}
}

const spfList = ref<Array<SpfObj>>()
const initFormFieldInfo = (spfArray: Array<any>) => {
	if (opt.value === OperationTypeEnum.ADD) {
		spfArray.forEach(item => {
			form.value[item.id] = item.defaultValue
		})
	}
	spfList.value = spfArray
}

const singleInfo = (data: any) => {
	form.value = data
}

const cancel = () => {
	dialogVisible.value = false
}

let emits = defineEmits(['addSpecArrayData', 'updateSpecArrayData']);

const save = async (formEl: FormInstance | undefined) => {
	if (!formEl) return
	await formEl.validate((valid, fields) => {
		if (valid) {
			if (opt.value === OperationTypeEnum.ADD) {
				emits('addSpecArrayData', form.value, spfList.value)
			} else if (opt.value === OperationTypeEnum.UPDATE) {
				emits('updateSpecArrayData', form.value, spfList.value, currentIndex.value)
			}
			cancel()
		}
	})
}

const resetForm = (formEl: FormInstance | undefined) => {
	if (!formEl) return
	formEl.resetFields()
}

const handleClose = (done: () => void) => {
	resetForm(formRef.value)
	dialogVisible.value = false
	done()
}

defineExpose({
	init
})

</script>


<style scoped lang="scss">

</style>