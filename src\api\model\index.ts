import request from "@/utils/request";
import {LargeModel} from "@/api/model/types";

/**
 * 查询大模型列表
 */
export function queryLargeModelList() {
    return request.service({
        url: "/api/v1/manage/llmInfo",
        method: "get",
    });
}

/**
 * 根据ID获取单条数据
 * @param id
 */
export function getLargeModelById(id: string) {
    return request.service({
        url: `/api/v1/manage/getLargeModelById/${id}`,
        method: "get",
    });
}

export function llmInfoAdd(params: LargeModel) {
    return request.service({
        url: "/api/v1/manage/llmInfoAdd",
        method: "post",
        data: params,
    });
}

export function llmInfoUpdate(params: LargeModel) {
    return request.service({
        url: "/api/v1/manage/llmInfoUpdate",
        method: "put",
        data: params,
    });
}