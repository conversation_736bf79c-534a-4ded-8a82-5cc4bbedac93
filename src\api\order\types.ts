/**
 * 查询栏数据
 */
export interface OrderListQuery {
    productOrderNumber?: string,
    customerNumber?: string,
    prodordStatus?: number | null,
    productName?: string,
    createTime?: Date[] | undefined,
}

/**
 * 查询订单接口请求参数模型
 */
export interface QueryData extends OrderListQuery {
    prodOrdTimeBegin?: string,
    prodOrdTimeEnd?: string,
    pageNum: number,
    pageSize: number,
}

/**
 * 订单单条表单数据
 */
export interface OrderFormData {
    productOrderNumber?: string,
    operationSubType?: number | null,
    batchNumber?: string,
    prodordTime?: string,
}