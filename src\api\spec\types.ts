export interface SpecForm {
    id?: string,
    bigType?: string,
    tableData?: Array<SpAddReq>,
}

export interface Specification {
    bigType?: string,
    specInfo?: string,
    updateTime?: Date,
}

export interface SpecificationAddReq {
    id?: string,
    bigType?: string,
    spfJson?: Array<SpAddReq>
}

export interface SpAddReq {
    id?: number,
    sort?: number,
    name?: string,
    isNeed?: string,
    defaultValue?: string,
}

export interface SpecificationQueryParams {
    type?: string,
}