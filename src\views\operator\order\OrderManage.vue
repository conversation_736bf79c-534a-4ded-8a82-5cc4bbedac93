<template>
	<div>
		<el-card style="width: 100%;">
			<template #header>
				<el-text
					class="mx-1"
					size="large"
					line-clamp="1"
					tag="b"
				>
					{{ orderTitle }}
				</el-text>
			</template>

			<el-container>
				<el-header style="height: 80px;">
					<el-form :model="queryForm" ref="queryFormRef" label-width="auto">
						<el-row :gutter="24">
							<el-col :span="6">
								<el-form-item label="订单编号" prop="productOrderNumber">
									<el-input v-model="queryForm.productOrderNumber" placeholder="请输入订单编号"
									          clearable></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="6">
								<el-form-item label="订单状态" prop="prodordStatus">
									<dictionary v-model="queryForm.prodordStatus" type-code="orderStatus"/>
								</el-form-item>
							</el-col>
							<el-col :span="6">
								<el-form-item label="产品名称" prop="productName">
									<el-input
										v-model="queryForm.productName"
										placeholder="请输入产品名称"
										clearable
									></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="6">
								<el-form-item label="客户编码" prop="customerNumber">
									<el-input
										v-model="queryForm.customerNumber"
										placeholder="请输入客户编码"
										clearable
									></el-input>
								</el-form-item>
							</el-col>
						</el-row>
						<el-row :gutter="24">
							<el-col :span="12">
								<el-form-item label="订单创建时间" prop="createTime">
									<el-date-picker
										v-model="queryForm.createTime"
										type="daterange"
										range-separator="至"
										start-placeholder="请选择开始时间"
										end-placeholder="请选择结束时间"
										size="default"
										clearable
									/>
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<div style="float: right;">
									<el-button type="primary" @click="query(1)">查询</el-button>
									<el-button plain @click="resetForm(queryFormRef)">重置</el-button>
								</div>
							</el-col>
						</el-row>
					</el-form>
				</el-header>
				<el-main>
					<el-table
						v-loading="loading"
						:data="tableData"
						border
						height="450"
						style="width: 100%"
					>
						<el-table-column prop="productOrderNumber" label="订单编号" align="center" width="200"/>
						<el-table-column prop="productName" label="产品名称" align="center"/>
						<el-table-column prop="subsId" label="订购关系ID" align="center"/>
						<el-table-column prop="customerNumber" label="客户编码" align="center" width="190"/>
						<el-table-column prop="customerName" label="客户名称" align="center"/>
						<el-table-column prop="operationSubType" label="类型" align="center" :formatter="typeFormatter"/>
						<el-table-column prop="prodordTime" label="订单创建时间" align="center" width="200" />
						<el-table-column prop="prodordStatus" label="状态" align="center">
							<template #default="scope">
								<el-tag :type="typeCodeMap.get('orderStatusColor').value[scope.row.prodordStatus]">
									{{ typeCodeMap.get('orderStatus').value[scope.row.prodordStatus] }}
								</el-tag>
							</template>
						</el-table-column>
						<el-table-column label="操作" align="center">
							<template #default="scope">
								<el-button link type='primary' @click="detail(scope.row)">
									{{ detailText }}
								</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-main>
				<el-footer>
					<el-pagination
						class="order-page"
						v-model:current-page="current"
						v-model:page-size="size"
						:page-sizes="[10, 50, 100, 200]"
						:small="false"
						:background="true"
						size="default"
						layout="total, sizes, prev, pager, next, jumper"
						:total="total"
						@size-change="handleSizeChange"
						@current-change="handleCurrentChange"
					/>
				</el-footer>
			</el-container>

		</el-card>
		<order-detail
			ref="orderDetailRef"
			:typeCodeMap="typeCodeMap"
		>
		</order-detail>
	</div>
</template>

<script setup lang="ts">
import {FormInstance} from "element-plus";
import OrderDetail from "@/views/operator/order/components/OrderDetail.vue";
import {orderListQuery} from "@/api/order";
import moment from "moment/moment";
import {OrderListQuery, QueryData} from "@/api/order/types";
import {initDictMap} from "@/utils/commonUtils";
import {OperationTypeEnum} from "@/enums/OperationTypeEnum";

const orderTitle = ref('订单管理')
const detailText = ref('详情')
const orderDetailRef = ref()

const queryForm = reactive<OrderListQuery>({
	productOrderNumber: '',
	customerNumber: '',
	prodordStatus: null,
	productName: '',
	createTime: [],
});

const tableData = ref([]);

const loading = ref(false)
const current = ref<number>(1)
const size = ref<number>(10)
const total = ref<number>(0)

const handleSizeChange = (val: number) => {
	size.value = val
	query()
}
const handleCurrentChange = (val: number) => {
	current.value = val
	query(val)
}

const detail = (rowData: object) => {
	orderDetailRef.value.init(OperationTypeEnum.VIEW, rowData)
}

const typeFormatter = (row: any, column: any, cellValue: any, index: number) => {
	return typeCodeMap.get('orderType').value[cellValue]
}



const query = (index?: number) => {
	if (index) {
		current.value = index
	}
	let data: QueryData = {
		...queryForm,
		pageNum: current.value,
		pageSize: size.value
	}
	delete data.createTime
	let createTime = queryForm.createTime;
	if (createTime && createTime.length === 2) {
		data.prodOrdTimeBegin = moment(createTime[0]).format('YYYY-MM-DD')
		data.prodOrdTimeEnd = moment(createTime[1]).format('YYYY-MM-DD')
	}
	loading.value = true
	orderListQuery(data).then(response => {
		let pageData = response.data;
		if (pageData) {
			tableData.value = pageData.list
			total.value = pageData.total
		}
	}).finally(() => {
		loading.value = false
	})
}
const queryFormRef = ref<FormInstance>()

const resetForm = (formEl: FormInstance | undefined) => {
	if (!formEl) {
		return
	}
	formEl.resetFields()
}

const typeCodeMap = reactive<Map<string, any>>(new Map([
	["orderType", ref<object>({})],
	["orderStatus", ref<object>({})],
	["orderStatusColor", ref<object>({})],
]));

onMounted(() => {
	query()
	initDictMap(typeCodeMap)
})

</script>

<style scoped lang="scss">
.order-page {
	float: right;
	line-height: 60px;
}

</style>