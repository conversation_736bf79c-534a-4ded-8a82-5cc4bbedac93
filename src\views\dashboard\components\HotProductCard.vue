<template>
	<el-card shadow="never" style="cursor: pointer;height:100%" class="hot-prod main-card">
		<template #header>
			<div class="flex items-center justify-center">
				<span class="text-[var(--el-color-black)]">{{ title }}</span>
			</div>
		</template>

		<div class="h-full">
			<div style="height: calc(100% - 30px);" class="flex items-center justify-center">
				<div class="text-sm text-center h-full text-[var(--el-text-color-secondary)]">
					<div class="h-[80px]">
						<!--<svg-icon :icon-class="iconClass" color="#ff8000" size="6em"/>-->
						<el-image
							class="card-image h-[80px] w-[80px] p-[10px]"
							:src="imageBase64Code"
							fit="fill"
						></el-image>
					</div>
					{{ description }}
				</div>
			</div>

			<div style="height: 30px;"
			     class="flex items-center justify-center text-sm text-[var(--el-text-color-secondary)] m-[10px]"
			>
				<span class="price">¥{{ price }}</span>/<span>{{ cycleType }}</span>
			</div>
		</div>
	</el-card>
</template>

<script setup lang="ts">

const props = defineProps({
	iconClass: {
		type: String,
		required: false,
		default: ''
	},
	imageBase64Code: {
		type: String,
		required: true,
		default: ''
	},
	title: {
		type: String,
		required: true,
		default: ''
	},
	description: {
		type: String,
		required: false,
		default: ''
	},
	price: {
		type: String,
		required: false,
		default: '0'
	},
	cycleType: {
		type: String,
		required: false,
		default: ''
	},
});

</script>

<style scoped lang="scss">
.main-card {
	:deep(.el-card__body) {
		height: calc(100% - 61px);
	}
}

.hot-prod {
	background-image: url("../../../assets/images/hot-left.png");
	background-size: 18%;
	background-repeat: no-repeat;
}

.price {
	font-size: x-large;
	color: red;
}

</style>