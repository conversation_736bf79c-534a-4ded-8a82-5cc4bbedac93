import request from "@/utils/request";
import { AxiosPromise } from "axios";
import { regUserForm } from "./types";


/**
 * 添加用户
 *
 * @param data
 */
export function addUser(data: any) {
  return request.service({
    url: "/api/v1/users/register",
    method: "post",
    data: data,
  });
}


/**
 * 修改用户密码
 *
 * @param id
 * @param password
 */
export function updateUserPassword(id: number, password: string) {
  return request.service({
    url: "/api/v1/users/" + id + "/password",
    method: "patch",
    params: { password: password },
  });
}
