<template>
	<div class="register-page">
		<el-container>
			<el-header>
				<div class="mt-5">
					<el-image :src="getAssetsFile('images/logo.png')"></el-image>
					<el-text
						class="mx-1 header-title"
						type="primary"
						size="large"
						line-clamp="1"
						tag="b"
					>
						| {{ defaultSettings.title }}
					</el-text>
				</div>
			</el-header>
			<el-main>
				<el-card class="form-card">
					<el-row :gutter="24">
						<el-col :span="8">
							<div class="left-image-blank">
								<el-image
									class="h-full"
									:src="getAssetsFile('images/bg_registerV.jpg')"
									fit="fill"
								></el-image>
								<span class="img-title">{{ title }}</span>
								<span class="img-sub-title">{{ subtitle }}</span>
							</div>
						</el-col>
						<el-col :span="16">
							<el-form
								ref="ruleFormRef"
								:model="ruleForm"
								:rules="rules"
								label-width="auto"
								class="demo-ruleForm"
								size="default"
								status-icon
								label-position="left"
							>
								<el-form-item label="账号" prop="username">
									<el-input
										v-model="ruleForm.username"
										placeholder="账号（2-32个字符，仅支持字母、数字、下划线、中划线）"
										clearable
									/>
								</el-form-item>

								<el-form-item label="登录密码" prop="password">
									<el-input
										type="password"
										show-password
										v-model="ruleForm.password"
										placeholder="登录密码（8-16位字符，同时包含数字、大小写字母、至少一个特殊字符）"
										clearable
									/>
								</el-form-item>

								<el-form-item label="确认密码" prop="confirmPassword">
									<el-input
										type="password"
										show-password
										v-model="ruleForm.confirmPassword"
										placeholder="确认密码"
										clearable
									/>
								</el-form-item>

								<el-divider content-position="left">{{ certificateUploadTxt }}</el-divider>

								<el-form-item label="企业营业执照" prop="license" class="is-required">
									<el-upload
										ref="uploadRef"
										class="avatar-uploader"
										v-model:file-list="fileList"
										action="#"
										:auto-upload="false"
										:on-remove="handleRemove"
										:on-change="handleChange"
										:accept="acceptType"
										:limit="1"
									>
										<img v-if="imageUrl" :src="imageUrl" class="avatar" style="width: 500px;"/>
										<el-icon v-else class="avatar-uploader-icon">
											<Plus/>
										</el-icon>
										<template #tip>
											<div class="el-upload__tip">
												请提供证件的原件照片或彩色扫描件（正副本均可），文字/盖章清晰可辨认
											</div>
										</template>
									</el-upload>
								</el-form-item>


								<el-divider content-position="left">{{ informationTxt }}</el-divider>

								<el-form-item label="统一社会信用代码" prop="creditCode">
									<el-input
										v-model="ruleForm.creditCode"
										placeholder="统一社会信用代码"
										clearable
									/>
								</el-form-item>

								<el-form-item label="企业名称" prop="enterpriseName">
									<el-input
										v-model="ruleForm.enterpriseName"
										placeholder="企业名称"
										clearable
									/>
								</el-form-item>

								<el-form-item label="企业类型" prop="enterpriseType">
									<el-select
										v-model="ruleForm.enterpriseType"
										placeholder="请选择"
										class="w-full"
										clearable
									>
										<el-option
											v-for="item in enterpriseTypeOption"
											:key="item.value"
											:label="item.label"
											:value="item.value"
										></el-option>
									</el-select>
								</el-form-item>

								<el-form-item label="法人代表姓名" prop="legalRepresentative">
									<el-input
										v-model="ruleForm.legalRepresentative"
										placeholder="法人代表姓名"
										clearable
									/>
								</el-form-item>

								<el-form-item label="法人代表身份证" prop="legalRepresentativeId">
									<el-input
										v-model="ruleForm.legalRepresentativeId"
										placeholder="法人代表身份证"
										clearable
									/>
								</el-form-item>

								<el-form-item label="联系人姓名" prop="liaison">
									<el-input
										v-model="ruleForm.liaison"
										placeholder="联系人姓名"
										clearable
									/>
								</el-form-item>
								<el-form-item label="联系人电话" prop="liaisonPhone">
									<el-input
										v-model="ruleForm.liaisonPhone"
										placeholder="联系人电话"
										clearable
									/>
								</el-form-item>
								<el-form-item label="联系人邮箱" prop="liaisonEmail">
									<el-input
										v-model="ruleForm.liaisonEmail"
										placeholder="联系人邮箱"
										clearable
									/>
								</el-form-item>

								<el-divider content-position="left">{{ businessTxt }}</el-divider>

								<el-form-item label="开户名" prop="accountName">
									<el-input
										v-model="ruleForm.accountName"
										placeholder="开户名（一般与企业名称一致）"
										clearable
									/>
								</el-form-item>

								<el-form-item label="税号" prop="taxNumber">
									<el-input
										v-model="ruleForm.taxNumber"
										placeholder="税号"
										clearable
									/>
								</el-form-item>

								<el-form-item label="单位地址" prop="address">
									<el-input
										v-model="ruleForm.address"
										placeholder="单位地址"
										clearable
									/>
								</el-form-item>

								<el-form-item label="开户银行" prop="accountBank">
									<el-input
										v-model="ruleForm.accountBank"
										placeholder="开户银行"
										clearable
									/>
								</el-form-item>

								<el-form-item label="银行账号" prop="bankAccount">
									<el-input
										v-model="ruleForm.bankAccount"
										placeholder="银行账号"
										clearable
									/>
								</el-form-item>

								<el-row :gutter="24">
									<el-col :offset="4" :span="8">
										<el-button style="width: 100%;" type="primary" @click="submitForm(ruleFormRef)">
											注册
										</el-button>
									</el-col>
									<el-col :span="8">
										<el-button style="width: 100%;" @click="resetForm(ruleFormRef)">重置</el-button>
									</el-col>
								</el-row>
								<el-row :gutter="24" class="mt-5">
									<el-col :span="3">
										<el-button link type="primary" @click="toPortal">
											{{ backPortalTxt }}
										</el-button>
									</el-col>
									<el-col :offset="14" :span="3">
										<span style="color: #A3A6AD;font-size: 14px;">{{ hasAccountTxt }}</span>
									</el-col>
									<el-col :span="3">
										<el-button link type="primary" @click="toLogin">{{ loginTxt }}</el-button>
									</el-col>
								</el-row>
							</el-form>
						</el-col>
					</el-row>
				</el-card>
				<div class="text-center mt-5">
					<el-text
						type="info"
						class="mx-1"
						size="default"
						line-clamp="1"
						tag="b"
					>
						{{ copyright }}
					</el-text>
				</div>
			</el-main>
		</el-container>
	</div>

</template>

<script setup lang="ts">
import {getAssetsFile} from "@/utils/commonUtils";
import defaultSettings from "@/settings";
import {FormInstance, FormRules, UploadProps, UploadUserFile} from "element-plus";
import {RegisterInfo} from "@/api/user/types";
import {useRouter} from "vue-router";
import {Plus} from '@element-plus/icons-vue'
import {registerUser} from "@/api/user";

const router = useRouter();

const certificateUploadTxt = ref<string>('证件上传')
const informationTxt = ref<string>('企业信息')
const businessTxt = ref<string>('业务收付款')

const title = ref<string>('政企业务风险防控信息化系统')
const subtitle = ref<string>('提供普惠可信的算力、网络、安全服务')
const backPortalTxt = ref<string>('返回门户')
const hasAccountTxt = ref<string>('已有账号？')
const loginTxt = ref<string>('立即登录')
const copyright = ref<string>('Copyright © 中国移动, All Rights Reserved.')

const acceptType = ref<string>('.jpg,.png,.jpeg')

const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<RegisterInfo>({})

const fileList = ref<UploadUserFile[]>([])

const enterpriseTypeOption = reactive<any>([
	{
		label: '国有企业',
		value: 1,
	}, {
		label: '外资企业',
		value: 2,
	}, {
		label: '合资企业',
		value: 3,
	}, {
		label: '民营企业',
		value: 4,
	},
])

const validateConfirmPassword = (rule: any, value: any, callback: any) => {
	if (value === '') {
		callback(new Error('请输入确认密码, 密码长度1～16'))
	} else {
		if (ruleForm.password !== value) {
			callback(new Error('请输入确认密码需和密码保持一致！'))
		} else {
			callback()
		}
	}
}

const validateLicense = (rule: any, value: any, callback: any) => {
	if (fileList.value && fileList.value.length === 0) {
		callback(new Error('请上传企业营业执照'))
	} else {
		callback()
	}
}

const validateBankAccount = (rule: any, value: any, callback: any) => {
	if (/^\d{1,19}$/.test(value)) {
		callback()
	} else {
		callback(new Error('请输入正确的银行账号长度1～19'))
	}
}

const rules = reactive<FormRules<RegisterInfo>>({
	username: [
		{required: true, message: '请输入账号，注册成功后，账号不可修改', trigger: 'blur'},
		{min: 2, max: 32, message: '账号长度2～32', trigger: 'blur'},
		{
			pattern: /^[a-zA-Z0-9_-]{2,32}$/,
			message: '账号（2-32个字符，仅支持字母、数字、下划线、中划线）',
			trigger: "blur",
		},
	],
	password: [
		{required: true, message: '请输入密码', trigger: 'blur'},
		{min: 8, max: 16, message: '密码长度8～16', trigger: 'blur'},
		{
			pattern: /^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[^a-zA-Z0-9]).{8,16}$/,
			message: '登录密码（8-16位字符，同时包含数字、大小写字母、至少一个特殊字符）',
			trigger: "blur",
		},
	],
	confirmPassword: [
		{required: true, message: '请输入确认密码', trigger: 'blur'},
		{min: 8, max: 16, message: '密码长度8～16', trigger: 'blur'},
		{validator: validateConfirmPassword, trigger: 'blur'}
	],
	license: [
		{validator: validateLicense, trigger: 'blur'}
	],
	creditCode: [
		{required: true, message: '请输入统一社会信用代码', trigger: 'blur'},
		{
			pattern: /^.{18}$/,
			message: '统一社会信用代码长度18',
			trigger: "blur",
		},
	],
	enterpriseName: [
		{required: true, message: '请输入企业名称', trigger: 'blur'},
	],
	enterpriseType: [
		{required: true, message: '请选择', trigger: 'blur'},
	],
	legalRepresentative: [
		{required: true, message: '请输入法人代表姓名', trigger: 'blur'},
	],
	legalRepresentativeId: [
		{required: true, message: '请输入法人代表身份证', trigger: 'blur'},
		{
			pattern: /^.{18}$/,
			message: '法人代表身份证长度18位',
			trigger: "blur",
		},
	],
	liaison: [
		{required: true, message: '请输入联系人', trigger: 'blur'},
	],
	liaisonPhone: [
		{required: true, message: '请输入联系人电话', trigger: 'blur'},
		{
			pattern: /^[1][3-9][0-9]{9}$/,
			message: '请输入正确的联系人电话',
			trigger: "blur",
		},
	],
	liaisonEmail: [
		{required: true, message: '请输入联系人邮箱', trigger: 'blur'},
		{
			pattern: /^([a-zA-Z0-9_\-\.])+\@([a-zA-Z0-9_\-\.])+\.([A-Za-z]{2,4})$/,
			message: '请输入正确的邮箱',
			trigger: "blur",
		},
	],
	accountName: [
		{required: true, message: '请输入开户名', trigger: 'blur'},
	],
	taxNumber: [
		{required: true, message: '请输入税号', trigger: 'blur'},
		{
			pattern: /^.{19}$/,
			message: '税号长度19位',
			trigger: "blur",
		},
	],
	address: [
		{required: true, message: '请输入单位地址', trigger: 'blur'},
	],
	accountBank: [
		{required: true, message: '请输入开户银行', trigger: 'blur'},
	],
	bankAccount: [
		{required: true, message: '请输入银行账号', trigger: 'blur'},
		{validator: validateBankAccount, trigger: 'blur'},
	]
})

const submitForm = async (formEl: FormInstance | undefined) => {
	if (!formEl) return
	await formEl.validate((valid, fields) => {
		if (valid) {
			let obj = {...ruleForm}
			let formData: FormData = new FormData()
			for (const [key, value] of Object.entries(obj)) {
				formData.append(key, value)
			}
			// 处理要上传的文件
			if (fileList.value && fileList.value.length > 0) {
				fileList.value.filter((item: any) => item.raw !== undefined)
					.forEach((item: any) => formData.append("file", item.raw))
			}
			registerUser(formData).then((response: any) => {
				if (response.code === '00000') {
					ElMessage.success('注册成功！')
					router.push({
						path: '/index',
					});
				} else {
					ElMessage.error('注册失败！')
					resetForm(ruleFormRef.value)
				}
			}).catch(() => {
				ElMessage.error('注册失败！')
				resetForm(ruleFormRef.value)
			})
		}
	})
}

const resetForm = (formEl: FormInstance | undefined) => {
	if (!formEl) return
	formEl.resetFields()
	fileList.value = []
	imageUrl.value = ''
}

const imageUrl = ref<string>('')

const handleChange: UploadProps['onChange'] = (uploadFile: any, uploadFiles: any) => {
	const fileSuffix = uploadFile.name.substring(uploadFile.name.lastIndexOf(".") + 1);
	let uid = uploadFile.uid
	const whiteList = ["jpg", "png", "jpeg", "JPG", "PNG", "JPEG"];
	const isLt = uploadFile.size / 1024 / 1024 < 5;
	if (whiteList.indexOf(fileSuffix) === -1 || !isLt) {
		ElMessage.error('上传文件类型jpg 、png、jpeg 并且 文件大小不超过5M！');
		if (uploadFiles) {
			let index = uploadFiles.findIndex((item: any) => item.uid === uid);
			if (index !== -1) {
				uploadFiles.splice(index, 1)
				fileList.value = uploadFiles
			}
		}
	} else {
		if (uploadFile.raw) {
			const reader = new FileReader();
			reader.onload = (e: any) => {
				imageUrl.value = e.target.result
			};
			reader.readAsDataURL(uploadFile.raw);
		}
		fileList.value = uploadFiles
	}
}

const handleRemove = (uploadFile: any, uploadFiles: any) => {
	fileList.value = uploadFiles
	imageUrl.value = ''
}

const toPortal = () => {
	router.push("/dashboard");
}

const toLogin = () => {
	router.push({
		path: '/index',
	});
}
</script>


<style scoped lang="scss">
.register-page {
	width: 100%;
	height: 100vh;
	background: url("@/assets/images/login-bg.jpg") no-repeat center right;

	.header-title {
		margin: 0 0 3px 10px;
		font-size: 28px;
	}

	.form-card {
		margin: 0 auto;
		width: 90%;


		.left-image-blank {
			height: 100%;
			position: relative;

			.img-title {
				width: 260px;
				position: absolute;
				top: 100px;
				left: 50%;
				transform: translate(-50%, 0);
				font-size: 26px;
				color: white;
				font-weight: bold;
			}

			.img-sub-title {
				width: 310px;
				position: absolute;
				top: 160px;
				left: 50%;
				transform: translate(-50%, 0);
				font-size: 18px;
				color: white;
				font-weight: bold;
			}
		}

	}


	.avatar-uploader .el-upload {
		border: 1px dashed var(--el-border-color);
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
		transition: var(--el-transition-duration-fast);
	}

	.avatar-uploader .el-upload:hover {
		border-color: var(--el-color-primary);
	}

	.el-icon.avatar-uploader-icon {
		font-size: 28px;
		color: #8c939d;
		width: 178px;
		height: 178px;
		text-align: center;
		border: 1px solid #A3A6AD;
		border-radius: 8px;
	}
}
</style>