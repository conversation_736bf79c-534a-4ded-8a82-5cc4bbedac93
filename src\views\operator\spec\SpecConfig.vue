<template>
	<div>
		<el-card style="width: 100%;">
			<template #header>
				<el-text
					class="mx-1"
					size="large"
					line-clamp="1"
					tag="b"
				>
					{{ productTitle }}
				</el-text>
			</template>

			<el-container>
				<el-header>
					<el-form :model="queryForm" ref="queryFormRef" label-width="auto">
						<el-row :gutter="24">
							<el-col :span="6">
								<el-form-item label="产品大类" prop="type">
									<el-select
										v-model="queryForm.type"
										placeholder=""
										clearable
									>
										<el-option
											v-for="option in productClassOption"
											:key="option.value"
											:label="option.label"
											:value="option.value"
										/>
									</el-select>
								</el-form-item>
							</el-col>
							<el-col :span="8">
								<el-button type="primary" :icon="Search" @click="query(1)">{{ queryBtnText }}</el-button>
								<el-button plain :icon="RefreshLeft" @click="resetForm(queryFormRef)">{{ resetBtnText }}</el-button>
								<el-button :icon="Plus" type="primary" @click="add">{{ addBtnText }}</el-button>
							</el-col>
						</el-row>
					</el-form>
				</el-header>
				<el-main>
					<el-table
						v-loading="loading"
						:data="tableData"
						border
						height="450"
						style="width: 100%"
						tooltip-effect="dark"
					>
						<el-table-column
							prop="bigType"
							label="产品大类"
							align="center"
							:formatter="classFormatter"
						/>
						<el-table-column prop="spfJson" label="规格信息" align="center"/>
						<el-table-column prop="updateTime" label="更新时间" align="center"/>
						<el-table-column label="操作" align="center" width="200">
							<template #default="scope">
								<el-button link type='primary' @click="edit(scope.row)">{{ editBtnText }}</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-main>
				<el-footer>
					<el-pagination
						class="product-page"
						v-model:current-page="current"
						v-model:page-size="size"
						:page-sizes="[10, 50, 100, 200]"
						:small="false"
						:background="true"
						size="default"
						layout="total, sizes, prev, pager, next, jumper"
						:total="total"
						@size-change="handleSizeChange"
						@current-change="handleCurrentChange"
					/>
				</el-footer>
			</el-container>
		</el-card>
		<spec-dialog
			ref="specDialogRef"
			@query="query"
			:product-class-option="productClassOption"
		>
		</spec-dialog>
	</div>
</template>

<script setup lang="ts">
import {FormInstance} from "element-plus";
import SpecDialog from "@/views/operator/spec/components/SpecDialog.vue";
import {Specification} from "@/api/spec/types";
import {categoriesEnumQuery, specificationQuery} from "@/api/spec";
import {Plus, RefreshLeft, Search} from "@element-plus/icons-vue";
import {OperationTypeEnum} from "@/enums/OperationTypeEnum";

const productTitle = ref<string>('规格配置')
const queryBtnText = ref<string>('查询')
const addBtnText = ref<string>('新增规格')
const resetBtnText = ref<string>('重置')
const editBtnText = ref<string>('编辑')
const specDialogRef = ref()

const queryForm = reactive({
	type: '',
});

const tableData = ref<Array<Specification>>([]);

const loading = ref(false)
const current = ref<number>(1)
const size = ref<number>(10)
const total = ref<number>(0)

const handleSizeChange = (val: number) => {
	size.value = val
	query()
}
const handleCurrentChange = (val: number) => {
	current.value = val
	query(val)
}

const query = (index?: number) => {
	if (index) {
		current.value = index
	}
	let data = {
		type: queryForm.type,
		extra: {
			currentPage: current.value,
			pageSize: size.value,
		}
	}
	loading.value = true
	specificationQuery(data).then(response => {
		let pageData = response.data;
		if (pageData) {
			tableData.value = pageData.records
			current.value = pageData.current
			size.value = pageData.size
			total.value = pageData.total
		}
	}).finally(() => {
		loading.value = false
	})
}
const queryFormRef = ref<FormInstance>()

const resetForm = (formEl: FormInstance | undefined) => {
	if (!formEl) return
	formEl.resetFields()
}

const add = () => {
	specDialogRef.value.init(OperationTypeEnum.ADD, null)
}

const view = (rowData: Specification) => {
	specDialogRef.value.init(OperationTypeEnum.VIEW, rowData)
}

const edit = (rowData: Specification) => {
	specDialogRef.value.init(OperationTypeEnum.UPDATE, rowData)
}

const classFormatter = (row: any, column: any, cellValue: any, index: number) => {
	return productClassMap.value[cellValue]
}

const productClassOption = ref<Array<any>>([])
const productClassMap = ref<Object<any>>({})
const initProductClassOption = () => {
	categoriesEnumQuery().then(response => {
		let data = response.data
		if (data && data.length > 0) {
			data.forEach(item => {
				productClassMap.value[item.name] = item.value
				productClassOption.value.push({
					label: item.value,
					value: item.name
				})
			})
		}
	})
}

onMounted(() => {
	query()
	initProductClassOption()
})

</script>

<style scoped lang="scss">

.product-page {
	float: right;
	line-height: 60px;
}

</style>