export interface ProductDetail {
    productName?: string,
    productNumber?: string,
    productStatus?: number,
    shelfStatus?: number,
    productClass?: string,
    description?: string,
    isHot?: number,

    specTableData: SpecObj[],
    lineTableData: LineObj[],
    tableData?: [],

    duration?: string,
    discountType?: string,
    discountValue?: string,
    discountDescription?: string,
}

/**
 * 规格列表对象
 */
export interface SpecObj {
    specName?: string,
    spfName?: string,
    ratePlanAndSpfId?: string | null,
    vCPU?: string,
    memory?: string,
    systemDisk: string,
    dataDisk: string,
    GPU?: string,

    ratePlanName?: string,
    ratePlanType?: string,
    defaultValue?: string,
    price?: string,
    discount?: string,
}

/**
 * 线路列表对象
 */
export interface LineObj {
    specName?: string,
    spfName?: string,
    routeScheme?: string,
    machineRoom?: string,
    bandWidth?: string,
    domesticSegment?: string,
    overseasSegment?: string,
    DIA?: string,

    ratePlanName?: string,
    ratePlanType?: string,
    defaultValue?: string,
    discount?: string,
}

/**
 * 询价查询参数
 */
export interface InquiryQueryParams {
    productCnt?: number,
    billMethod?: string,
    effduration?: number,
    specCode?: string,
    productNumber?: string,
    specPrice?: string,
    linePrice?: string,
    specId?: string,
    lineSpecId?: string,
}

/**
 * 首页热门产品
 */
export interface IndexHotProduct {
    description?: string,
    imageBase64Code?: string,
    isHot?: string,
    productCatelog?: string,
    productClass?: string,
    productName?: string,
    price?: string,
    cycleType?: string,
    productNumber?: string,
    productStatus?: string,
}

export interface ProductTableInfo {
    productNumber?: string,
    productName?: string,
    description?: string,
    productClass?: number,
    productStatus?: number,
    shelfStatus?: number,
    updateTime?: string,
}

export interface ProductClassInfo {
    productClassName?: string,
    productClassValue?: string,
    updateTime?: Date,
}


export interface SpecificationQuery {
    type?: string,
    extra?: PageParams,

}
export interface PageParams {
    currentPage?: number,
    pageSize?: number,
    totalRow?: number,
}

export interface CategoriesAddReq {
    name?: string,
    value?: string,
}

export interface CategoriesUpdateReq {
    name?: string,
    value?: string,
}

export interface PmProductCateBean {
    id?: string,
    name?: string,
    value?: string,
    createTime?: Date,
    update_time?: Date,
}

export interface ProductSyncReq {
    productNumber?: string,
}

export interface SpfObj {
    id?: string,
    name?: string,
    isNeed?: boolean | string,
    defaultValue?: string,
}