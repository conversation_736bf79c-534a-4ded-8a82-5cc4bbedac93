<template>
	<div
		class="risk-summary-card"
		:style="{ background: riskColor === 1 ? 'var(--var-background-risk-color)' : '' }"
	>
		<div class="left">
			<img
				class="left-pic"
				:src="getAssetsFile(imgUrl)"
				alt="图片"
			/>
		</div>
		<div class="right">
			<div class="title">{{ title }}</div>
			<div class="count"
			     :style="{ color: riskColor === 1 ? '#EF8731' : '#0079FD' }"
			>
				{{ count }}
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import {getAssetsFile} from "@/utils/commonUtils";

const props = defineProps({
	imgUrl: {
		type: String,
		required: true,
	},
	title: {
		type: String,
		required: true,
	},
	count: {
		type: Number,
		required: true,
	},
	riskColor: {
		type: Number,
		required: true,
	}
});


</script>

<style scoped lang="scss">
.risk-summary-card {
	--var-background-risk-color: linear-gradient(293deg, #F9E1E1 0%, #FCF4F4 100%);
	--var-background-default-color: linear-gradient(293deg, #E1E7F9 0%, #F4F7FC 100%);

	display: flex;
	flex-direction: row;
	min-height: 80px;

	position: relative;
	overflow: hidden;
	box-shadow: 0 0 12px rgb(0,0,0,.12);
	border: none;

	opacity: 1;
	border-radius: 5.29px;
	background: var(--var-background-default-color);
	backdrop-filter: blur(8.82px);

	.left {
		width: 30%;
		display: flex;
		justify-content: center;
		align-items: center;

		.left-pic {
			width: 50px;
			height: 50px;
		}
	}

	.right {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: center;

		.title {
			font-size: 12px;
			font-weight: bold;
			color: #333;
			margin: 0;
		}

		--font-color: #e67e22;

		.count {
			opacity: 1;
			color: #0079FD;
			font-family: YouSheBiaoTiYuan;
			font-weight: 400;
			font-size: 25.26px;
			line-height: 35.47px;
			letter-spacing: 0;
			text-align: left;
		}
	}
}


/*.risk-summary-card::before {
	content: "";
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	border: 2px solid transparent;
	border-image: linear-gradient(135deg, transparent 40%, #f4b279) 1;
	border-radius: 5px;
	clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%, 0 20%, 20% 0);
	pointer-events: none;
}*/


</style>