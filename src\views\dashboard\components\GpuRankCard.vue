<template>
	<el-card>
		<div :id="id" :class="className" :style="{ height }"></div>
	</el-card>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';

const props = defineProps({
	id: {
		type: String,
		default: "barChart",
	},
	className: {
		type: String,
		default: "",
	},
	width: {
		type: String,
		default: "200px",
		required: false,
	},
	height: {
		type: String,
		default: "200px",
		required: true,
	},
	chartData: {
		type: Array<any>,
		required: true,
		default: []
	},

});

const chart = ref<any>("");

type EChartsOption = echarts.EChartsOption;

const initBar = () => {
	let chartDom = document.getElementById(props.id)!;
	chart.value = echarts.init(chartDom);
	let option: EChartsOption = {
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'shadow'
			}
		},
		legend: {},
		grid: {
			left: '3%',
			right: '4%',
			bottom: '3%',
			containLabel: true
		},
		yAxis: {
			type: 'category',
			data: []
		},
		xAxis: {
			type: 'value',
			boundaryGap: [0, 0.01]
		},
		series: [
			{
				label: {
					show: true,
					position: 'inside'
				},
				data: [],
				type: 'bar',
				barWidth: 45,//固定柱子宽度
			}
		]
	};
	if (props.chartData && props.chartData.length > 0) {
		let yData = []
		let xData = []
		props.chartData.forEach(item => {
			yData.push(item.gpuTypeName)
			xData.push(item.gpuFlops)
		})
		option.yAxis.data = yData
		option.series[0].data = xData
		chart.value.setOption(option);
		// 大小自适应
		window.addEventListener("resize", () => {
			chart.value.resize();
		});
	}
}

watch(() => props.chartData, (newVal, oldVal) => {
	if (newVal !== null) {
		initBar();
	}
}, {immediate: false, deep: true});

onActivated(() => {
	if (chart.value) {
		chart.value.resize();
	}
});
onMounted(() => {
	initBar()
})


</script>

<style scoped lang="scss">

</style>