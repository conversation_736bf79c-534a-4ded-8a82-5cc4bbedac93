<template>
	<div>
		<el-container>
			<el-main>
				<el-row :gutter="24">
					<el-col :offset="1" :span="12">
						<el-row :gutter="24">
							<el-col :span="24">
								<el-card style="width: 100%;">
									<template #header>
										<div class="card-header">
											<el-text
												class="mx-1"
												size="large"
												line-clamp="1"
												tag="b"
											>
												{{ recentTitle }}
											</el-text>
											<el-button type="primary" link @click="moreInfo">
												{{ moreInfoText }}
											</el-button>
										</div>
									</template>

									<el-row :gutter="24">
										<el-col :span="10" v-for="item in recentCardList">
											<product-order-card
												:src="getAssetsFile('product/recent_order.png')"
												:product-name="item.productName"
												:order-duration="item.effduration"
												:order-time="item.prodordTime"
											>
											</product-order-card>
										</el-col>
									</el-row>
								</el-card>
							</el-col>
						</el-row>

						<el-row :gutter="24" class="mt-5">
							<el-col :span="24">
								<el-card style="width: 100%;">
									<template #header>
										<div class="card-header">
											<el-text
												class="mx-1"
												size="large"
												line-clamp="1"
												tag="b"
											>
												{{ deadlineTitle }}
											</el-text>
											<el-button type="primary" link @click="moreInfo">
												{{ moreInfoText }}
											</el-button>
										</div>
									</template>

									<el-row :gutter="24">
										<el-col :span="10" v-for="item in expireCardList">
											<product-order-card
												:src="getAssetsFile('product/due.png')"
												:product-name="item.productName"
												:expiration-date="item.expTime"
											>
											</product-order-card>
										</el-col>
									</el-row>
								</el-card>
							</el-col>
						</el-row>
					</el-col>

					<el-col :span="9">
						<el-row :gutter="24">
							<el-col :span="24">
								<el-card style="width: 100%;">
									<el-row :gutter="24">
										<el-col :span="3">
											<el-avatar v-if="userStore.user.avatar" :size="50" :src="userStore.user.avatar"/>
											<el-avatar v-else :size="50" :src="getAssetsFile('default_avatar.png')"/>
										</el-col>
										<el-col :span="12">
											<el-text
												class="mx-1 user-name-content"
												size="large"
												line-clamp="1"
												tag="b"
											>
												{{ userNameText }}{{ userStore.user.nickname }}
											</el-text>
										</el-col>
									</el-row>
									<el-row :gutter="24" style="margin-top: 15px;" v-for="rowList in tableDataByRowList">
										<el-col :span="6" v-for="item in rowList">
											<el-button plain @click="toManagePage(item.url)">
												{{ item.name }}
											</el-button>
										</el-col>
									</el-row>
								</el-card>
							</el-col>
						</el-row>
					</el-col>
				</el-row>
			</el-main>
		</el-container>
	</div>
</template>

<script setup lang="ts">
import {useUserStore} from "@/store";
import {getAssetsFile, getNewArray} from "@/utils/commonUtils";
import ProductOrderCard from "@/views/consumer/center/components/ProductOrderCard.vue";
import {specialOrderQuery} from "@/api/user";

const router = useRouter();
const userStore = useUserStore();
const recentTitle = ref<string>('最近订购')
const deadlineTitle = ref<string>('即将到期')
const moreInfoText = ref<string>('更多')
const userNameText = ref<string>('用户名：')

const recentCardList = ref<Array<any>>([])
const expireCardList = ref<Array<any>>([])

const tableDataByRowList = ref()
const routerInfoList = ref([
	{
		url: '/consumer/order',
		name: '订单管理',
	},
	{
		url: '/consumer/bill',
		name: '账单管理',
	},
	{
		url: '/consumer/enterprise-info',
		name: '企业信息',
	},
])

const initRouterBtn = () => {
	tableDataByRowList.value = getNewArray(routerInfoList.value, 4)
}

const moreInfo = () => {
	toManagePage('/consumer/order')
}

const toManagePage = (path: string) => {
	if (path) {
		router.push(
			{
				path: path,
			}
		)
	}
}

const initOrderCard = (queryType: number) => {
	// queryType为1表示最近订购，为2表示即将过期。
	let data = {
		queryType: queryType,
	}
	specialOrderQuery(data).then(response => {
		let resData = response.data
		if (resData) {
			if (queryType === 1) {
				recentCardList.value = resData
			} else if (queryType === 2) {
				expireCardList.value = resData
			}
		}
	})
}

onMounted(() => {
	initOrderCard(1)
	initOrderCard(2)
	initRouterBtn()
})


</script>

<style scoped lang="scss">

.card-header {
	display: flex;
	justify-content: space-between;
}

.user-name-content {
	line-height: 56.5px;
}
</style>