<template>
	<div class="common-layout">
		<el-container>
			<el-header>
				<el-text
					class="mx-1"
					size="large"
					line-clamp="1"
					tag="b"
				>{{ headerTitle }} {{route.query.title}}
				</el-text>
			</el-header>
			<el-main>
				<div class="main-content">
					<el-image
						v-if="route.query.flag === 'success'"
						:src="getAssetsFile('product/activate_success.jpg')"
						class="main-pic"
						fit="fill"
					>
					</el-image>
					<el-image
						v-else
						:src="getAssetsFile('product/activate_error.jpg')"
						class="main-pic"
						fit="fill"
					>
					</el-image>
					<el-text
						class="mx-1 main-pic-text"
						size="large"
						line-clamp="1"
					>{{ route.query.flag === 'success' ? successMsg : errorMsg }}
					</el-text>
					<div class="button-row">
						<el-button plain @click="back">返回</el-button>
						<el-button v-if="route.query.flag === 'success'" type="danger" @click="toUse">去使用</el-button>
						<el-button v-if="route.query.flag === 'success'" type="primary" @click="viewOrder">查看订单</el-button>
					</div>
				</div>
			</el-main>
		</el-container>
	</div>
</template>

<script setup lang="ts">
import {getAssetsFile} from "@/utils/commonUtils";
import {useUserStore} from "@/store";
import {getDictOptions} from "@/api/dict";

const router = useRouter();
let route = useRoute()
const userStore = useUserStore();

const headerTitle = ref('【订购】')
const successMsg = ref('恭喜你开通成功')
const errorMsg = ref('开通失败，请联系客服处理！')

const back = () => {
	router.go(-1);
}

const toUseUrl = ref<string>('')

const toUse = () => {
	router.push({ path: '/external', query: { url: toUseUrl.value } });
}

const viewOrder = () => {
	let roles = userStore.user.roles;
	if (roles.includes('Operator')) {
		router.push({
			path: '/operator/op-order'
		});
	} else if (roles.includes('Customer')) {
		router.push(
			{
				path: '/consumer/order'
			}
		)
	}
}

const initToUseUrl = () => {
	getDictOptions('toUseUrl').then(response => {
		let respData = response.data
		if (respData && respData.length > 0) {
			let optionType = respData[0]
			toUseUrl.value = `${optionType.value}?username=${userStore.user.username}&password=${userStore.user.password}`
		}
	})
}

onMounted(() => {
	initToUseUrl()
})

</script>

<style scoped lang="scss">

.common-layout {
	.main-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;

		.main-pic {
			display: block;
			height: 240px;
			width: 240px;
		}

		.main-pic-text {
			display: block;
			margin-top: 15px;
		}
	}

	.button-row {
		margin-top: 15px;
	}
}

</style>