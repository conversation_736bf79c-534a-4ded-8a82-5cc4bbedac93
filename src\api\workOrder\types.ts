/**
 * 工单查询
 */
export interface WorkOrderQuery {
  workOrderId?: string;
  workOrderStatus?: number | null;
  createTime?: Date[] | null ;
}


export interface QueryData extends WorkOrderQuery {
  level: number;
  createTimeBegin?: string;
  createTimeEnd?: string;
  pageNum: number,
  pageSize: number,
}

export interface WorkOrder {
  workOrderId?: string;
  workOrderTopic?: string;
  workOrderContent?: string;
  workOrderStatus?: number;
  undealCount?: number;
}
