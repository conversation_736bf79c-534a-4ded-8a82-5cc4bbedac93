<template>
  <div class="logo-container">
    <transition enter-active-class="animate__animated animate__fadeInLeft">
      <router-link v-if="collapse" class="wh-full flex-center" to="/">
        <img v-if="settingsStore.sidebarLogo" :src="logo" class="logo-image" />
      </router-link>

      <router-link v-else class="wh-full flex-center" to="/">
	      <div class="icon-father">
		      <div class="logo-image white-background">
		      </div>
		      <svg-icon
			      size="20px"
			      v-if="settingsStore.sidebarLogo"
			      icon-class="chinamobile"
			      class="index-mobile-icon"
		      ></svg-icon>
	      </div>
        <span class="logo-title"> {{ defaultSettings.title }}</span>
      </router-link>
    </transition>
  </div>
</template>

<script lang="ts" setup>
import defaultSettings from "@/settings";
import { useSettingsStore } from "@/store";

const settingsStore = useSettingsStore();

defineProps({
  collapse: {
    type: Boolean,
    required: true,
  },
});

const logo = ref(new URL(`../../../../assets/icons/chinamobile.svg`, import.meta.url).href);
</script>

<style lang="scss" scoped>
.logo-container {
  width: 100%;
  height: $navbar-height;
  background-color: $sidebar-logo-background;

  .logo-image {
    width: 30px;
    height: 30px;
    background-color: white;
	border-radius: 50%;
  }

	.icon-father {
		position: relative;
		left: 5px;
		width: 20px;
		height: 20px;
	}

	.white-background {
		position: absolute;
		top: 3px;
		left: 3px;
		width: 15px;
		height: 15px;
	}

	.index-mobile-icon {
		width: 20px;
		height: 20px;
		z-index: 20;
		position: absolute;
	}

  .logo-title {
    flex-shrink: 0; /* 防止容器在空间不足时缩小 */
    margin-left: 10px;
    font-size: 14px;
    font-weight: bold;
    color: white;
  }
}

.layout-top,
.layout-mix {
  .logo-container {
    width: $sidebar-width;
  }

  &.hideSidebar {
    .logo-container {
      width: $sidebar-width-collapsed;
    }
  }
}
</style>
