import request from "@/utils/request";
import {AxiosResponse} from "axios";
import {
    RiskQuestionQuery,
    RiskSummaryQuery,
    RiskViewQuery,
    RiskWaringRecordListQuery,
    WorkOrderForm
} from "@/api/risk/types";

/**
 * 月份列表查询
 */
export function queryMonthStr(): Promise<AxiosResponse<any, any>> {
    return request.service({
        url: "/api/v1/risk/queryMonthStr",
        method: "post",
    });
}

/**
 * 大屏指标查询
 */
export function queryRiskView(riskViewQuery: RiskViewQuery): Promise<AxiosResponse<any, any>> {
    return request.service({
        url: "/api/v1/risk/queryRiskView",
        method: "post",
        data: riskViewQuery
    });
}

/**
 * 风险区域概要指标查询
 */
export function riskRegionSummaryQuery(data: RiskSummaryQuery): Promise<AxiosResponse<any, any>> {
    return request.service({
        url: "/api/v1/risk/riskRegionSummaryQuery",
        method: "post",
        data: data,
    });
}

/**
 * 风险预警工单整改查询
 * @param data 入参
 */
export function riskRegionSummaryRcfQuery(data: RiskSummaryQuery): Promise<AxiosResponse<any, any>> {
    return request.service({
        url: "/api/v1/risk/riskRegionSummaryRcfQuery",
        method: "post",
        data: data,
    });
}

/**
 * 风险工单列表分页查询
 */
export function riskRecordListQueryPage(params: RiskWaringRecordListQuery): Promise<AxiosResponse<any, any>> {
    return request.service({
        url: "/api/v1/risk/riskRecordListQueryPage",
        method: "post",
        data: params,
    });
}

/**
 * 撤销与批量撤销
 */
export function revokeWorkOrder(data: WorkOrderForm): Promise<AxiosResponse<any, any>> {
    return request.service({
        url: "/api/v1/risk/revokeWorkOrder",
        method: "post",
        data: data
    });
}

/**
 * 导出数据
 */
export function riskDataExport(data: WorkOrderForm): Promise<AxiosResponse<any, any>> {
    return request.service({
        url: "/api/v1/risk/riskDataExport",
        method: "post",
        data: data,
        responseType: "arraybuffer",
    });
}

/**
 * 预制问题查询
 *
 * @param data 入参
 */
export function queryRiskQuestion(data: RiskQuestionQuery): Promise<AxiosResponse<any, any>> {
    return request.service({
        url: "/api/v1/ebrps/ai/queryRiskQuestion",
        method: "post",
        data: data,
    });
}

/**
 * 区域列表查询
 */
export function queryRegionList(): Promise<AxiosResponse<any, any>> {
    return request.service({
        url: "/api/v1/risk/queryRegionList",
        method: "post"
    });
}
