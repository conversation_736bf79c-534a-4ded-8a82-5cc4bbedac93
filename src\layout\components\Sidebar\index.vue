<template>
  <div :class="{ 'has-logo': sidebarLogo }">
    <!--混合布局-->
    <div class="flex w-full" v-if="layout == 'mix'">
      <SidebarLogo v-if="sidebarLogo" :collapse="!appStore.sidebar.opened" />
      <SidebarMixTopMenu class="flex-1" />
      <NavbarRight />
    </div>
    <!--左侧布局 || 顶部布局 -->
    <template v-else>
      <SidebarLogo v-if="sidebarLogo" :collapse="!appStore.sidebar.opened" />
      <el-scrollbar>
        <SidebarMenu v-if="userStore.user.username == undefined" :menu-list="guest_routes" base-path="" />
        <SidebarMenu v-else :menu-list="permissionStore.routes" base-path="" />
      </el-scrollbar>
      <NavbarRight v-if="layout === 'top'" />
    </template>
  </div>
</template>

<script setup lang="ts">
import { useSettingsStore, usePermissionStore, useAppStore, useUserStore } from "@/store";

const appStore = useAppStore();
const settingsStore = useSettingsStore();
const permissionStore = usePermissionStore();
const userStore = useUserStore();
const guest_routes = JSON.parse('[\n' +
	'    {\n' +
	'        "path": "/",\n' +
	'        "name": "/",\n' +
	'        "redirect": "/dashboard",\n' +
	'        "children": [\n' +
	'            {\n' +
	'                "path": "dashboard",\n' +
	'                "name": "Dashboard",\n' +
	'                "meta": {\n' +
	'                    "title": "dashboard",\n' +
	'                    "icon": "homepage",\n' +
	'                    "affix": true,\n' +
	'                    "keepAlive": true,\n' +
	'                    "alwaysShow": false\n' +
	'                }\n' +
	'            }\n' +
	'        ]\n' +
	'    }\n' +
	']');

const sidebarLogo = computed(() => settingsStore.sidebarLogo);
const layout = computed(() => settingsStore.layout);
</script>

<style lang="scss" scoped>
.has-logo {
  .el-scrollbar {
    height: calc(100vh - $navbar-height);
  }
}
</style>
