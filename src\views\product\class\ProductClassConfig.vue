<template>
	<div>
		<el-card style="width: 100%;">
			<template #header>
				<el-text
					class="mx-1"
					size="large"
					line-clamp="1"
					tag="b"
				>
					{{ productTitle }}
				</el-text>
			</template>

			<el-container>
				<el-header>
					<el-form :model="queryForm" ref="queryFormRef" label-width="auto">
						<el-row :gutter="24">
							<el-col :span="6">
								<el-form-item prop="type">
									<el-input
										v-model="queryForm.type"
										placeholder="产品目录"
										clearable
									></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="8">
								<el-button type="primary" :icon="Search" @click="query(1)">{{ queryBtnText }}</el-button>
								<el-button plain :icon="RefreshLeft" @click="resetForm(queryFormRef)">{{ resetBtnText }}</el-button>
								<el-button :icon="Plus" type="primary" @click="add">{{ addBtnText }}</el-button>
							</el-col>
						</el-row>
					</el-form>
				</el-header>
				<el-main>
					<el-table
						v-loading="loading"
						:data="tableData"
						border
						height="450"
						style="width: 100%"
						tooltip-effect="dark"
					>
						<el-table-column prop="name" label="产品大类编码" align="center" />
						<el-table-column prop="value" label="产品大类名称" align="center" />
						<el-table-column prop="updateTime" label="更新时间" align="center" />
						<el-table-column label="操作" align="center" width="200">
							<template #default="scope">
								<el-button link type='primary' @click="edit(scope.row)">{{ editBtnText }}</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-main>
				<el-footer>
					<el-pagination
						class="product-page"
						v-model:current-page="current"
						v-model:page-size="size"
						:page-sizes="[10, 50, 100, 200]"
						:small="false"
						:background="true"
						size="default"
						layout="total, sizes, prev, pager, next, jumper"
						:total="total"
						@size-change="handleSizeChange"
						@current-change="handleCurrentChange"
					/>
				</el-footer>
			</el-container>
		</el-card>
	</div>
	<product-class-dialog
		ref="productClassDlgRef"
		@query="query"
	></product-class-dialog>
</template>

<script setup lang="ts">
import { FormInstance} from "element-plus";
import {PmProductCateBean, ProductClassInfo, SpecificationQuery} from "@/api/product/types";
import ProductClassDialog from "@/views/product/class/components/ProductClassDialog.vue";
import {categoriesQuery} from "@/api/product";
import {Plus, RefreshLeft, Search} from "@element-plus/icons-vue";

const productTitle = ref<string>('产品大类查询')
const addBtnText = ref<string>('新增产品大类')
const queryBtnText = ref<string>('查询')
const resetBtnText = ref<string>('重置')
const editBtnText = ref<string>('编辑')
const productClassDlgRef = ref()

const queryForm = reactive({
	type: '',
});

const tableData = ref<Array<PmProductCateBean>>([]);

const loading = ref(false)
const current = ref<number>(1)
const size = ref<number>(10)
const total = ref<number>(0)

const handleSizeChange = (val: number) => {
	size.value = val
	query()
}
const handleCurrentChange = (val: number) => {
	current.value = val
	query(val)
}

const query = (index?: number) => {
	if (index) {
		current.value = index
	}

	let data: SpecificationQuery = {
		type: queryForm.type,
		extra: {
			currentPage: current.value,
			pageSize: size.value
		}
	}
	loading.value = true
	categoriesQuery(data).then(response => {
		let pageData = response.data;
		if (pageData) {
			tableData.value = pageData.records
			current.value = pageData.current
			size.value = pageData.size
			total.value = pageData.total
		}
	}).finally(() => {
		loading.value = false
	})
}
const queryFormRef = ref<FormInstance>()

const resetForm = (formEl: FormInstance | undefined) => {
	if (!formEl) return
	formEl.resetFields()
}

const add = () => {
	productClassDlgRef.value.init('add', null)
}

const view = (rowData: ProductClassInfo) => {
	productClassDlgRef.value.init('view', rowData)
}

const edit = (rowData: ProductClassInfo) => {
	productClassDlgRef.value.init('update', rowData)
}

onMounted(() => {
	query()

})

</script>

<style scoped lang="scss">

.product-page {
	float: right;
	line-height: 60px;
}

</style>