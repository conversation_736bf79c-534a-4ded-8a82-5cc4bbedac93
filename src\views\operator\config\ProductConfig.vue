<template>
	<div>
		<el-card style="width: 100%;">
			<template #header>
				<el-text
					class="mx-1"
					size="large"
					line-clamp="1"
					tag="b"
				>
					{{ productTitle }}
				</el-text>
			</template>

			<el-container>
				<el-header>
					<el-form :model="queryForm" ref="queryFormRef" label-width="auto">
						<el-row :gutter="24">
							<el-col :span="6">
								<el-form-item label="产品名称" prop="productName">
									<el-input
										v-model="queryForm.productName"
										placeholder="请输入产品名称"
										clearable
									>
									</el-input>
								</el-form-item>
							</el-col>
							<el-col :span="8">
								<el-button type="primary" :icon="Search" @click="query(1)">{{ queryBtnText }}</el-button>
								<el-button plain :icon="RefreshLeft" @click="resetForm(queryFormRef)">{{ resetBtnText }}</el-button>
								<el-button
									type="primary"
									@click="add"
									:icon="Plus"
									v-hasPerm="['prod:config:add']"
								>
									{{ addBtnText }}
								</el-button>
							</el-col>
						</el-row>
					</el-form>
				</el-header>
				<el-main>
					<el-table
						v-loading="loading"
						:data="tableData"
						border
						height="450"
						style="width: 100%"
						tooltip-effect="dark"
					>
						<el-table-column prop="productNumber" label="产品编码" align="center" width="200"/>
						<el-table-column prop="productName" label="产品名称" align="center"/>
						<el-table-column prop="description" label="产品描述" show-overflow-tooltip align="center"/>
						<el-table-column prop="productClass" label="产品大类" align="center" :formatter="classFormatter"/>
						<el-table-column prop="productStatus" label="产品状态" align="center" :formatter="statusFormatter"/>
						<el-table-column
							prop="shelfStatus"
							v-hasPerm="['prod:config:upShelf']"
							label="上下架状态"
							align="center"
							:formatter="shelfStatusFormatter"
						/>
						<el-table-column prop="updateTime" label="更新时间" align="center"/>
						<el-table-column label="操作" align="center" width="200">
							<template #default="scope">
								<el-button link type='primary' @click="view(scope.row)">{{ viewBtnText }}</el-button>
								<el-button
									link
									type='primary'
									v-hasPerm="['prod:config:edit']"
									@click="edit(scope.row)"
								>
									{{ editBtnText }}
								</el-button>
								<el-button
									link
									type='primary'
									v-hasPerm="['prod:config:upShelf']"
									@click="upShelf(scope.row)"
									v-if="scope.row.shelfStatus == ShelfStatusEnum.PENDING || scope.row.shelfStatus == ShelfStatusEnum.REMOVED"
								>
									{{ upShelfBtnText }}
								</el-button>
								<el-button
									link
									type='primary'
									v-hasPerm="['prod:config:downShelf']"
									@click="downShelf(scope.row)"
									v-if="scope.row.shelfStatus == ShelfStatusEnum.LISTED"
								>
									{{ downShelfBtnText }}
								</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-main>
				<el-footer>
					<el-pagination
						class="product-page"
						v-model:current-page="current"
						v-model:page-size="size"
						:page-sizes="[10, 50, 100, 200]"
						:small="false"
						:background="true"
						size="default"
						layout="total, sizes, prev, pager, next, jumper"
						:total="total"
						@size-change="handleSizeChange"
						@current-change="handleCurrentChange"
					/>
				</el-footer>
			</el-container>
		</el-card>
		<product-detail
			ref="productDetailRef"
			@query="query"
			:product-class-option="productClassOption"
		>
		</product-detail>
	</div>
</template>

<script setup lang="ts">
import {FormInstance} from "element-plus";
import ProductDetail from "@/views/operator/config/components/ProductDetail.vue";
import {productExpire, productQuery, productSync} from "@/api/product";
import {initDictMap} from "@/utils/commonUtils";
import {ProductSyncReq, ProductTableInfo} from "@/api/product/types";
import {Plus, Search, RefreshLeft} from "@element-plus/icons-vue";
import {ShelfStatusEnum} from "@/enums/ShelfStatusEnum";
import {categoriesEnumQuery} from "@/api/spec";
import {OperationTypeEnum} from "@/enums/OperationTypeEnum";

const productTitle = ref<string>('产品查询')
const queryBtnText = ref<string>('查询')
const addBtnText = ref<string>('新增产品')
const resetBtnText = ref<string>('重置')
const viewBtnText = ref<string>('查看')
const editBtnText = ref<string>('编辑')
const upShelfBtnText = ref<string>('上架')
const downShelfBtnText = ref<string>('下架')
const productDetailRef = ref()

const queryForm = ref({
	productName: '',
});

const tableData = ref<Array<ProductTableInfo>>([]);

const loading = ref(false)
const current = ref<number>(1)
const size = ref<number>(10)
const total = ref<number>(0)

const handleSizeChange = (val: number) => {
	size.value = val
	query()
}
const handleCurrentChange = (val: number) => {
	current.value = val
	query(val)
}

const classFormatter = (row: any, column: any, cellValue: any, index: number) => {
	return productClassMap.value[cellValue]
}
const statusFormatter = (row: any, column: any, cellValue: any, index: number) => {
	return typeCodeMap.get('productStatus').value[cellValue]
}

const shelfStatusFormatter = (row: any, column: any, cellValue: any, index: number) => {
	return typeCodeMap.get('shelfStatus').value[cellValue]
}

const query = (index?: number) => {
	if (index) {
		current.value = index
	}

	let data = {
		pageNum: current.value,
		pageSize: size.value,
		productName: queryForm.value.productName
	}

	loading.value = true
	productQuery(data).then(response => {
		let pageData = response.data;
		if (pageData) {
			tableData.value = pageData.list
			/*current.value = pageData.current
			size.value = pageData.size*/
			total.value = pageData.total
		}
	}).finally(() => {
		loading.value = false
	})
}
const queryFormRef = ref<FormInstance>()

const resetForm = (formEl: FormInstance | undefined) => {
	if (!formEl) return
	formEl.resetFields()
}

const add = () => {
	productDetailRef.value.init(OperationTypeEnum.ADD, null)
}

const view = (rowData: ProductTableInfo) => {
	productDetailRef.value.init(OperationTypeEnum.VIEW, rowData)
}

const edit = (rowData: ProductTableInfo) => {
	productDetailRef.value.init(OperationTypeEnum.UPDATE, rowData)
}

/**
 * 上架
 */
const upShelf = (rowData: ProductTableInfo) => {
	let params: ProductSyncReq = {
		productNumber: rowData.productNumber
	}
	productSync(params).then(response => {
		if (response.code === '00000') {
			ElMessageBox.alert(`${rowData.productName}已成功上架！`, '产品上架', {
				confirmButtonText: '确定'
			})
		} else {
			ElMessageBox.alert(`${rowData.productName}上架失败！`, '产品上架', {
				confirmButtonText: '确定'
			})
		}
		query()
	})
}

/**
 * 下架
 */
const downShelf = (rowData: ProductTableInfo) => {
	let params: ProductSyncReq = {
		productNumber: rowData.productNumber
	}
	productExpire(params).then(response => {
		if (response.code === '00000') {
			ElMessageBox.alert(`${rowData.productName}已成功下架！`, '产品下架', {
				confirmButtonText: '确定'
			})
		} else {
			ElMessageBox.alert(`${rowData.productName}下架失败！`, '产品下架', {
				confirmButtonText: '确定'
			})
		}
		query()
	})
}

const typeCodeMap = reactive<Map<string, any>>(new Map([
	["productStatus", ref<object>({})],
	["shelfStatus", ref<object>({})],
]));

const productClassOption = ref<Array<any>>([])
const productClassMap = ref<Object<any>>({})
const initProductClassOption = () => {
	categoriesEnumQuery().then(response => {
		let data = response.data
		if (data && data.length > 0) {
			data.forEach(item => {
				productClassMap.value[item.name] = item.value
				productClassOption.value.push({
					label: item.value,
					value: item.name
				})
			})
		}
	})
}

onMounted(() => {
	query()
	initDictMap(typeCodeMap)
	initProductClassOption()
})

</script>

<style scoped lang="scss">

.product-page {
	float: right;
	line-height: 60px;
}

</style>