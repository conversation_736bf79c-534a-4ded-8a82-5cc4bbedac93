<template>
	<div class="warning-detail-main-dialog">
		<el-dialog
			v-model="dialogVisible"
			class="warning-detail-dialog"
			width="auto"
			:modal="false"
			:close-on-click-modal="true"
			:close-on-press-escape="true"
			:before-close="handleClose"
			:show-close="false"
			:style="dialogStyle"
			:draggable="false"
		>
			<div class="count-list">
				<div class="title-bar">
					{{ mainTitle }}{{ sum }}个
				</div>
				<div class="content-text">
					<span class="sub-title">ICT</span>
					<span class="cnt">{{ ictCount }}个</span>
				</div>
				<div class="content-text">
					<span class="sub-title">移动云</span>
					<span class="cnt">{{ cloudCount }}个</span>
				</div>
				<div class="content-text">
					<el-button link  @click="rectificationList" v-if="mapBtnFlag === 1">
						<span class="sub-title" style="margin-left: 0;color: #409EFF;">待整改</span>
					</el-button>
					<span v-if="mapBtnFlag === 1" class="cnt">{{ pendingRectificationCount }}个</span>
					<span v-if="mapBtnFlag === 2" class="sub-title" style="margin-left: 0.05rem;">整改完成率</span>
					<span v-if="mapBtnFlag === 2" class="cnt">{{ completionRate }}%</span>
				</div>
				<el-button link @click="viewDetail" style="padding: 0;" v-if="mapBtnFlag === 1">
					<span class="detail-text">查看风险详情>></span>
				</el-button>
				<el-button link @click="rectificationDetail" style="padding: 0;" v-if="mapBtnFlag === 2">
					<span class="detail-text">查看整改详情>></span>
				</el-button>
			</div>

		</el-dialog>
	</div>
</template>

<script setup lang="ts">

import {useRouter} from "vue-router";

const props = defineProps({
	mainTitle: {
		type: String,
		default: '',
		required: true,
	},
	sum: {
		type: String,
		default: '0',
		required: true,
	},
	ictCount: {
		type: String,
		default: 0,
		required: true,
	},
	cloudCount: {
		type: String,
		default: 0,
		required: true,
	},
	// 待整改
	pendingRectificationCount: {
		type: String,
		default: 0,
		required: true,
	},
	// 整改完成率
	completionRate: {
		type: String,
		default: 0,
		required: true,
	},
	// 1：按风险预警单数统计 2：按整改情况统计
	mapBtnFlag: {
		type: Number,
		default: 0,
		required: true,
	},
	riskMonth: {
		type: String,
		default: '',
		required: true,
	},
	regionLevel: {
		type: Number,
		default: 1,
		required: true,
	},
	provinceName: {
		type: String,
		default: '',
		required: true,
	},
})

const dialogVisible = ref<boolean>(false)
const tableData = ref([])
const opt = ref<string>('')

const clickPosition = ref({x: 0, y: 0});

// 鼠标点击坐标偏移量（根据实际情况调整）
const dialogOffset = 20;

const dialogStyle = ref({
	left: '0px',
	top: '0px'
});

const init = (optType: string, data: any) => {
	// 获取点击位置
	clickPosition.value = {
		x: event.clientX,
		y: event.clientY
	};

	// 设置对话框位置
	dialogStyle.value = {
		position: 'fixed',
		left: clickPosition.value.x + dialogOffset + 'px',
		top: clickPosition.value.y + dialogOffset + 'px',
		margin: '0', // 清除默认居中样式
		transform: 'none' // 禁用默认的 transform 居中
	};

	dialogVisible.value = true
	opt.value = optType
	switch (optType) {
		case 'view':
			break
	}
}

const router = useRouter();

// 查看整改列表
const rectificationList = () => {
	router.push({
		path: '/correction',
		state: {
			riskMonth: props.riskMonth,
			regionLevel: props.regionLevel,
			provinceName: props.provinceName,
		}
	})
}

// 查看整改详情
const rectificationDetail = () => {
	router.push({
		path: '/summary-rectification',
		state: {
			riskMonth: props.riskMonth,
			regionLevel: props.regionLevel,
			provinceName: props.provinceName,
		}
	})
}

// 查看风险详情
const viewDetail = () => {
	router.push({
		path: '/risk-detail',
		state: {
			riskMonth: props.riskMonth,
			regionLevel: props.regionLevel,
			provinceName: props.provinceName,
		}
	})
}

const handleClose = (done: () => void) => {
	dialogVisible.value = false
	tableData.value = []
	done()
}

const back = () => {
	dialogVisible.value = false
	tableData.value = []
}

defineExpose({
	init
})

</script>

<style scoped lang="scss">

.warning-detail-main-dialog {
	/* 覆盖默认样式 */
	:deep(.el-dialog) {
		position: fixed !important;
		height: 2.29rem;
		opacity: 1;
		border-radius: 8px;
		background: #2C467E7F;
		backdrop-filter: blur(27.33px);
		box-shadow: 0 0 12px rgb(0, 0, 0, .12);
		padding: 0;
	}

	:deep(.el-dialog__header) {
		padding: 0;
	}


	.warning-detail-dialog {

		.count-list {
			display: flex;
			flex-direction: column;
			align-items: center;
			margin-top: 0.1rem;

			.title-bar {
				opacity: 1;
				color: #FFFFFF;
				font-family: PingFang SC;
				font-weight: bold;
				font-size: 0.18rem;
				line-height: 0.4rem;
				letter-spacing: 0;
				text-align: left;
				white-space: nowrap;
				margin: 0 0.2rem;
			}

			.content-text {
				display: flex;
				flex-direction: row;
				justify-content: space-between;
				background: #FFFFFF;
				border-radius: 0.04rem;
				width: 2rem;
				height: 0.4rem;
				align-items: center;

				&:not(:first-child) {
					margin-top: 0.04rem;
				}

				.sub-title {
					opacity: 1;
					color: #4E5969;
					font-family: PingFang SC;
					font-weight: 400;
					font-size: 0.18rem;
					line-height: 0.15rem;
					letter-spacing: 0;
					margin-left: 0.05rem;
				}

				.sub-title::before {
					content: '';
					display: inline-block;
					width: 0.08rem;
					height: 0.08rem;
					background-color: #409EFF;
					border-radius: 50%;
					margin-right: 0.06rem;
					vertical-align: middle;
				}

				.cnt {
					opacity: 1;
					color: #0079FD;
					font-family: PingFang SC;
					font-weight: 500;
					font-size: 0.18rem;
					line-height: 0.11rem;
					letter-spacing: 0;
					margin-right: 0.05rem;
				}
			}


			.detail-text {
				opacity: 1;
				color: #FFFFFF;
				font-family: PingFang SC;
				font-weight: bold;
				font-size: 0.18rem;
				line-height: 0.4rem;
				letter-spacing: 0;
				margin-top: 0.05rem;

			}
		}
	}
}


</style>
