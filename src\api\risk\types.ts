export interface RiskViewQuery {
    level?: string,
    monthStr?: string,
    regionCode?: string,
    regionName?: string,
}

export interface RiskViewVo {
    regionLevel?: string;
    regionCode?: string;
    regionName?: string;
    monthRecordNumber?: string;
    allRecordNumber?: string;
    allCorrectionRecordNumber?: string;
    allCorrectionRecordRate?: string;
    regionStatisticsList?: RegionStatisticsModel[];
    riskScaleOfImpactModel?: RiskScaleOfImpactModel;
    riskScenarioStatisticsModel?: RiskScenarioStatisticsModel;
    reportTrendModel?: RiskTrendModel;
    topRiskModel?: TopRiskModel;
    riskWarningRecordStatisticsModel?: RiskWarningRecordStatisticsModel;
    statusOfCorrectiveForRiskWarningRecordModel?: StatusOfCorrectiveForRiskWarningRecordModel;
}


export interface RegionStatisticsModel {
    regionName?: string;
    regionMonthRecordNumber?: string;
    regionIctRecordNumber?: string;
    regionMcRecordNumber?: string;
    regionPdRcfRecordNumber?: string;
    regionMonthRcfRecordNumber?: string;
    regionIctRcfRecordNumber?: string;
    regionMcRcfRecordNumber?: string;
    regionMonthRcfRate?: string;
}

export interface RiskScaleOfImpactModel {
    ictRecordNumber?: string;
    ictProjectNumber?: string;
    ictProjectMoney?: string;
    mcRecordNumber?: string;
    mcCustomerNumber?: string;
    mcProjectNumber?: string;
    mcProjectMoney?: string;
}

export interface RiskScenarioStatisticsModel {
    ictABRecordNumber?: string;
    ictUpDownRecordNumber?: string;
    mcIncomeRecordNumber?: string;
    mcOrderRecordNumber?: string;
    ictABProjectMoney?: string;
    ictUpDownProjectMoney?: string;
    mcIncomeProjectMoney?: string;
    mcOrderProjectMoney?: string;
}

export interface RiskTrendModel {
    riskTrendObjectList?: RiskTrendObject[];
}

export interface RiskTrendObject {
    index?: string;
    monthStr?: string;
    ictABRecordNumber?: string;
    ictUpDownRecordNumber?: string;
    mcIncomeRecordNumber?: string;
    mcOrderRecordNumber?: string;
    ictABProjectMoney?: string;
    ictUpDownProjectMoney?: string;
    mcIncomeProjectMoney?: string;
    mcOrderProjectMoney?: string;
}

export interface RiskWarningRecordStatisticsModel {
    allRecordNumber?: string;
    firstLevelRecordNumber?: string;
    secondLevelRecordNumber?: string;
    thirdLevelRecordNumber?: string;
    allMoMRecordNumber?: string;
    firstLevelMoMNumber?: string;
    secondLevelMoMNumber?: string;
    thirdLevelMoMNumber?: string;
    riskWarningRecordObjectList?: RiskWarningRecordObject[];
}

export interface RiskWarningRecordObject {
    index?: string;
    monthStr?: string;
    firstLevelRecordNumber?: string;
    secondLevelRecordNumber?: string;
    thirdLevelRecordNumber?: string;
    allMoMRecordNumber?: string;
}

export interface StatusOfCorrectiveForRiskWarningRecordModel {
    onTimeRateModel?: OnTimeRate;
    overTimeRateModel?: OverTimeRate;
}

export interface OnTimeRate {
    allOnTimeRate?: string;
    firstLevelOnTimeRate?: string;
    secondLevelOnTimeRate?: string;
    thirdLevelOnTimeRate?: string;
    regionOnTimeRateObjectList?: RegionOnTimeRateObject[];
}

export interface OverTimeRate {
    allOverTimeRate?: string;
    firstLevelOverTimeRate?: string;
    secondLevelOverTimeRate?: string;
    thirdLevelOverTimeRate?: string;
    regionOverTimeRateObjectList?: RegionOverTimeRateObject[];
}

export interface RegionOnTimeRateObject {
    UnitName?: string;
    onTimeNumber?: string;
    firstLevelOnTimeNumber?: string;
    secondLevelOnTimeNumber?: string;
    thirdLevelOnTimeNumber?: string;
}

export interface RegionOverTimeRateObject {
    UnitName?: string;
    regionOverTimeRate?: string;
}

export interface TopRiskModel {
    topTitleStr?: string;
    topOneRiskObject?: TopRiskObject;
    topTwoRiskObject?: TopRiskObject;
    topThreeRiskObject?: TopRiskObject;
    topFourRiskObject?: TopRiskObject;
    topFiveRiskObject?: TopRiskObject;
}

export interface TopRiskObject {
    regionName?: string;
    firstLevelRecordNumber?: string;
    firstLevelIctRecordNumber?: string;
    firstLevelMcRecordNumber?: string;
    firstLevelPdRcfRecordNumber?: string;
    secondLevelRecordNumber?: string;
    secondLevelIctRecordNumber?: string;
    secondLevelMcRecordNumber?: string;
    secondLevelPdRcfRecordNumber?: string;
    thirdLevelRecordNumber?: string;
    thirdLevelIctRecordNumber?: string;
    thirdLevelMcRecordNumber?: string;
    thirdLevelPdRcfRecordNumber?: string;
}

export interface RiskWaringRecordListQuery {
    /**
     * 风险预警描述，模糊匹配
     */
    riskDesc?: string;

    /**
     * 风险业务类型
     * ICT业务，移动云业务
     */
    riskBusinessType?: string;

    /**
     * 风险等级
     * 一级预警；二级预警；三级预警
     */
    riskLevel?: string;

    status?: string | null | number;

    /**
     * 业务数据时间
     */
    businessDataTime?: string;

    /**
     * 省份编码
     */
    provinceCode?: string;

    regionName?: string;

    regionCode?: string;

    /**
     * 地市编码
     */
    cityCode?: string;

    /**
     * 区县编码
     */
    districtCode?: string;

    pageNum?: number;

    pageSize?: number;

    warningDate?: [];
}

export interface WorkOrderForm {
    /**
     * 工单编码（主键）
     */
    recordId?: string;

    /**
     * 风险业务类型
     */
    riskBusinessType?: string;

    /**
     * 风险等级
     */
    riskLevel?: string;

    /**
     * 工单编码数组（必填）
     */
    recordIds: string[];
}

export interface RiskSummaryQuery {

    startDate?: string;

    endDate?: string;

    /**
     * 省份编码
     */
    regionCode?: string;

    /**
     * 区域名称
     */
    regionName?: string;

    /**
     * 区域名称
     */
   regionLevel?: string;
}

export interface RiskQuestionQuery {
    monthStr?: string;
}

