<!--  线 + 柱混合图 -->
<template>
	<el-card>
		<template #header>
			<div class="title">
				<el-text
					class="mx-1"
					size="large"
					line-clamp="1"
					tag="b"
				>
					费用趋势（¥）
				</el-text>
				<el-tooltip effect="dark" content="点击试试下载" placement="bottom">
					<i-ep-download class="download" @click="downloadEchart"/>
				</el-tooltip>
			</div>
		</template>

		<div :id="id" :class="className" :style="{ height, width }"></div>
	</el-card>
</template>

<script setup lang="ts">
import * as echarts from "echarts";
import {getNumberOfDigits} from "@/utils/commonUtils";

const props = defineProps({
	id: {
		type: String,
		default: "barChart",
	},
	className: {
		type: String,
		default: "",
	},
	width: {
		type: String,
		default: "200px",
		required: true,
	},
	height: {
		type: String,
		default: "200px",
		required: true,
	},
	chartData: {
		type: Array<Object>,
		default: [],
		required: true,
	},
});

const xData = ref<Array<string>>([]);
const yData = ref<Array<number>>([]);

const yMaxValue = ref<number>(10000);
const yInterval = ref<number>(2000);

const initChartData = () => {
	// 图表初始化
	chart.value = markRaw(
		echarts.init(document.getElementById(props.id) as HTMLDivElement)
	);
	if (props.chartData && props.chartData.length > 0) {
		props.chartData.forEach(item => {
			xData.value.push(item.billMonth)
			yData.value.push(item.totalFeeAfterDis.toFixed(2))
		})
		if (yData.value.length > 0) {
			let max = Math.max(...yData.value)
			let integerNumber = Math.trunc(max);
			let digits = getNumberOfDigits(integerNumber);
			if (digits !== 0) {
				let number = Math.pow(10, digits);
				yMaxValue.value = number
				yInterval.value = number / 5
			}
		}
		options.yAxis[0].max = yMaxValue.value
		options.yAxis[0].interval = yInterval.value
		chart.value.setOption(options);
		// 大小自适应
		window.addEventListener("resize", () => {
			chart.value.resize();
		});
	}
}

const options = {
	grid: {
		left: "2%",
		right: "2%",
		bottom: "10%",
		containLabel: true,
	},
	tooltip: {
		trigger: "axis",
		axisPointer: {
			type: "cross",
			crossStyle: {
				color: "#999",
			},
		},
	},
	legend: {
		x: "center",
		y: "bottom",
		data: ["月份"],
		textStyle: {
			color: "#999",
		},
	},
	xAxis: [
		{
			type: "category",
			data: xData.value,
			axisPointer: {
				type: "shadow",
			},
		},
	],
	yAxis: [
		{
			type: "value",
			min: 0,
			max: 10000,
			interval: 2000,
			axisLabel: {
				formatter: "{value} ",
			},
		},
		{
			type: "value",
			min: 0,
			max: 100,
			interval: 20,
			axisLabel: {
				formatter: "{value}%",
			},
		},
	],
	series: [
		{
			name: "月份",
			type: "bar",
			data: yData.value,
			barWidth: 80,
			label: {
				formatter: '{c}' + '元',
				show: true,
				position: 'top'
			},
			itemStyle: {
				color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
					{offset: 0, color: "#83bff6"},
					{offset: 0.5, color: "#188df0"},
					{offset: 1, color: "#188df0"},
				]),
			},
		},
	],
};

const downloadEchart = () => {
	// 获取画布图表地址信息
	const img = new Image();
	img.src = chart.value.getDataURL({
		type: "png",
		pixelRatio: 1,
		backgroundColor: "#fff",
	});
	// 当图片加载完成后，生成 URL 并下载
	img.onload = () => {
		const canvas = document.createElement("canvas");
		canvas.width = img.width;
		canvas.height = img.height;
		const ctx = canvas.getContext("2d");
		if (ctx) {
			ctx.drawImage(img, 0, 0, img.width, img.height);
			const link = document.createElement("a");
			link.download = `柱状图.png`;
			link.href = canvas.toDataURL("image/png", 0.9);
			document.body.appendChild(link);
			link.click();
			link.remove();
		}
	};
};

watch(() => props.chartData, (newVal, oldVal) => {
	if (newVal !== null) {
		initChartData();
	}
}, {immediate: false, deep: true});

const chart = ref<any>("");
onMounted(() => {
	// 图表初始化
	initChartData()
});

onActivated(() => {
	if (chart.value) {
		chart.value.resize();
	}
});
</script>
<style lang="scss" scoped>
.title {
	display: flex;
	justify-content: space-between;

	.download {
		cursor: pointer;

		&:hover {
			color: #409eff;
		}
	}
}
</style>
