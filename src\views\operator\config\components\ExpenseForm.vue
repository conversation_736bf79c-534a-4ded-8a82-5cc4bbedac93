<template>
	<el-row :gutter="24">
		<el-col :span="24">
			<el-form-item label="资费名称" prop="ratePlanName">
				<el-input v-model="form.ratePlanName" clearable placeholder="请输入资费名称"></el-input>
			</el-form-item>
		</el-col>
	</el-row>

	<el-row :gutter="24">
		<el-col :span="12">
			<el-form-item label="计费方式" prop="ratePlanType">
				<dictionary
					:clearable="false"
					v-model="form.ratePlanType"
					type-code="billMethod"
					placeholder="请输入计费方式"
				></dictionary>
			</el-form-item>
		</el-col>
	</el-row>

	<el-row :gutter="24">
		<el-col :span="12">
			<el-form-item label="目录价（元）" prop="price">
				<el-input v-model="form.price" clearable placeholder="请输入目录价"></el-input>
			</el-form-item>
		</el-col>
	</el-row>

	<el-row :gutter="24">
		<el-col :span="12">
			<el-form-item label="折扣（%）" prop="discount">
				<el-input v-model="form.discount" clearable placeholder="请输入折扣"></el-input>
			</el-form-item>
		</el-col>
	</el-row>
</template>

<script setup lang="ts">
const props = defineProps({
	form: {
		type: Object,
		require: true,
		default: "",
	},
})

</script>

<style scoped lang="scss">

</style>